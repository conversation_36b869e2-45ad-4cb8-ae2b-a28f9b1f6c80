# Zephyr v6 - Web Chat Client

A modern, feature-rich web chat application built with Electron and Python. Connect to chat rooms, send real-time messages, and enjoy a beautiful, responsive interface.

## Features

### 🚀 Core Features
- **Real-time messaging** - Instant message delivery using WebSocket connections
- **Multiple chat rooms** - Join different rooms for various topics
- **User avatars** - Colorful, auto-generated user avatars
- **Typing indicators** - See when other users are typing
- **Message formatting** - Support for mentions, links, bold, and italic text
- **Context menus** - Right-click messages for additional options

### 🎨 Interface Features
- **Modern UI** - Beautiful gradient design with glassmorphism effects
- **Responsive design** - Works on different screen sizes
- **Dark theme** - Easy on the eyes for long chat sessions
- **Smooth animations** - Polished user experience with CSS transitions
- **Toast notifications** - Non-intrusive status updates

### 🛠 Technical Features
- **Cross-platform** - Runs on Windows, macOS, and Linux
- **WebSocket support** - Real-time bidirectional communication
- **Message persistence** - Chat history and user preferences saved locally
- **Error handling** - Graceful handling of connection issues
- **Extensible architecture** - Easy to add new features and integrations

## Quick Start

### Prerequisites
- **Node.js** (v16 or higher)
- **Python** (v3.8 or higher)
- **npm** or **yarn**

### Installation

1. **Clone or download** the project
2. **Install dependencies**:
   ```bash
npm install
   pip install -r requirements.txt
```
3. **Start the application**:
   ```bash
npm start
```

### First Time Setup

1. **Enter your username** - Choose a unique username (letters, numbers, _ and - only)
2. **Select a room** - Enter a room ID or use quick join buttons
3. **Start chatting** - Send messages and interact with other users

## Usage Guide

### Joining a Chat Room

1. **Username**: Enter your desired username in the sidebar
2. **Room ID**: Enter a room name (e.g., "general", "tech", "gaming")
3. **Quick Join**: Use the preset room buttons for popular rooms
4. **Connect**: Click "Join Room" to connect

### Sending Messages

- **Type your message** in the input field at the bottom
- **Press Enter** or click the send button
- **Format text** using:
  - `@username` for mentions
  - `**bold text**` for bold
  - `*italic text*` for italic
  - URLs are automatically linked

### Message Features

- **Right-click messages** to access context menu options:
  - Copy message content
  - Copy username
  - Mention user
- **Scroll through history** - All messages are preserved during your session
- **Clear chat** - Use the clear button to remove all messages

### Room Management

- **Leave room** - Click the "Leave" button to disconnect
- **Switch rooms** - Leave current room and join a new one
- **Room info** - See current room name in the header

## Architecture

### Frontend (Electron + HTML/CSS/JS)
- **Electron main process** - Window management and system integration
- **Renderer process** - Chat interface and user interactions
- **IPC communication** - Secure communication between processes

### Backend (Python)
- **WebSocket client** - Real-time message handling
- **HTTP API integration** - User authentication and data fetching
- **Message processing** - Content formatting and validation
- **Simulated responses** - Demo bot users for testing

### Data Flow
1. **User input** → Frontend validation → IPC to main process
2. **Main process** → Python backend via stdin/stdout
3. **Python backend** → WebSocket server → Message processing
4. **Responses** → Python backend → Main process → Frontend display

## Configuration

### User Data Storage
- **Username and room** preferences are saved locally
- **Data file**: `userdata.txt` in the application directory
- **Format**: `username|roomid`

### Backend Configuration
- **WebSocket server**: Currently uses echo.websocket.org for demo
- **API endpoints**: Uses JSONPlaceholder for mock data
- **Message simulation**: Built-in bot responses for testing

## Development

### Project Structure
```
├── main.js                 # Electron main process
├── renderer/
│   ├── index.html          # Chat interface
│   ├── styles.css          # UI styling
│   └── renderer.js         # Frontend logic
├── backend/
│   └── web_chat_client.py  # Python backend
├── assets/
│   └── icon.png           # Application icon
└── package.json           # Node.js dependencies
```

### Adding Features

1. **Frontend features**: Modify `renderer/renderer.js` and update UI in `index.html`
2. **Backend features**: Extend `backend/web_chat_client.py`
3. **Styling**: Update `renderer/styles.css` for visual changes
4. **IPC communication**: Add new handlers in `main.js`

### Building for Production

```bash
npm run build
```

This creates distributable packages in the `dist/` directory.

## Customization

### Themes
- Modify CSS variables in `styles.css` to change colors
- Update gradient backgrounds for different visual styles
- Customize avatar colors and message styling

### Chat Features
- Add emoji support by extending message formatting
- Implement file sharing by adding attachment handling
- Create custom commands by extending message processing

### Integration
- Replace WebSocket server with your own chat backend
- Add authentication with real user accounts
- Integrate with existing chat services or APIs

## Troubleshooting

### Common Issues

**Connection Problems**:
- Check internet connection
- Verify WebSocket server availability
- Review console logs for error messages

**Message Display Issues**:
- Clear chat and reconnect
- Check browser console for JavaScript errors
- Verify message formatting

**Performance Issues**:
- Clear chat history if too many messages
- Restart application
- Check system resources

### Debug Mode
Run with debug logging:
```bash
npm run dev
```

### Logs
- **Main process logs**: Check terminal/command prompt
- **Renderer logs**: Open DevTools (Ctrl+Shift+I)
- **Python logs**: Check stderr output

## Contributing

1. **Fork the repository**
2. **Create a feature branch**
3. **Make your changes**
4. **Test thoroughly**
5. **Submit a pull request**

## License

MIT License - see LICENSE file for details.

## Support

For issues, questions, or feature requests:
- Check the troubleshooting section
- Review console logs for error details
- Create an issue with detailed information

---

**Zephyr v6 Web Chat** - Modern chat application for the modern web.ing
- Create custom commands by extending message processing

### Integration
- Replace WebSocket server with your own chat backend
- Add authentication with real user accounts
- Integrate with existing chat services or APIs

## Troubleshooting

### Common Issues

**Connection Problems**:
- Check internet connection
- Verify WebSocket server availability
- Review console logs for error messages

**Message Display Issues**:
- Clear chat and reconnect
- Check browser console for JavaScript errors
- Verify message formatting

**Performance Issues**:
- Clear chat history if too many messages
- Restart application
- Check system resources

### Debug Mode
Run with debug logging:
```bash
npm run dev
```

### Logs
- **Main process logs**: Check terminal/command prompt
- **Renderer logs**: Open DevTools (Ctrl+Shift+I)
- **Python logs**: Check stderr output

## Contributing

1. **Fork the repository**
2. **Create a feature branch**
3. **Make your changes**
4. **Test thoroughly**
5. **Submit a pull request**

## License

MIT License - see LICENSE file for details.

## Support

For issues, questions, or feature requests:
- Check the troubleshooting section
- Review console logs for error details
- Create an issue with detailed information

---

**Zephyr v6 Web Chat** - Modern chat application for the modern web.