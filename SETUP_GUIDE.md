# Zephyr v6 - Quick Setup Guide

## 🚀 Quick Start

1. **Run the application:**
   ```bash
   npm start
   ```
   Or double-click `start.bat`

2. **Get your Discord user token:**
   - Open Discord in browser
   - Press F12 → Network tab
   - Send any message
   - Find request to `/api/v*/messages`
   - Copy the "Authorization" header value

3. **Configure:**
   - Paste your token in the app
   - Enter a channel ID
   - Click "Connect"

## ✨ Features Overview

### Message Queue System
- Messages are queued before sending
- Visual queue counter shows pending messages
- Failed messages automatically retry
- Rate limit handling with countdown timer

### Real-time Chat
- Live message display with avatars
- User display names and role colors
- Support for threads and polls
- Typing indicators

### Context Menu
- Right-click messages to copy mentions
- Copy user IDs for advanced usage
- Easy mention copying for replies

### Rate Limiting
- Smart rate limit detection
- Visual countdown when rate limited
- Automatic retry with proper delays
- No risk of IP bans

## 🔧 File Structure

```
🖤 ZEPHYR ELECTRON/
├── main.js              # Electron main process
├── maintoken.txt        # Your token (auto-created)
├── start.bat           # Easy startup script
├── renderer/
│   ├── index.html      # Main interface
│   ├── styles.css      # Dark theme styling
│   └── renderer.js     # Frontend logic
└── backend/
    └── discord_client.py # Discord API handler
```

## ⚠️ Important Notes

- **User tokens are sensitive** - never share them
- Using user tokens may violate Discord's ToS
- This is for educational purposes only
- Keep your `maintoken.txt` file secure

## 🎯 How to Use

1. **First Time Setup:**
   - Get your user token (see above)
   - Enter token and save it
   - Enter channel ID and connect

2. **Sending Messages:**
   - Type in the message box
   - Press Enter or click send
   - Watch the queue counter

3. **Queue Management:**
   - View queued messages in the panel
   - Failed messages show in red
   - Successful messages disappear from queue

4. **Rate Limiting:**
   - App automatically handles rate limits
   - Shows countdown when limited
   - Messages wait and retry automatically

## 🛠️ Troubleshooting

- **Token issues:** Make sure you copied the full token
- **Channel access:** Ensure you can see the channel normally
- **Python errors:** Make sure Python 3.8+ is installed
- **Connection fails:** Check your internet connection

## 🎨 Interface Guide

- **Header:** Shows connection status and queue count
- **Sidebar:** Token input, channel ID, rate limit info
- **Main Chat:** Live messages with avatars and names
- **Input Area:** Type and send messages
- **Queue Panel:** Shows pending/failed messages

Enjoy using Zephyr v6! 🖤⚡