# Debug patch for discord_client.py
# Add this code to the handle_message_create function right after the 
# content processing

def debug_message_content(data):
    """Debug function to analyze message content"""
    import json
    import logging
    
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 50)
    logger.info("🔍 DEBUGGING MESSAGE CONTENT")
    logger.info("=" * 50)
    
    # Log the entire message structure
    logger.info(f"📝 FULL MESSAGE DATA:")
    logger.info(json.dumps(data, indent=2, ensure_ascii=False))
    
    # Check all possible content fields
    content_fields = ['content', 'text', 'message', 'body']
    for field in content_fields:
        if field in data:
            logger.info(f"✅ Found field '{field}': {repr(data[field])}")
        else:
            logger.info(f"❌ Missing field '{field}'")
    
    # Check if content is in nested objects
    if 'message' in data and isinstance(data['message'], dict):
        logger.info(
            f"🔍 Nested message object: "
            f"{json.dumps(data['message'], indent=2)}"
        )
    
    # Check for content in different encodings
    content = data.get('content', '')
    if content:
        logger.info(f"✅ Content found: {repr(content)}")
        logger.info(f"📏 Content length: {len(content)}")
        logger.info(f"📊 Content type: {type(content)}")
        logger.info(f"🔤 Content bytes: {content.encode('utf-8')}")
    else:
        logger.info(f"❌ No content in 'content' field")
        
        # Check if content might be in attachments or embeds
        if data.get('attachments'):
            logger.info(f"📎 Has attachments: {len(data['attachments'])}")
        if data.get('embeds'):
            logger.info(f"🔗 Has embeds: {len(data['embeds'])}")
        if data.get('components'):
            logger.info(f"🧩 Has components: {len(data['components'])}")
    
    logger.info("=" * 50)

# Instructions:
# 1. Add this import at the top of discord_client.py:
#    from debug_patch import debug_message_content
#
# 2. Add this line in handle_message_create after the content processing:
#    debug_message_content(data)