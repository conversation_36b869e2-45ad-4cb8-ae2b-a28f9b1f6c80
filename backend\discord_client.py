import asyncio
import json
import sys
import time
import threading
import queue
from datetime import datetime, timedelta
import logging
import aiohttp
import websockets

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DiscordClient:
    def __init__(self):
        self.token = None
        self.channel_id = None
        self.guild_id = None
        self.user = None
        self.session = None
        self.websocket = None
        self.connected = False
        
        # Heartbeat system
        self.heartbeat_task = None
        self.heartbeat_interval = None
        self.sequence = None
        
        # Message queue
        self.message_queue = queue.Queue()
        self.queue_paused = False
        
        # Rate limiting
        self.last_message_time = 0
        self.message_count = 0
        self.rate_limit_reset = 0
        
        # Autoreact settings
        self.autoreact_enabled = False
        self.autoreact_emoji = '👍'
        
        self.base_url = "https://discord.com/api/v10"
        self.gateway_url = None
    
    async def connect(self, token, channel_id):
        """Connect to Discord with proper error handling"""
        self.token = token
        self.channel_id = channel_id
        
        try:
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                headers={"Authorization": self.token},
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            logger.info("🔐 Verifying Discord token...")
            await self.verify_token()
            logger.info(f"✅ Token verified for user: {self.user['username']}")
            
            logger.info(f"🔍 Checking channel access: {channel_id}")
            channel_info = await self.get_channel_info()
            logger.info(f"✅ Channel found: {channel_info.get('name', 'Unknown')}")
            
            logger.info("🌐 Getting Discord gateway URL...")
            await self.get_gateway_url()
            logger.info(f"✅ Gateway URL obtained")
            
            logger.info("🔌 Connecting to Discord Gateway...")
            await self.connect_gateway()
            logger.info("✅ Successfully connected to Discord Gateway")
            
            # Get display name and avatar
            display_name = self.user.get('global_name') or self.user['username']
            avatar_url = f"https://cdn.discordapp.com/avatars/{self.user['id']}/{self.user['avatar']}.png" if self.user.get('avatar') else None
            
            # Send success message
            self.send_to_frontend({
                'type': 'connection_success',
                'user': {
                    'id': self.user['id'],
                    'username': self.user['username'],
                    'display_name': display_name,
                    'discriminator': self.user['discriminator'],
                    'avatar': avatar_url,
                    'global_name': self.user.get('global_name')
                },
                'channel_name': channel_info.get('name', 'Unknown Channel')
            })
            
            logger.info("🚀 Discord connection fully established!")
            
        except Exception as e:
            logger.error(f"❌ Connection failed: {e}")
            self.connected = False
            await self.cleanup_connection()
            self.send_to_frontend({
                'type': 'connection_error',
                'error': str(e)
            })
    
    async def verify_token(self):
        """Verify the Discord token and get user information"""
        try:
            async with self.session.get(f"{self.base_url}/users/@me") as response:
                if response.status == 401:
                    raise Exception("Invalid Discord token - please check your token")
                elif response.status == 403:
                    raise Exception("Token forbidden - token may be disabled")
                elif response.status == 429:
                    raise Exception("Rate limited - please wait and try again")
                elif response.status != 200:
                    raise Exception(f"Failed to verify token - HTTP {response.status}")
                
                self.user = await response.json()
                
        except aiohttp.ClientError as e:
            raise Exception(f"Network error during token verification: {e}")
    
    async def get_channel_info(self):
        """Get channel information and verify access"""
        try:
            async with self.session.get(f"{self.base_url}/channels/{self.channel_id}") as response:
                if response.status == 401:
                    raise Exception("Invalid token for channel access")
                elif response.status == 403:
                    raise Exception("No permission to access this channel")
                elif response.status == 404:
                    raise Exception("Channel not found - check the channel ID")
                elif response.status != 200:
                    raise Exception(f"Failed to get channel info - HTTP {response.status}")
                
                channel_data = await response.json()
                self.guild_id = channel_data.get('guild_id')
                return channel_data
                
        except aiohttp.ClientError as e:
            raise Exception(f"Network error getting channel info: {e}")
    
    async def get_gateway_url(self):
        """Get the Discord Gateway URL"""
        try:
            async with self.session.get(f"{self.base_url}/gateway") as response:
                if response.status != 200:
                    raise Exception(f"Failed to get gateway URL - HTTP {response.status}")
                
                gateway_data = await response.json()
                self.gateway_url = f"{gateway_data['url']}/?v=10&encoding=json"
                
        except aiohttp.ClientError as e:
            raise Exception(f"Network error getting gateway URL: {e}")
    
    async def connect_gateway(self):
        """Connect to Discord Gateway WebSocket with proper error handling"""
        try:
            # Connect to WebSocket
            self.websocket = await asyncio.wait_for(
                websockets.connect(
                    self.gateway_url,
                    max_size=2**24,
                    max_queue=128,
                    ping_interval=20,
                    ping_timeout=10,
                    close_timeout=10
                ),
                timeout=15.0
            )
            
            # Send identify payload
            identify_payload = {
                "op": 2,
                "d": {
                    "token": self.token,
                    "intents": 33281,
                    "properties": {
                        "$os": "windows",
                        "$browser": "chrome",
                        "$device": "desktop"
                    },
                    "compress": False,
                    "large_threshold": 50,
                    "presence": {
                        "status": "online",
                        "since": None,
                        "activities": [],
                        "afk": False
                    }
                }
            }
            
            await self.websocket.send(json.dumps(identify_payload))
            
            # Handle initial messages
            await asyncio.wait_for(self._handle_initial_messages(), timeout=30.0)
            
            # Start continuous message listener
            self.message_listener_task = asyncio.create_task(self._message_listener())
            
        except asyncio.TimeoutError:
            raise Exception("Connection timeout - Discord gateway unreachable")
        except Exception as e:
            raise Exception(f"Failed to connect to Discord gateway: {e}")
    
    async def _handle_initial_messages(self):
        """Handle initial gateway messages until READY is received"""
        while True:
            try:
                message = await self.websocket.recv()
                data = json.loads(message)
                
                op = data.get('op')
                event_type = data.get('t')
                
                if op == 10:  # Hello
                    heartbeat_interval = data['d']['heartbeat_interval']
                    self.heartbeat_interval = heartbeat_interval / 1000.0
                    
                    # Start heartbeat task
                    self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
                    
                elif op == 0 and event_type == 'READY':
                    self.connected = True
                    ready_data = data.get('d', {})
                    if 'user' in ready_data:
                        self.user.update(ready_data['user'])
                    return
                    
                elif op == 9:  # Invalid Session
                    raise Exception("Invalid session - token may be invalid or expired")
                    
                elif op == 7:  # Reconnect
                    raise Exception("Discord requested reconnect - please try again")
                    
            except websockets.exceptions.ConnectionClosed:
                raise Exception("WebSocket connection closed during handshake")
            except json.JSONDecodeError:
                continue
    
    async def _heartbeat_loop(self):
        """Send heartbeat messages to keep connection alive"""
        try:
            while self.connected and self.websocket and not self.websocket.closed:
                await asyncio.sleep(self.heartbeat_interval)
                
                if not self.connected or not self.websocket or self.websocket.closed:
                    break
                
                heartbeat_payload = {"op": 1, "d": self.sequence}
                
                try:
                    await self.websocket.send(json.dumps(heartbeat_payload))
                except Exception:
                    break
                    
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Heartbeat error: {e}")
    
    async def _message_listener(self):
        """Listen for incoming Discord messages"""
        try:
            while self.connected and self.websocket and not self.websocket.closed:
                try:
                    message = await self.websocket.recv()
                    data = json.loads(message)
                    
                    # Update sequence number
                    if data.get('s'):
                        self.sequence = data['s']
                    
                    op = data.get('op')
                    event_type = data.get('t')
                    
                    if op == 0:  # Dispatch
                        if event_type == 'MESSAGE_CREATE':
                            await self._handle_message_create(data['d'])
                    elif op == 11:  # Heartbeat ACK
                        continue
                        
                except websockets.exceptions.ConnectionClosed:
                    logger.info("WebSocket connection closed")
                    break
                except json.JSONDecodeError:
                    continue
                except Exception as e:
                    logger.error(f"Error in message listener: {e}")
                    continue
                    
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Message listener error: {e}")
    
    async def _handle_message_create(self, message_data):
        """Handle incoming Discord message"""
        try:
            # Only process messages from our channel
            if message_data.get('channel_id') != self.channel_id:
                return
            
            # Send message to frontend (including our own messages)
            self.send_to_frontend({
                'type': 'message_received',
                'message': {
                    'id': message_data.get('id'),
                    'content': message_data.get('content', ''),
                    'timestamp': message_data.get('timestamp'),
                    'edited_timestamp': message_data.get('edited_timestamp')
                },
                'user': {
                    'id': message_data.get('author', {}).get('id'),
                    'username': message_data.get('author', {}).get('username'),
                    'display_name': message_data.get('author', {}).get('global_name') or message_data.get('author', {}).get('username'),
                    'avatar': message_data.get('author', {}).get('avatar'),
                    'discriminator': message_data.get('author', {}).get('discriminator')
                }
            })
            
            # Auto-react to own messages if enabled
            if (self.autoreact_enabled and 
                self.user and 
                message_data.get('author', {}).get('id') == self.user.get('id')):
                
                message_id = message_data.get('id')
                if message_id:
                    # React to the message asynchronously (don't block)
                    asyncio.create_task(self._add_reaction(message_id, self.autoreact_emoji))
            
        except Exception as e:
            logger.error(f"Error handling message create: {e}")
    
    async def send_message(self, content, delay=0):
        """Send message to Discord channel"""
        if not self.connected:
            raise Exception("Not connected to Discord")
        
        if delay > 0:
            await asyncio.sleep(delay)
        
        # Rate limiting
        current_time = time.time()
        if current_time - self.last_message_time < 1:
            await asyncio.sleep(1 - (current_time - self.last_message_time))
        
        try:
            payload = {"content": content}
            async with self.session.post(
                f"{self.base_url}/channels/{self.channel_id}/messages",
                json=payload
            ) as response:
                if response.status == 429:
                    retry_after = (await response.json()).get('retry_after', 1)
                    await asyncio.sleep(retry_after)
                    return await self.send_message(content, 0)
                elif response.status not in [200, 201]:
                    raise Exception(f"Failed to send message - HTTP {response.status}")
                
                self.last_message_time = time.time()
                self.message_count += 1
                
                self.send_to_frontend({
                    'type': 'message_sent',
                    'content': content,
                    'timestamp': datetime.now().isoformat()
                })
                
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise
    
    async def cleanup_connection(self):
        """Clean up connection resources"""
        try:
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass
            
            if hasattr(self, 'message_listener_task') and self.message_listener_task:
                self.message_listener_task.cancel()
                try:
                    await self.message_listener_task
                except asyncio.CancelledError:
                    pass
            
            if self.websocket:
                await self.websocket.close()
            
            if self.session:
                await self.session.close()
                
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    async def disconnect(self):
        """Disconnect from Discord"""
        self.connected = False
        await self.cleanup_connection()
        
        self.websocket = None
        self.session = None
        self.channel_id = None
        self.guild_id = None
        self.user = None
        
        self.send_to_frontend({'type': 'disconnected'})
        logger.info("✅ Disconnected from Discord")
    
    def add_message_to_queue(self, message_data):
        """Add message to queue"""
        if not self.queue_paused:
            self.message_queue.put(message_data)
    
    def pause_queue(self):
        """Pause message queue"""
        self.queue_paused = True
    
    def resume_queue(self):
        """Resume message queue"""
        self.queue_paused = False
    
    def clear_queue(self):
        """Clear message queue"""
        cleared_count = self.message_queue.qsize()
        while not self.message_queue.empty():
            try:
                self.message_queue.get_nowait()
            except queue.Empty:
                break
        return cleared_count
    
    def send_to_frontend(self, data):
        """Send data to frontend via stdout"""
        try:
            print(json.dumps(data), flush=True)
        except Exception as e:
            logger.error(f"Failed to send to frontend: {e}")
    
    async def _add_reaction(self, message_id, emoji):
        """Add reaction to a message"""
        if not self.connected or not self.session:
            return
            
        try:
            # URL encode the emoji for the API
            encoded_emoji = self._encode_emoji_for_api(emoji)
            
            url = f"{self.base_url}/channels/{self.channel_id}/messages/{message_id}/reactions/{encoded_emoji}/@me"
            
            async with self.session.put(url) as response:
                if response.status == 204:
                    logger.info(f"✅ Added reaction {emoji} to message {message_id}")
                elif response.status == 400:
                    logger.warning(f"❌ Invalid emoji format: {emoji}")
                elif response.status == 403:
                    logger.warning(f"❌ No permission to add reactions")
                elif response.status == 404:
                    logger.warning(f"❌ Message not found: {message_id}")
                else:
                    logger.warning(f"❌ Failed to add reaction: HTTP {response.status}")
                    
        except Exception as e:
            logger.error(f"❌ Error adding reaction: {e}")
    
    def _encode_emoji_for_api(self, emoji):
        """Encode emoji for Discord API with better error handling"""
        import urllib.parse
        import re
        
        if not emoji:
            return urllib.parse.quote('👍')  # Default fallback
        
        emoji = emoji.strip()
        
        # Handle custom Discord emoji format <:name:id> or <a:name:id>
        if emoji.startswith('<') and emoji.endswith('>'):
            # Extract name:id from <:name:id> or <a:name:id>
            if emoji.startswith('<a:'):
                # Animated emoji
                return emoji[3:-1]  # Remove <a: and >
            elif emoji.startswith('<:'):
                # Static custom emoji
                return emoji[2:-1]  # Remove <: and >
            else:
                return emoji[1:-1]  # Remove < and >
        
        # Handle shortcode format :name:
        elif emoji.startswith(':') and emoji.endswith(':') and len(emoji) > 2:
            # Convert shortcode to Unicode if possible
            shortcode_to_unicode = {
                ':thumbsup:': '👍',
                ':thumbsdown:': '👎',
                ':heart:': '❤️',
                ':fire:': '🔥',
                ':100:': '💯',
                ':ok_hand:': '👌',
                ':clap:': '👏',
                ':eyes:': '👀',
                ':thinking:': '🤔',
                ':laughing:': '😂',
                ':cry:': '😢',
                ':angry:': '😠',
                ':smile:': '😄',
                ':wink:': '😉',
                ':confused:': '😕',
                ':sunglasses:': '😎',
                ':warning:': '⚠️',
                ':skull:': '💀',
                ':skull_and_crossbones:': '☠️'
            }
            
            unicode_emoji = shortcode_to_unicode.get(emoji.lower())
            if unicode_emoji:
                return urllib.parse.quote(unicode_emoji)
            else:
                # If shortcode not found, try to use it as is (might be a custom server emoji)
                return emoji[1:-1]  # Remove : and :
        
        # Handle Unicode emoji (default case)
        else:
            try:
                # Ensure the emoji is properly encoded
                # First try to encode/decode to fix any corruption
                if isinstance(emoji, str):
                    # Check if emoji looks corrupted (contains weird characters)
                    if any(ord(c) > 0x10000 for c in emoji if ord(c) < 0x1F000):
                        logger.warning(f"⚠️ Detected potentially corrupted emoji: {repr(emoji)}")
                        # Try to fix common corruption patterns
                        emoji = self._fix_corrupted_emoji(emoji)
                
                return urllib.parse.quote(emoji)
            except Exception as e:
                logger.error(f"❌ Failed to encode emoji '{emoji}': {e}")
                return urllib.parse.quote('👍')  # Fallback to thumbs up
    
    def _fix_corrupted_emoji(self, emoji):
        """Attempt to fix corrupted emoji encoding"""
        try:
            # Common corrupted emoji patterns and their fixes
            corruption_fixes = {
                'Γÿá∩╕Å': '⚠️',  # Warning emoji corruption
                'ΓÇÅ': '👍',      # Thumbs up corruption
                'Γÿ¿': '❤️',      # Heart corruption
                'ΓÇÄ': '🔥',      # Fire corruption
            }
            
            # Check for exact matches first
            if emoji in corruption_fixes:
                fixed = corruption_fixes[emoji]
                logger.info(f"🔧 Fixed corrupted emoji '{emoji}' -> '{fixed}'")
                return fixed
            
            # Try to decode as UTF-8 bytes if it looks like corruption
            try:
                # If the string contains high-value characters, try to re-encode
                emoji_bytes = emoji.encode('latin1')
                fixed_emoji = emoji_bytes.decode('utf-8')
                logger.info(f"🔧 Fixed emoji encoding: '{emoji}' -> '{fixed_emoji}'")
                return fixed_emoji
            except:
                pass
            
            logger.warning(f"⚠️ Could not fix corrupted emoji '{emoji}', using default")
            return '👍'
            
        except Exception as e:
            logger.error(f"❌ Error fixing corrupted emoji: {e}")
            return '👍'
    
    def update_autoreact_settings(self, enabled, emoji):
        """Update autoreact settings with emoji validation"""
        self.autoreact_enabled = enabled
        
        # Clean and validate the emoji
        if emoji:
            emoji = emoji.strip()
            # Check if emoji looks corrupted and try to fix it
            if any(ord(c) > 0x10000 for c in emoji if ord(c) < 0x1F000):
                emoji = self._fix_corrupted_emoji(emoji)
        
        self.autoreact_emoji = emoji or '👍'  # Default to thumbs up
        
        # Log the settings with proper encoding info
        encoded_emoji = self._encode_emoji_for_api(self.autoreact_emoji)
        logger.info(f"🎭 Autoreact: {'enabled' if enabled else 'disabled'} with emoji '{self.autoreact_emoji}' (encoded: {encoded_emoji})")

class ZephyrBackend:
    def __init__(self):
        self.client = DiscordClient()
        self.loop = None
        
    def start(self):
        """Start the backend"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        # Start input processing in a separate thread
        input_thread = threading.Thread(
            target=self.process_input, daemon=True
        )
        input_thread.start()
        
        # Run the main loop
        try:
            self.loop.run_forever()
        except KeyboardInterrupt:
            pass
        finally:
            self.loop.run_until_complete(self.client.cleanup_connection())
    
    def process_input(self):
        """Process input from the main process"""
        while True:
            try:
                line = sys.stdin.readline()
                if not line:
                    break
                
                data = json.loads(line.strip())
                asyncio.run_coroutine_threadsafe(
                    self.handle_message(data), self.loop
                )
                
            except json.JSONDecodeError:
                logger.error("Invalid JSON received")
            except Exception as e:
                logger.error(f"Input processing error: {e}")
    
    async def handle_message(self, data):
        """Handle messages from frontend"""
        action = data.get('action')
        
        try:
            if action == 'connect':
                token = data.get('token', '').strip()
                channel_id = data.get('channel_id', '').strip()
                
                if not token:
                    raise Exception("Discord token is required")
                if not channel_id:
                    raise Exception("Channel ID is required")
                if not channel_id.isdigit():
                    raise Exception("Channel ID must be numeric")
                
                await self.client.connect(token, channel_id)
                
            elif action == 'disconnect':
                await self.client.disconnect()
                
            elif action == 'send_message':
                if not self.client.connected:
                    raise Exception("Not connected to Discord")
                    
                content = data.get('content', '').strip()
                if not content:
                    raise Exception("Message content cannot be empty")
                    
                delay = data.get('delay', 0)
                await self.client.send_message(content, delay)
                
            elif action == 'pause_queue':
                self.client.pause_queue()
                
            elif action == 'resume_queue':
                self.client.resume_queue()
                
            elif action == 'clear_queue':
                cleared = self.client.clear_queue()
                logger.info(f"Cleared {cleared} messages from queue")
                
            elif action == 'update_autoreact':
                enabled = data.get('enabled', False)
                emoji = data.get('emoji', '👍')
                self.client.update_autoreact_settings(enabled, emoji)
                
            else:
                logger.warning(f"Unknown action: {action}")
                
        except Exception as e:
            logger.error(f"Error handling message: {e}")
            if action == 'connect':
                self.client.send_to_frontend({
                    'type': 'connection_error',
                    'error': str(e)
                })

if __name__ == "__main__":
    backend = ZephyrBackend()
    backend.start()


