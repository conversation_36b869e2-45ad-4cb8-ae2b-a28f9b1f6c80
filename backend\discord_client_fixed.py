import asyncio
import json
import sys
import time
import threading
import queue
from datetime import datetime, timedelta
import logging
import aiohttp
import websockets
import requests
import concurrent.futures

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DiscordClient:
    def __init__(self):
        self.token = None
        self.channel_id = None
        self.guild_id = None
        self.user = None
        self.session = None
        self.websocket = None
        self.connected = False
        
        # Heartbeat system with health monitoring
        self.heartbeat_task = None
        self.heartbeat_interval = None
        self.sequence = None
        self.last_heartbeat_ack = time.time()
        self.heartbeat_timeout = 60  # 60 seconds without ACK = dead connection
        
        # Enhanced Message queue system
        self.message_queue = []
        self.queue_paused = False
        self.queue_processing = False
        self.queue_lock = asyncio.Lock()
        self.queue_processor_task = None
        
        # Rate limiting
        self.last_message_time = 0
        self.message_count = 0
        self.rate_limit_reset = 0
        
        # Settings
        self.message_delay = 0.1  # Default message delay in seconds
        self.rate_limit_delay = 0.1  # Reduced from 1 second to 0.1 seconds
        
        # Burst mode system (integrated with normal messaging)
        self.burst_mode_enabled = False
        self.burst_intervals = {
            'double': 15,  # seconds
            'triple': 20,  # seconds
            'quad': 25     # seconds
        }
        self.burst_cycle = ['double', 'triple', 'quad']  # Burst cycle order
        self.current_burst_index = 0  # Current position in burst cycle
        self.last_burst_time = 0  # When the last burst of any type occurred
        self.original_message_delay = 0.1  # Store original delay
        self.rate_limited_delay = 2.5  # Delay when rate limited
        self.post_burst_mode = False  # Track if we're in post-burst fast mode
        self.post_burst_start_time = 0  # When post-burst mode started
        self.current_burst_rate_limited = False  # Track if current burst has been rate limited
        
        # Connection health monitoring
        self.message_listener_task = None
        self.connection_monitor_task = None
        self.last_message_received = time.time()
        self.connection_timeout = 300  # 5 minutes without any activity = reconnect
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        
        self.base_url = "https://discord.com/api/v10"
        self.gateway_url = None
    
    async def connect(self, token, channel_id):
        """Connect to Discord with proper error handling"""
        self.token = token
        self.channel_id = channel_id
        
        try:
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                headers={"Authorization": self.token},
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            logger.info("🔐 Verifying Discord token...")
            await self.verify_token()
            logger.info(f"✅ Token verified for user: {self.user['username']}")
            
            logger.info(f"🔍 Checking channel access: {channel_id}")
            channel_info = await self.get_channel_info()
            logger.info(f"✅ Channel found: {channel_info.get('name', 'Unknown')}")
            
            logger.info("🌐 Getting Discord gateway URL...")
            await self.get_gateway_url()
            logger.info(f"✅ Gateway URL obtained")
            
            logger.info("🔌 Connecting to Discord Gateway...")
            await self.connect_gateway()
            logger.info("✅ Successfully connected to Discord Gateway")
            
            # Get display name and avatar
            display_name = self.user.get('global_name') or self.user['username']
            avatar_url = f"https://cdn.discordapp.com/avatars/{self.user['id']}/{self.user['avatar']}.png" if self.user.get('avatar') else None
            
            # Send success message
            self.send_to_frontend({
                'type': 'connection_success',
                'user': {
                    'id': self.user['id'],
                    'username': self.user['username'],
                    'display_name': display_name,
                    'discriminator': self.user['discriminator'],
                    'avatar': avatar_url,
                    'global_name': self.user.get('global_name')
                },
                'channel_name': channel_info.get('name', 'Unknown Channel')
            })
            
            logger.info("🚀 Discord connection fully established!")
            
        except Exception as e:
            logger.error(f"❌ Connection failed: {e}")
            self.connected = False
            await self.cleanup_connection()
            self.send_to_frontend({
                'type': 'connection_error',
                'error': str(e)
            })
    
    async def verify_token(self):
        """Verify the Discord token and get user information"""
        try:
            async with self.session.get(f"{self.base_url}/users/@me") as response:
                if response.status == 401:
                    raise Exception("Invalid Discord token - please check your token")
                elif response.status == 403:
                    raise Exception("Token forbidden - token may be disabled")
                elif response.status == 429:
                    raise Exception("Rate limited - please wait and try again")
                elif response.status != 200:
                    raise Exception(f"Failed to verify token - HTTP {response.status}")
                
                self.user = await response.json()
                
        except aiohttp.ClientError as e:
            raise Exception(f"Network error during token verification: {e}")
    
    async def get_channel_info(self):
        """Get channel information and verify access"""
        try:
            async with self.session.get(f"{self.base_url}/channels/{self.channel_id}") as response:
                if response.status == 401:
                    raise Exception("Invalid token for channel access")
                elif response.status == 403:
                    raise Exception("No permission to access this channel")
                elif response.status == 404:
                    raise Exception("Channel not found - check the channel ID")
                elif response.status != 200:
                    raise Exception(f"Failed to get channel info - HTTP {response.status}")
                
                channel_data = await response.json()
                self.guild_id = channel_data.get('guild_id')
                return channel_data
                
        except aiohttp.ClientError as e:
            raise Exception(f"Network error getting channel info: {e}")
    
    async def get_gateway_url(self):
        """Get the Discord Gateway URL"""
        try:
            async with self.session.get(f"{self.base_url}/gateway") as response:
                if response.status != 200:
                    raise Exception(f"Failed to get gateway URL - HTTP {response.status}")
                
                gateway_data = await response.json()
                self.gateway_url = f"{gateway_data['url']}/?v=10&encoding=json"
                
        except aiohttp.ClientError as e:
            raise Exception(f"Network error getting gateway URL: {e}")
    
    async def connect_gateway(self):
        """Connect to Discord Gateway WebSocket with proper error handling"""
        try:
            # Connect to WebSocket
            self.websocket = await asyncio.wait_for(
                websockets.connect(
                    self.gateway_url,
                    max_size=2**24,
                    max_queue=128,
                    ping_interval=20,
                    ping_timeout=10,
                    close_timeout=10
                ),
                timeout=15.0
            )
            
            # Send identify payload
            identify_payload = {
                "op": 2,
                "d": {
                    "token": self.token,
                    "intents": 33281,
                    "properties": {
                        "$os": "windows",
                        "$browser": "chrome",
                        "$device": "desktop"
                    },
                    "compress": False,
                    "large_threshold": 50,
                    "presence": {
                        "status": "online",
                        "since": None,
                        "activities": [],
                        "afk": False
                    }
                }
            }
            
            await self.websocket.send(json.dumps(identify_payload))
            
            # Handle initial messages
            await asyncio.wait_for(self._handle_initial_messages(), timeout=30.0)
            
            # Start continuous message listener with health monitoring
            self.message_listener_task = asyncio.create_task(self._message_listener_with_health_check())
            
            # Start connection health monitor
            self.connection_monitor_task = asyncio.create_task(self._connection_health_monitor())
            
        except asyncio.TimeoutError:
            raise Exception("Connection timeout - Discord gateway unreachable")
        except Exception as e:
            raise Exception(f"Failed to connect to Discord gateway: {e}")
    
    async def _handle_initial_messages(self):
        """Handle initial gateway messages until READY is received"""
        while True:
            try:
                message = await self.websocket.recv()
                data = json.loads(message)
                
                op = data.get('op')
                event_type = data.get('t')
                
                if op == 10:  # Hello
                    heartbeat_interval = data['d']['heartbeat_interval']
                    self.heartbeat_interval = heartbeat_interval / 1000.0
                    
                    # Start heartbeat task
                    self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
                    
                elif op == 0 and event_type == 'READY':
                    self.connected = True
                    ready_data = data.get('d', {})
                    if 'user' in ready_data:
                        self.user.update(ready_data['user'])
                    return
                    
                elif op == 9:  # Invalid Session
                    raise Exception("Invalid session - token may be invalid or expired")
                    
                elif op == 7:  # Reconnect
                    raise Exception("Discord requested reconnect - please try again")
                    
            except websockets.exceptions.ConnectionClosed:
                raise Exception("WebSocket connection closed during handshake")
            except json.JSONDecodeError:
                continue
    
    async def _heartbeat_loop(self):
        """Send heartbeat messages to keep connection alive with health monitoring"""
        try:
            while self.connected and self.websocket and not self.websocket.closed:
                await asyncio.sleep(self.heartbeat_interval)
                
                if not self.connected or not self.websocket or self.websocket.closed:
                    break
                
                # Check if we've received a heartbeat ACK recently
                if time.time() - self.last_heartbeat_ack > self.heartbeat_timeout:
                    logger.warning("💔 Heartbeat timeout - connection may be dead")
                    break
                
                heartbeat_payload = {"op": 1, "d": self.sequence}
                
                try:
                    await self.websocket.send(json.dumps(heartbeat_payload))
                    logger.debug("💓 Heartbeat sent")
                except Exception as e:
                    logger.error(f"💔 Failed to send heartbeat: {e}")
                    break
                    
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Heartbeat error: {e}")
    
    async def _connection_health_monitor(self):
        """Monitor connection health and trigger reconnection if needed"""
        try:
            while self.connected:
                await asyncio.sleep(60)  # Check every 60 seconds (less aggressive)
                
                if not self.connected:
                    break
                
                current_time = time.time()
                
                # Only check for connection issues if we're not already reconnecting
                if self.reconnect_attempts == 0:
                    # Check heartbeat health first (more reliable indicator)
                    if current_time - self.last_heartbeat_ack > self.heartbeat_timeout:
                        logger.warning("💔 Heartbeat timeout detected - connection may be dead")
                        await self._trigger_reconnection("Heartbeat timeout")
                        break
                    
                    # Check if we haven't received any messages for too long (less critical)
                    elif current_time - self.last_message_received > self.connection_timeout:
                        logger.warning("🔍 No messages received for 5 minutes - connection may be stale")
                        # Send a test message to verify connection is working
                        try:
                            await self._send_connection_test()
                            logger.info("✅ Connection test passed - connection is healthy")
                        except Exception as e:
                            logger.error(f"❌ Connection test failed: {e}")
                            await self._trigger_reconnection("Connection test failed")
                            break
                else:
                    # If we're already reconnecting, just wait
                    logger.debug(f"🔄 Reconnection in progress (attempt {self.reconnect_attempts})")
                    
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Connection health monitor error: {e}")
    
    async def _trigger_reconnection(self, reason):
        """Trigger a reconnection attempt"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error(f"❌ Max reconnection attempts reached. Giving up.")
            await self.disconnect()
            return
        
        self.reconnect_attempts += 1
        logger.info(f"🔄 Triggering reconnection (attempt {self.reconnect_attempts}/{self.max_reconnect_attempts}): {reason}")
        
        try:
            # Clean up current connection completely
            await self._cleanup_websocket_connection()
            
            # Wait with exponential backoff before reconnecting
            wait_time = min(2 ** self.reconnect_attempts, 30)
            logger.info(f"⏳ Waiting {wait_time}s before reconnection attempt...")
            await asyncio.sleep(wait_time)
            
            # Get fresh gateway URL to avoid stale connections
            logger.info("🌐 Getting fresh gateway URL...")
            await self.get_gateway_url()
            
            # Reconnect to gateway
            logger.info("🔌 Attempting gateway reconnection...")
            await self.connect_gateway()
            
            # Reset reconnect attempts on successful connection
            self.reconnect_attempts = 0
            logger.info("✅ Reconnection successful")
            
        except Exception as e:
            logger.error(f"❌ Reconnection failed: {e}")
            # Schedule another reconnection attempt
            if self.reconnect_attempts < self.max_reconnect_attempts:
                logger.info(f"🔄 Will retry reconnection in health check cycle...")
            else:
                logger.error("❌ Max reconnection attempts reached, disconnecting...")
                await self.disconnect()
    
    async def _cleanup_websocket_connection(self):
        """Clean up WebSocket connection and related tasks"""
        try:
            # Cancel message listener task
            if self.message_listener_task and not self.message_listener_task.done():
                self.message_listener_task.cancel()
                try:
                    await self.message_listener_task
                except asyncio.CancelledError:
                    pass
                self.message_listener_task = None
            
            # Cancel connection monitor task
            if self.connection_monitor_task and not self.connection_monitor_task.done():
                self.connection_monitor_task.cancel()
                try:
                    await self.connection_monitor_task
                except asyncio.CancelledError:
                    pass
                self.connection_monitor_task = None
            
            # Cancel heartbeat task
            if self.heartbeat_task and not self.heartbeat_task.done():
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass
                self.heartbeat_task = None
            
            # Close WebSocket connection
            if self.websocket and not self.websocket.closed:
                logger.info("🔌 Closing WebSocket connection...")
                await self.websocket.close()
                self.websocket = None
            
            # Reset connection state
            self.sequence = None
            self.last_heartbeat_ack = time.time()
            
        except Exception as e:
            logger.error(f"❌ Error during WebSocket cleanup: {e}")
    
    async def _check_connection_health(self):
        """Check if the WebSocket connection is healthy"""
        try:
            if not self.websocket or self.websocket.closed:
                return False
            
            # Check if we've received a heartbeat ACK recently
            time_since_ack = time.time() - self.last_heartbeat_ack
            if time_since_ack > (self.heartbeat_interval * 2):
                logger.warning(f"⚠️ No heartbeat ACK for {time_since_ack:.1f}s")
                return False
            
            # Try to send a ping
            await self.websocket.ping()
            return True
            
        except Exception as e:
            logger.error(f"❌ Connection health check failed: {e}")
            return False
    
    async def _send_connection_test(self):
        """Send a test message to verify connection is working"""
        # This is a silent test - we don't actually send a message
        # Instead, we just check if we can make an API call
        try:
            async with self.session.get(f"{self.base_url}/users/@me") as response:
                if response.status != 200:
                    raise Exception(f"API test failed with status {response.status}")
                logger.debug("✅ Connection test passed")
        except Exception as e:
            logger.error(f"❌ Connection test failed: {e}")
            raise
    
    async def _message_listener_with_health_check(self):
        """Message listener with enhanced health monitoring and timeout handling"""
        logger.info("🎧 Enhanced message listener started")
        
        try:
            while self.connected and self.websocket and not self.websocket.closed:
                try:
                    # Use timeout to prevent indefinite blocking
                    message = await asyncio.wait_for(
                        self.websocket.recv(), 
                        timeout=60.0  # 60 second timeout
                    )
                    
                    # Update last message received time
                    self.last_message_received = time.time()
                    
                    data = json.loads(message)
                    
                    # Update sequence number
                    if data.get('s'):
                        self.sequence = data['s']
                    
                    op = data.get('op')
                    event_type = data.get('t')
                    
                    if op == 0:  # Dispatch
                        if event_type == 'MESSAGE_CREATE':
                            logger.debug(f"📨 Received message: {data['d'].get('content', '')[:50]}...")
                            await self._handle_message_create(data['d'])
                        elif event_type == 'READY':
                            logger.info("🔄 Received READY event")
                    elif op == 11:  # Heartbeat ACK
                        self.last_heartbeat_ack = time.time()
                        logger.debug("💓 Heartbeat ACK received")
                        continue
                    elif op == 9:  # Invalid Session
                        logger.error("❌ Invalid session - triggering reconnection")
                        # Don't trigger reconnection if already reconnecting
                        if self.reconnect_attempts == 0:
                            await self._trigger_reconnection("Invalid session")
                        break
                    elif op == 7:  # Reconnect
                        logger.info("🔄 Discord requested reconnect")
                        # Don't trigger reconnection if already reconnecting
                        if self.reconnect_attempts == 0:
                            await self._trigger_reconnection("Discord requested reconnect")
                        break
                        
                except asyncio.TimeoutError:
                    # Timeout waiting for message - check connection health
                    logger.debug("⏰ Message receive timeout - checking connection health")
                    if not await self._check_connection_health():
                        logger.error("❌ Connection unhealthy - triggering reconnection")
                        await self._trigger_reconnection("Connection health check failed")
                        break
                    continue
                except websockets.exceptions.ConnectionClosed as e:
                    logger.warning(f"🔌 WebSocket connection closed: {e}")
                    await self._trigger_reconnection("WebSocket connection closed")
                    break
                except json.JSONDecodeError as e:
                    logger.warning(f"⚠️ Invalid JSON received: {e}")
                    continue
                except Exception as e:
                    logger.error(f"❌ Unexpected error in message listener: {e}")
                    # Don't break on unexpected errors, just continue after a short delay
                    await asyncio.sleep(1)
                    continue
                    
        except asyncio.CancelledError:
            logger.info("🛑 Message listener cancelled")
        except Exception as e:
            logger.error(f"❌ Fatal message listener error: {e}")
        finally:
            logger.info("🔌 Enhanced message listener stopped")
    
    async def _handle_message_create(self, message_data):
        """Handle incoming Discord message"""
        try:
            # Only process messages from our channel
            if message_data.get('channel_id') != self.channel_id:
                return
            
            # Send message to frontend (including our own messages)
            self.send_to_frontend({
                'type': 'message_received',
                'message': {
                    'id': message_data.get('id'),
                    'content': message_data.get('content', ''),
                    'timestamp': message_data.get('timestamp'),
                    'edited_timestamp': message_data.get('edited_timestamp')
                },
                'user': {
                    'id': message_data.get('author', {}).get('id'),
                    'username': message_data.get('author', {}).get('username'),
                    'display_name': message_data.get('author', {}).get('global_name') or message_data.get('author', {}).get('username'),
                    'avatar': message_data.get('author', {}).get('avatar'),
                    'discriminator': message_data.get('author', {}).get('discriminator')
                }
            })
            
        except Exception as e:
            logger.error(f"Error handling message create: {e}")
    
    async def add_message_to_queue(self, content, delay=0):
        """Add message to queue for ordered processing"""
        async with self.queue_lock:
            message_id = f"msg_{int(time.time() * 1000)}_{len(self.message_queue)}"
            queue_item = {
                'id': message_id,
                'content': content,
                'delay': delay,
                'timestamp': time.time(),
                'retries': 0,
                'status': 'queued'
            }
            
            self.message_queue.append(queue_item)
            self.send_queue_size_update()
            
            # Start queue processor if not running
            if not self.queue_processing:
                self.queue_processor_task = asyncio.create_task(self._process_queue())
            
            return message_id
    
    async def _process_queue(self):
        """Process messages from queue in order with integrated burst mode"""
        self.queue_processing = True
        
        try:
            while self.message_queue and self.connected:
                if self.queue_paused:
                    await asyncio.sleep(0.1)
                    continue
                
                # Check for burst opportunities if burst mode is enabled
                if self.burst_mode_enabled:
                    burst_executed = await self._check_and_execute_burst()
                    if burst_executed:
                        # After burst, enable fast mode (0.1s delay)
                        self.post_burst_mode = True
                        self.post_burst_start_time = time.time()
                        logger.info(f"🚀 Entering post-burst fast mode (0.1s delay) - burst timing paused. Rate limited flag: {self.current_burst_rate_limited}")
                        continue
                
                # Process single message
                async with self.queue_lock:
                    if not self.message_queue:
                        break
                    
                    current_message = self.message_queue[0]
                
                try:
                    # Determine delay
                    if current_message['delay'] > 0:
                        delay = current_message['delay']
                    elif self.post_burst_mode:
                        delay = 0.1  # Fast mode after burst
                    else:
                        delay = self.message_delay
                    
                    if delay > 0:
                        await asyncio.sleep(delay)
                    
                    # Send the message
                    await self.send_message(current_message['content'], 0)
                    
                    # Remove from queue on success
                    async with self.queue_lock:
                        if self.message_queue and self.message_queue[0]['id'] == current_message['id']:
                            self.message_queue.pop(0)
                            self.send_queue_size_update()
                    
                except Exception as e:
                    logger.error(f"❌ Failed to send queue message: {e}")
                    
                    # Check if it's a rate limit error
                    if "429" in str(e) or "rate limit" in str(e).lower():
                        logger.info(f"🚫 Rate limit detected! Post-burst mode: {self.post_burst_mode}, Already rate limited: {self.current_burst_rate_limited}")
                        # Exit post-burst fast mode on rate limit (only once per burst)
                        if self.post_burst_mode and not self.current_burst_rate_limited:
                            logger.info("✅ Processing first rate limit for this burst")
                            self.current_burst_rate_limited = True  # Mark this burst as rate limited
                            self._exit_post_burst_mode()
                        elif self.current_burst_rate_limited:
                            logger.info("🔄 Additional rate limit ignored - current burst already processed")
                        elif not self.post_burst_mode:
                            logger.info("ℹ️ Rate limit detected but not in post-burst mode")
                    
                    current_message['retries'] += 1
                    
                    if current_message['retries'] >= 3:
                        # Remove failed message after 3 retries
                        async with self.queue_lock:
                            if self.message_queue and self.message_queue[0]['id'] == current_message['id']:
                                self.message_queue.pop(0)
                                self.send_queue_size_update()
                    else:
                        # Retry after delay
                        await asyncio.sleep(2 ** current_message['retries'])
                
        except Exception as e:
            logger.error(f"Queue processor error: {e}")
        finally:
            self.queue_processing = False
    
    async def _check_and_execute_burst(self):
        """Check if the next burst in cycle should be executed"""
        # Don't burst while in post-burst fast mode (pause burst timing)
        if self.post_burst_mode:
            logger.debug(f"⏸️ Burst timing paused - in post-burst fast mode. Rate limited: {self.current_burst_rate_limited}")
            return False
        
        current_time = time.time()
        
        # Get the current burst type in the cycle
        current_burst_type = self.burst_cycle[self.current_burst_index]
        burst_count = {'double': 2, 'triple': 3, 'quad': 4}[current_burst_type]
        burst_interval = self.burst_intervals[current_burst_type]
        
        # Check if enough time has passed and we have enough messages
        if (current_time - self.last_burst_time >= burst_interval and
            len(self.message_queue) >= burst_count):
            
            logger.info(f"💥 Executing {current_burst_type} burst ({self.current_burst_index + 1}/3 in cycle) with {burst_count} messages")
            
            # PAUSE normal queue processing during burst
            logger.info("⏸️ Pausing normal queue processing for burst execution")
            self.queue_paused = True
            
            try:
                # Extract messages for burst
                messages_to_burst = []
                async with self.queue_lock:
                    messages_to_burst = self.message_queue[:burst_count]
                    # Remove from queue immediately
                    self.message_queue = self.message_queue[burst_count:]
                    self.send_queue_size_update()
                
                # Execute burst with threading for TRUE simultaneous sending
                await self._execute_integrated_burst(current_burst_type, messages_to_burst)
                
            finally:
                # ALWAYS resume normal queue processing after burst (even if error)
                logger.info("▶️ Resuming normal queue processing after burst")
                self.queue_paused = False
            
            # Update last burst time and move to next in cycle
            self.last_burst_time = current_time
            self.current_burst_index = (self.current_burst_index + 1) % len(self.burst_cycle)
            
            # Reset rate limit flag for the new burst
            self.current_burst_rate_limited = False
            
            next_burst_type = self.burst_cycle[self.current_burst_index]
            logger.info(f"🔄 Next burst in cycle: {next_burst_type} (in {self.burst_intervals[next_burst_type]}s) - rate limit flag reset")
            
            return True
        
        return False
    
    async def _execute_integrated_burst(self, burst_type, messages):
        """Execute burst messages simultaneously using THREADING for true simultaneity"""
        try:
            def send_burst_message_sync(content):
                """Send a single burst message synchronously in a thread"""
                try:
                    # Use requests for synchronous HTTP (better for threading)
                    response = requests.post(
                        f"{self.base_url}/channels/{self.channel_id}/messages",
                        headers={"Authorization": self.token},
                        json={"content": content},
                        timeout=30
                    )
                    
                    if response.status_code == 429:
                        retry_after = response.json().get('retry_after', 1)
                        logger.warning(f"🚫 Burst message rate limited: {retry_after}s")
                        return {'status': 'rate_limited', 'retry_after': retry_after}
                    elif response.status_code not in [200, 201]:
                        logger.error(f"❌ Burst message failed: HTTP {response.status_code}")
                        return {'status': 'error', 'code': response.status_code}
                    else:
                        logger.debug(f"✅ Burst message sent: {content[:50]}...")
                        return {'status': 'success', 'code': response.status_code}
                        
                except Exception as e:
                    logger.error(f"❌ Error sending burst message: {e}")
                    return {'status': 'exception', 'error': str(e)}
            
            # Use ThreadPoolExecutor for TRUE simultaneous sending
            loop = asyncio.get_event_loop()
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=len(messages)) as executor:
                # Submit all burst messages to threads simultaneously
                futures = []
                for message in messages:
                    content = message.get('content', '')
                    future = loop.run_in_executor(executor, send_burst_message_sync, content)
                    futures.append(future)
                
                logger.info(f"🚀 Launching {len(futures)} threads for simultaneous burst sending...")
                
                # Wait for ALL threads to complete
                results = await asyncio.gather(*futures, return_exceptions=True)
            
            # Analyze results
            success_count = 0
            rate_limited = False
            error_count = 0
            
            for result in results:
                if isinstance(result, dict):
                    if result['status'] == 'success':
                        success_count += 1
                    elif result['status'] == 'rate_limited':
                        rate_limited = True
                    else:
                        error_count += 1
                else:
                    error_count += 1
            
            # Log burst completion
            logger.info(f"🎯 {burst_type.capitalize()} burst completed: {success_count} success, {error_count} errors")
            
            if rate_limited:
                logger.warning("🚫 Burst triggered rate limit - normal queue will handle this")
            
            # Send burst completion notification to frontend
            self.send_to_frontend({
                'type': 'burst_executed',
                'burst_type': burst_type,
                'message_count': len(messages),
                'success_count': success_count,
                'error_count': error_count,
                'rate_limited': rate_limited,
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"❌ Critical error executing {burst_type} burst: {e}")
            # Queue resumption is handled by the calling function's finally block
    
    def _exit_post_burst_mode(self):
        """Exit post-burst mode and adjust burst timer to account for paused time"""
        if not self.post_burst_mode:
            return
        
        # Calculate how long we were in post-burst mode
        post_burst_duration = time.time() - self.post_burst_start_time
        
        # Adjust the burst timer by adding the paused duration
        # This effectively "pauses" the burst timing during post-burst mode
        self.last_burst_time += post_burst_duration
        
        # Set message speed to 2.5s when rate limited
        self.message_delay = self.rate_limited_delay
        
        # Exit post-burst mode
        self.post_burst_mode = False
        self.post_burst_start_time = 0
        
        current_burst_type = self.burst_cycle[self.current_burst_index]
        logger.info(f"⏰ Rate limited! Burst timing resumed, speed set to {self.rate_limited_delay}s. Next burst: {current_burst_type} (adjusted by {post_burst_duration:.1f}s)")
    
    def send_queue_size_update(self):
        """Send queue size update to frontend"""
        size = len(self.message_queue)
        self.send_to_frontend({
            'type': 'queue_size_update',
            'size': size
        })
    
    async def send_message(self, content, delay=0):
        """Send message to Discord channel"""
        if not self.connected:
            raise Exception("Not connected to Discord")
        
        if delay > 0:
            await asyncio.sleep(delay)
        
        # Rate limiting
        current_time = time.time()
        if current_time - self.last_message_time < self.rate_limit_delay:
            await asyncio.sleep(self.rate_limit_delay - (current_time - self.last_message_time))
        
        try:
            payload = {"content": content}
            async with self.session.post(
                f"{self.base_url}/channels/{self.channel_id}/messages",
                json=payload
            ) as response:
                if response.status == 429:
                    retry_after = (await response.json()).get('retry_after', 1)
                    logger.info(f"🚫 Rate limited! Waiting {retry_after}s before retry")
                    await asyncio.sleep(retry_after)
                    # Raise exception to trigger rate limit handling in queue processor
                    raise Exception(f"Rate limited - HTTP 429 (retry_after: {retry_after}s)")
                elif response.status not in [200, 201]:
                    raise Exception(f"Failed to send message - HTTP {response.status}")
                
                self.last_message_time = time.time()
                self.message_count += 1
                
                self.send_to_frontend({
                    'type': 'message_sent',
                    'content': content,
                    'timestamp': datetime.now().isoformat()
                })
                
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise
    
    async def cleanup_connection(self):
        """Clean up connection resources"""
        try:
            # Cancel all tasks
            tasks_to_cancel = [
                ('heartbeat_task', self.heartbeat_task),
                ('message_listener_task', self.message_listener_task),
                ('connection_monitor_task', self.connection_monitor_task),
                ('queue_processor_task', self.queue_processor_task)
            ]
            

            
            for task_name, task in tasks_to_cancel:
                if task:
                    logger.info(f"🛑 Cancelling {task_name}")
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                    except Exception as e:
                        logger.error(f"❌ Error cancelling {task_name}: {e}")
            
            # Close WebSocket
            if self.websocket:
                logger.info("🔌 Closing WebSocket")
                await self.websocket.close()
            
            # Close HTTP session
            if self.session:
                logger.info("🔌 Closing HTTP session")
                await self.session.close()
                
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
    
    async def disconnect(self):
        """Disconnect from Discord"""
        self.connected = False
        await self.cleanup_connection()
        
        self.websocket = None
        self.session = None
        self.channel_id = None
        self.guild_id = None
        self.user = None
        
        self.send_to_frontend({'type': 'disconnected'})
        logger.info("✅ Disconnected from Discord")
    
    def pause_queue(self):
        """Pause message queue"""
        self.queue_paused = True
        self.send_to_frontend({'type': 'queue_paused'})
    
    def resume_queue(self):
        """Resume message queue"""
        self.queue_paused = False
        self.send_to_frontend({'type': 'queue_resumed'})
    
    async def clear_queue(self):
        """Clear message queue"""
        async with self.queue_lock:
            cleared_count = len(self.message_queue)
            self.message_queue.clear()
            self.send_queue_size_update()
        
        self.send_to_frontend({'type': 'queue_cleared'})
        return cleared_count
    
    def start_burst_mode(self, intervals):
        """Start integrated burst mode with cycling system"""
        self.burst_mode_enabled = True
        self.burst_intervals.update(intervals)
        self.original_message_delay = self.message_delay
        self.current_burst_rate_limited = False  # Reset rate limit flag when starting
        
        # Don't reset burst progress - continue from current position in cycle
        current_burst_type = self.burst_cycle[self.current_burst_index]
        logger.info(f"🚀 Integrated burst mode started with cycling system")
        logger.info(f"📊 Intervals: {self.burst_intervals}")
        logger.info(f"🔄 Current burst in cycle: {current_burst_type} ({self.current_burst_index + 1}/3)")
    
    def stop_burst_mode(self):
        """Stop integrated burst mode"""
        self.burst_mode_enabled = False
        self._exit_post_burst_mode()  # Properly exit post-burst mode
        self.message_delay = self.original_message_delay  # Restore original delay when stopping
        self.current_burst_rate_limited = False  # Reset rate limit flag when stopping
        logger.info("🛑 Integrated burst mode stopped - restored original message delay")
    
    def send_to_frontend(self, data):
        """Send data to frontend via stdout"""
        try:
            print(json.dumps(data), flush=True)
        except Exception as e:
            logger.error(f"Failed to send to frontend: {e}")

class ZephyrBackend:
    def __init__(self):
        self.client = DiscordClient()
        self.loop = None
        
        # Autobeef system
        self.autobeef_active = False
        self.autobeef_task = None
        self.autobeef_delay = 5
        self.message_count = 0
        self.session_start_time = time.time()
        self.autobeef_start_time = None
        self.autobeef_session_time = 0
        
    def start(self):
        """Start the backend"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        # Start input processing in a separate thread
        input_thread = threading.Thread(
            target=self.process_input, daemon=True
        )
        input_thread.start()
        
        # Run the main loop
        try:
            self.loop.run_forever()
        except KeyboardInterrupt:
            pass
        finally:
            self.loop.run_until_complete(self.client.cleanup_connection())
    
    def process_input(self):
        """Process input from the main process"""
        while True:
            try:
                line = sys.stdin.readline()
                if not line:
                    break
                
                data = json.loads(line.strip())
                asyncio.run_coroutine_threadsafe(
                    self.handle_message(data), self.loop
                )
                
            except json.JSONDecodeError:
                logger.error("Invalid JSON received")
            except Exception as e:
                logger.error(f"Input processing error: {e}")
    
    async def handle_message(self, data):
        """Handle messages from frontend"""
        action = data.get('action')
        
        try:
            if action == 'connect':
                token = data.get('token', '').strip()
                channel_id = data.get('channel_id', '').strip()
                
                if not token:
                    raise Exception("Discord token is required")
                if not channel_id:
                    raise Exception("Channel ID is required")
                if not channel_id.isdigit():
                    raise Exception("Channel ID must be numeric")
                
                await self.client.connect(token, channel_id)
                
            elif action == 'disconnect':
                await self.client.disconnect()
                
            elif action == 'send_message':
                if not self.client.connected:
                    raise Exception("Not connected to Discord")
                    
                content = data.get('content', '').strip()
                if not content:
                    raise Exception("Message content cannot be empty")
                    
                delay = data.get('delay', 0)
                message_id = await self.client.add_message_to_queue(content, delay)
                
            elif action == 'pause_queue':
                self.client.pause_queue()
                
            elif action == 'resume_queue':
                self.client.resume_queue()
                
            elif action == 'clear_queue':
                cleared = await self.client.clear_queue()
                logger.info(f"Cleared {cleared} messages from queue")
                
            elif action == 'start_autobeef':
                delay = data.get('delay', 5)
                await self.start_autobeef(delay)
                
            elif action == 'stop_autobeef':
                await self.stop_autobeef()
                
            elif action == 'update_settings':
                settings = data.get('settings', {})
                if 'message_delay' in settings:
                    self.client.message_delay = float(settings['message_delay'])
                    logger.info(f"Updated message delay to {self.client.message_delay}s")
                    
            elif action == 'start_burst_mode':
                intervals = data.get('intervals', {})
                self.client.start_burst_mode(intervals)
                
            elif action == 'stop_burst_mode':
                self.client.stop_burst_mode()
                
            elif action == 'update_burst_intervals':
                intervals = data.get('intervals', {})
                if self.client.burst_mode_enabled:
                    # Update intervals and restart burst mode
                    self.client.stop_burst_mode()
                    self.client.start_burst_mode(intervals)
                else:
                    # Just update the intervals for next time
                    self.client.burst_intervals.update(intervals)
                    
            elif action == 'autobeef_send_message':
                # Handle autobeef message from frontend
                if not self.client.connected:
                    logger.warning("Cannot send autobeef message - not connected")
                    return
                    
                content = data.get('content', '').strip()
                if content:
                    await self.client.add_message_to_queue(content, 0)
                    self.message_count += 1
                    
            elif action == 'health_check':
                await self.handle_health_check()
                
            elif action == 'priority_mode':
                # Handle priority mode toggle
                enabled = data.get('enabled', False)
                logger.info(f"🔥 Priority mode {'enabled' if enabled else 'disabled'}")
                self.client.send_to_frontend({
                    'type': 'priority_mode_status',
                    'enabled': enabled
                })
                
            elif action == 'spam_mode':
                # Handle spam mode toggle
                enabled = data.get('enabled', False)
                message = data.get('message', '').strip()
                if enabled and message:
                    logger.info(f"💥 Spam mode enabled with message: {message}")
                    self.client.send_to_frontend({
                        'type': 'spam_mode_status',
                        'enabled': True,
                        'message': message
                    })
                else:
                    logger.info("💥 Spam mode disabled")
                    self.client.send_to_frontend({
                        'type': 'spam_mode_status',
                        'enabled': False
                    })
                    
            elif action == 'burst_message':
                # Handle burst message (30 simultaneous messages)
                if not self.client.connected:
                    logger.warning("Cannot send burst message - not connected")
                    return
                    
                content = data.get('content', '').strip()
                if content:
                    # Send message directly without queue (bypass rate limiting for burst)
                    await self.send_burst_message(content)
                    
            elif action == 'priority_message':
                # Handle priority message (single message, bypass queue)
                if not self.client.connected:
                    logger.warning("Cannot send priority message - not connected")
                    return
                    
                content = data.get('content', '').strip()
                if content:
                    # Send message directly without queue
                    await self.send_priority_message(content)
                    
            else:
                logger.warning(f"Unknown action: {action}")
                
        except Exception as e:
            logger.error(f"Error handling message: {e}")
            if action == 'connect':
                self.client.send_to_frontend({
                    'type': 'connection_error',
                    'error': str(e)
                })
    
    async def start_autobeef(self, delay):
        """Start autobeef functionality"""
        if not self.client.connected:
            logger.error("Cannot start autobeef - not connected to Discord")
            return
            
        if self.autobeef_active:
            logger.info("Autobeef already running")
            return
            
        self.autobeef_active = True
        self.autobeef_delay = delay
        self.autobeef_start_time = time.time()
        
        # Start autobeef task
        self.autobeef_task = asyncio.create_task(self._autobeef_loop())
        
        # Send confirmation to frontend
        self.client.send_to_frontend({
            'type': 'autobeef_started',
            'delay': delay
        })
        
        # Start session stats task
        asyncio.create_task(self._session_stats_loop())
        
        logger.info(f"🤖 Autobeef started with {delay}s delay")
    
    async def stop_autobeef(self):
        """Stop autobeef functionality"""
        if not self.autobeef_active:
            logger.info("Autobeef not running")
            return
            
        self.autobeef_active = False
        
        # Cancel autobeef task
        if self.autobeef_task:
            self.autobeef_task.cancel()
            try:
                await self.autobeef_task
            except asyncio.CancelledError:
                pass
            self.autobeef_task = None
        
        # Update session time
        if self.autobeef_start_time:
            self.autobeef_session_time += time.time() - self.autobeef_start_time
            self.autobeef_start_time = None
        
        # Send confirmation to frontend
        self.client.send_to_frontend({
            'type': 'autobeef_stopped'
        })
        
        logger.info("🛑 Autobeef stopped")
    
    async def _autobeef_loop(self):
        """Main autobeef loop - requests messages from frontend"""
        try:
            while self.autobeef_active and self.client.connected:
                try:
                    # Request a message from frontend wordlist system
                    self.client.send_to_frontend({
                        'type': 'autobeef_request_message'
                    })
                    
                    # Wait for next message
                    await asyncio.sleep(self.autobeef_delay)
                    
                except Exception as e:
                    logger.error(f"Error in autobeef loop: {e}")
                    await asyncio.sleep(1)
                    
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Autobeef loop error: {e}")
    
    async def _session_stats_loop(self):
        """Send session statistics to frontend"""
        try:
            while self.autobeef_active:
                current_time = time.time()
                total_session_time = current_time - self.session_start_time
                
                # Calculate autobeef session time
                autobeef_time = self.autobeef_session_time
                if self.autobeef_start_time:
                    autobeef_time += current_time - self.autobeef_start_time
                
                # Calculate WPM (rough estimate)
                wpm = 0
                if total_session_time > 0:
                    wpm = (self.message_count * 5) / (total_session_time / 60)  # Assume 5 words per message
                
                # Send stats to frontend
                self.client.send_to_frontend({
                    'type': 'session_stats',
                    'total_session_time': total_session_time,
                    'autobeef_session_time': autobeef_time,
                    'message_count': self.message_count,
                    'wpm': wpm
                })
                
                await asyncio.sleep(1)  # Update every second
                
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Session stats loop error: {e}")
    
    async def handle_health_check(self):
        """Handle health check request from frontend"""
        logger.info("🔍 Health check requested from frontend")
        
        try:
            # Check if client is connected
            if not self.client.connected:
                self.client.send_to_frontend({
                    'type': 'connection_warning',
                    'message': 'Backend reports: Not connected to Discord'
                })
                return
            
            # Check WebSocket connection
            if not self.client.websocket or self.client.websocket.closed:
                logger.warning("⚠️ WebSocket is closed - attempting to restart connection")
                self.client.send_to_frontend({
                    'type': 'connection_warning',
                    'message': 'WebSocket connection lost - attempting recovery'
                })
                
                # Try to restart the message listener
                if self.client.message_listener_task and self.client.message_listener_task.done():
                    self.client.message_listener_task = asyncio.create_task(self.client._message_listener_with_health_check())
                    logger.info("🔄 Message listener restarted due to health check")
                
                return
            
            # Send a test API call to verify connection
            try:
                await self.client._send_connection_test()
                
                # If we get here, connection is healthy
                self.client.send_to_frontend({
                    'type': 'connection_recovered',
                    'message': 'Health check passed - connection is healthy'
                })
                logger.info("✅ Health check passed")
                
            except Exception as api_error:
                logger.error(f"❌ Health check API test failed: {api_error}")
                self.client.send_to_frontend({
                    'type': 'connection_warning',
                    'message': f'Health check failed: {str(api_error)}'
                })
                
        except Exception as e:
            logger.error(f"❌ Health check error: {e}")
            self.client.send_to_frontend({
                'type': 'connection_error',
                'message': f'Health check failed: {str(e)}'
            })
    
    async def send_burst_message(self, content):
        """Send burst message using threading for simultaneous delivery"""
        import threading
        import concurrent.futures
        
        logger.info(f"💥 Sending burst message 30 times: {content[:50]}...")
        
        def send_single_message():
            """Send a single message synchronously"""
            try:
                import requests
                payload = {"content": content}
                response = requests.post(
                    f"{self.client.base_url}/channels/{self.client.channel_id}/messages",
                    json=payload,
                    headers={"Authorization": self.client.token},
                    timeout=10
                )
                return response.status_code in [200, 201]
            except Exception as e:
                logger.error(f"Burst message thread error: {e}")
                return False
        
        # Use ThreadPoolExecutor to send 30 messages simultaneously
        with concurrent.futures.ThreadPoolExecutor(max_workers=30) as executor:
            # Submit all 30 tasks at once
            futures = [executor.submit(send_single_message) for _ in range(30)]
            
            # Wait for all to complete
            successful = 0
            for future in concurrent.futures.as_completed(futures):
                try:
                    if future.result():
                        successful += 1
                except Exception as e:
                    logger.error(f"Burst future error: {e}")
        
        logger.info(f"💥 Burst complete: {successful}/30 messages sent successfully")
        
        # Send confirmation to frontend
        self.client.send_to_frontend({
            'type': 'burst_complete',
            'successful': successful,
            'total': 30,
            'content': content
        })
    
    async def send_priority_message(self, content):
        """Send priority message directly (bypass queue)"""
        logger.info(f"🔥 Sending priority message: {content[:50]}...")
        
        try:
            # Send message directly using the client's send_message method
            await self.client.send_message(content, delay=0)
            logger.info("🔥 Priority message sent successfully")
            
            # Send confirmation to frontend
            self.client.send_to_frontend({
                'type': 'priority_message_sent',
                'content': content
            })
            
        except Exception as e:
            logger.error(f"❌ Failed to send priority message: {e}")
            self.client.send_to_frontend({
                'type': 'priority_message_error',
                'error': str(e)
            })

if __name__ == "__main__":
    backend = ZephyrBackend()
    backend.start()