import asyncio
import json
import sys
import time
import threading
import queue
from datetime import datetime, timedelta
import logging
import aiohttp
import websockets

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DiscordClient:
    def __init__(self):
        self.token = None
        self.channel_id = None
        self.guild_id = None
        self.user = None
        self.session = None
        self.websocket = None
        self.connected = False
        
        # Heartbeat system
        self.heartbeat_task = None
        self.heartbeat_interval = None
        self.sequence = None
        
        # Enhanced Message queue system
        self.message_queue = []
        self.queue_paused = False
        self.queue_processing = False
        self.queue_lock = asyncio.Lock()
        self.queue_processor_task = None
        
        # Rate limiting
        self.last_message_time = 0
        self.message_count = 0
        
        # Message listener
        self.message_listener_task = None
        self.needs_reconnect = False
        self.last_message_received = time.time()
        
        self.base_url = "https://discord.com/api/v10"
        self.gateway_url = None
    
    async def connect(self, token, channel_id):
        """Connect to Discord with proper error handling"""
        self.token = token
        self.channel_id = channel_id
        
        try:
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                headers={"Authorization": self.token},
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            logger.info("🔐 Verifying Discord token...")
            await self.verify_token()
            logger.info(f"✅ Token verified for user: {self.user['username']}")
            
            logger.info(f"🔍 Checking channel access: {channel_id}")
            channel_info = await self.get_channel_info()
            logger.info(f"✅ Channel found: {channel_info.get('name', 'Unknown')}")
            
            logger.info("🌐 Getting Discord gateway URL...")
            await self.get_gateway_url()
            logger.info(f"✅ Gateway URL obtained")
            
            logger.info("🔌 Connecting to Discord Gateway...")
            await self.connect_gateway()
            logger.info("✅ Successfully connected to Discord Gateway")
            
            # Get display name and avatar
            display_name = self.user.get('global_name') or self.user['username']
            avatar_url = f"https://cdn.discordapp.com/avatars/{self.user['id']}/{self.user['avatar']}.png" if self.user.get('avatar') else None
            
            # Send success message
            self.send_to_frontend({
                'type': 'connection_success',
                'user': {
                    'id': self.user['id'],
                    'username': self.user['username'],
                    'display_name': display_name,
                    'discriminator': self.user['discriminator'],
                    'avatar': avatar_url,
                    'global_name': self.user.get('global_name')
                },
                'channel_name': channel_info.get('name', 'Unknown Channel')
            })
            
            logger.info("🚀 Discord connection fully established!")
            
        except Exception as e:
            logger.error(f"❌ Connection failed: {e}")
            self.connected = False
            await self.cleanup_connection()
            self.send_to_frontend({
                'type': 'connection_error',
                'error': str(e)
            })
    
    async def verify_token(self):
        """Verify the Discord token and get user information"""
        try:
            async with self.session.get(f"{self.base_url}/users/@me") as response:
                if response.status == 401:
                    raise Exception("Invalid Discord token - please check your token")
                elif response.status == 403:
                    raise Exception("Token forbidden - token may be disabled")
                elif response.status == 429:
                    raise Exception("Rate limited - please wait and try again")
                elif response.status != 200:
                    raise Exception(f"Failed to verify token - HTTP {response.status}")
                
                self.user = await response.json()
                
        except aiohttp.ClientError as e:
            raise Exception(f"Network error during token verification: {e}")
    
    async def get_channel_info(self):
        """Get channel information and verify access"""
        try:
            async with self.session.get(f"{self.base_url}/channels/{self.channel_id}") as response:
                if response.status == 401:
                    raise Exception("Invalid token for channel access")
                elif response.status == 403:
                    raise Exception("No permission to access this channel")
                elif response.status == 404:
                    raise Exception("Channel not found - check the channel ID")
                elif response.status != 200:
                    raise Exception(f"Failed to get channel info - HTTP {response.status}")
                
                channel_data = await response.json()
                self.guild_id = channel_data.get('guild_id')
                return channel_data
                
        except aiohttp.ClientError as e:
            raise Exception(f"Network error getting channel info: {e}")
    
    async def get_gateway_url(self):
        """Get the Discord Gateway URL"""
        try:
            async with self.session.get(f"{self.base_url}/gateway") as response:
                if response.status != 200:
                    raise Exception(f"Failed to get gateway URL - HTTP {response.status}")
                
                gateway_data = await response.json()
                self.gateway_url = f"{gateway_data['url']}/?v=10&encoding=json"
                
        except aiohttp.ClientError as e:
            raise Exception(f"Network error getting gateway URL: {e}")
    
    async def connect_gateway(self):
        """Connect to Discord Gateway WebSocket with proper error handling"""
        try:
            # Connect to WebSocket
            self.websocket = await asyncio.wait_for(
                websockets.connect(
                    self.gateway_url,
                    max_size=2**24,
                    max_queue=128,
                    ping_interval=20,
                    ping_timeout=10,
                    close_timeout=10
                ),
                timeout=15.0
            )
            
            # Send identify payload
            identify_payload = {
                "op": 2,
                "d": {
                    "token": self.token,
                    "intents": 33281,
                    "properties": {
                        "$os": "windows",
                        "$browser": "chrome",
                        "$device": "desktop"
                    },
                    "compress": False,
                    "large_threshold": 50,
                    "presence": {
                        "status": "online",
                        "since": None,
                        "activities": [],
                        "afk": False
                    }
                }
            }
            
            await self.websocket.send(json.dumps(identify_payload))
            
            # Handle initial messages
            await asyncio.wait_for(self._handle_initial_messages(), timeout=30.0)
            
            # Start continuous message listener with reconnection handling
            self.message_listener_task = asyncio.create_task(self._message_listener_with_reconnect())
            
        except asyncio.TimeoutError:
            raise Exception("Connection timeout - Discord gateway unreachable")
        except Exception as e:
            raise Exception(f"Failed to connect to Discord gateway: {e}")
    
    async def _handle_initial_messages(self):
        """Handle initial gateway messages until READY is received"""
        while True:
            try:
                message = await self.websocket.recv()
                data = json.loads(message)
                
                op = data.get('op')
                event_type = data.get('t')
                
                if op == 10:  # Hello
                    heartbeat_interval = data['d']['heartbeat_interval']
                    self.heartbeat_interval = heartbeat_interval / 1000.0
                    
                    # Start heartbeat task
                    self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
                    
                elif op == 0 and event_type == 'READY':
                    self.connected = True
                    ready_data = data.get('d', {})
                    if 'user' in ready_data:
                        self.user.update(ready_data['user'])
                    return
                    
                elif op == 9:  # Invalid Session
                    raise Exception("Invalid session - token may be invalid or expired")
                    
                elif op == 7:  # Reconnect
                    raise Exception("Discord requested reconnect - please try again")
                    
            except websockets.exceptions.ConnectionClosed:
                raise Exception("WebSocket connection closed during handshake")
            except json.JSONDecodeError:
                continue
    
    async def _heartbeat_loop(self):
        """Send heartbeat messages to keep connection alive"""
        try:
            while self.connected and self.websocket and not self.websocket.closed:
                await asyncio.sleep(self.heartbeat_interval)
                
                if not self.connected or not self.websocket or self.websocket.closed:
                    break
                
                heartbeat_payload = {"op": 1, "d": self.sequence}
                
                try:
                    await self.websocket.send(json.dumps(heartbeat_payload))
                except Exception:
                    break
                    
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Heartbeat error: {e}")
    
    async def _message_listener_with_reconnect(self):
        """Message listener with automatic reconnection handling"""
        while self.connected:
            try:
                await self._message_listener()
                
                # If we exit the message listener and need to reconnect
                if self.needs_reconnect and self.connected:
                    logger.info("🔄 Attempting automatic reconnection...")
                    self.needs_reconnect = False
                    
                    # Clean up current connection
                    if self.websocket and not self.websocket.closed:
                        await self.websocket.close()
                    
                    # Reconnect to gateway
                    try:
                        await self.connect_gateway()
                        logger.info("✅ Automatic reconnection successful")
                        continue
                    except Exception as e:
                        logger.error(f"❌ Automatic reconnection failed: {e}")
                        break
                else:
                    # Normal exit, don't reconnect
                    break
                    
            except Exception as e:
                logger.error(f"❌ Message listener error: {e}")
                if self.connected:
                    await asyncio.sleep(5)  # Wait before retrying
                else:
                    break
    
    async def _message_listener(self):
        """Listen for incoming Discord messages - simplified and reliable"""
        logger.info("🎧 Message listener started")
        
        try:
            while self.connected and self.websocket and not self.websocket.closed:
                try:
                    # Simple message receiving without aggressive timeouts
                    message = await self.websocket.recv()
                    data = json.loads(message)
                    
                    # Update sequence number
                    if data.get('s'):
                        self.sequence = data['s']
                    
                    op = data.get('op')
                    event_type = data.get('t')
                    
                    if op == 0:  # Dispatch
                        if event_type == 'MESSAGE_CREATE':
                            logger.debug(f"📨 Received message: {data['d'].get('content', '')[:50]}...")
                            self.last_message_received = time.time()
                            await self._handle_message_create(data['d'])
                        elif event_type == 'READY':
                            logger.info("🔄 Received READY event")
                    elif op == 11:  # Heartbeat ACK
                        logger.debug("💓 Heartbeat ACK received")
                        continue
                    elif op == 9:  # Invalid Session
                        logger.error("❌ Invalid session - connection will be reset")
                        break
                    elif op == 7:  # Reconnect
                        logger.info("🔄 Discord requested reconnect - attempting to reconnect")
                        self.needs_reconnect = True
                        break
                        
                except websockets.exceptions.ConnectionClosed as e:
                    logger.warning(f"🔌 WebSocket connection closed: {e}")
                    break
                except json.JSONDecodeError as e:
                    logger.warning(f"⚠️ Invalid JSON received: {e}")
                    continue
                except Exception as e:
                    logger.error(f"❌ Unexpected error in message listener: {e}")
                    # Don't break on unexpected errors, just continue
                    await asyncio.sleep(1)
                    continue
                    
        except asyncio.CancelledError:
            logger.info("🛑 Message listener cancelled")
        except Exception as e:
            logger.error(f"❌ Fatal message listener error: {e}")
        finally:
            logger.info("🔌 Message listener stopped")
    
    async def _handle_message_create(self, message_data):
        """Handle incoming Discord message"""
        try:
            # Only process messages from our channel
            if message_data.get('channel_id') != self.channel_id:
                return
            
            # Send message to frontend (including our own messages)
            self.send_to_frontend({
                'type': 'message_received',
                'message': {
                    'id': message_data.get('id'),
                    'content': message_data.get('content', ''),
                    'timestamp': message_data.get('timestamp'),
                    'edited_timestamp': message_data.get('edited_timestamp')
                },
                'user': {
                    'id': message_data.get('author', {}).get('id'),
                    'username': message_data.get('author', {}).get('username'),
                    'display_name': message_data.get('author', {}).get('global_name') or message_data.get('author', {}).get('username'),
                    'avatar': message_data.get('author', {}).get('avatar'),
                    'discriminator': message_data.get('author', {}).get('discriminator')
                }
            })
            
        except Exception as e:
            logger.error(f"Error handling message create: {e}")
    
    async def add_message_to_queue(self, content, delay=0):
        """Add message to queue for ordered processing"""
        async with self.queue_lock:
            message_id = f"msg_{int(time.time() * 1000)}_{len(self.message_queue)}"
            queue_item = {
                'id': message_id,
                'content': content,
                'delay': delay,
                'timestamp': time.time(),
                'retries': 0,
                'status': 'queued'
            }
            
            self.message_queue.append(queue_item)
            self.send_queue_size_update()
            
            # Start queue processor if not running
            if not self.queue_processing:
                self.queue_processor_task = asyncio.create_task(self._process_queue())
            
            return message_id
    
    async def _process_queue(self):
        """Process messages from queue in order"""
        self.queue_processing = True
        
        try:
            while self.message_queue and self.connected:
                if self.queue_paused:
                    await asyncio.sleep(0.1)
                    continue
                
                async with self.queue_lock:
                    if not self.message_queue:
                        break
                    
                    current_message = self.message_queue[0]
                
                try:
                    # Apply delay if specified
                    if current_message['delay'] > 0:
                        await asyncio.sleep(current_message['delay'])
                    
                    # Send the message
                    await self.send_message(current_message['content'], 0)
                    
                    # Remove from queue on success
                    async with self.queue_lock:
                        if self.message_queue and self.message_queue[0]['id'] == current_message['id']:
                            self.message_queue.pop(0)
                            self.send_queue_size_update()
                    
                except Exception as e:
                    logger.error(f"❌ Failed to send queue message: {e}")
                    
                    current_message['retries'] += 1
                    
                    if current_message['retries'] >= 3:
                        # Remove failed message after 3 retries
                        async with self.queue_lock:
                            if self.message_queue and self.message_queue[0]['id'] == current_message['id']:
                                self.message_queue.pop(0)
                                self.send_queue_size_update()
                    else:
                        # Retry after delay
                        await asyncio.sleep(2 ** current_message['retries'])
                
        except Exception as e:
            logger.error(f"Queue processor error: {e}")
        finally:
            self.queue_processing = False
    
    def send_queue_size_update(self):
        """Send queue size update to frontend"""
        size = len(self.message_queue)
        self.send_to_frontend({
            'type': 'queue_size_update',
            'size': size
        })
    
    async def send_message(self, content, delay=0):
        """Send message to Discord channel"""
        if not self.connected:
            raise Exception("Not connected to Discord")
        
        if delay > 0:
            await asyncio.sleep(delay)
        
        # Rate limiting
        current_time = time.time()
        if current_time - self.last_message_time < 1:
            await asyncio.sleep(1 - (current_time - self.last_message_time))
        
        try:
            payload = {"content": content}
            async with self.session.post(
                f"{self.base_url}/channels/{self.channel_id}/messages",
                json=payload
            ) as response:
                if response.status == 429:
                    retry_after = (await response.json()).get('retry_after', 1)
                    await asyncio.sleep(retry_after)
                    return await self.send_message(content, 0)
                elif response.status not in [200, 201]:
                    raise Exception(f"Failed to send message - HTTP {response.status}")
                
                self.last_message_time = time.time()
                self.message_count += 1
                
                self.send_to_frontend({
                    'type': 'message_sent',
                    'content': content,
                    'timestamp': datetime.now().isoformat()
                })
                
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise
    
    async def cleanup_connection(self):
        """Clean up connection resources"""
        try:
            # Cancel all tasks
            tasks_to_cancel = [
                ('heartbeat_task', self.heartbeat_task),
                ('message_listener_task', self.message_listener_task),
                ('queue_processor_task', self.queue_processor_task)
            ]
            
            for task_name, task in tasks_to_cancel:
                if task:
                    logger.info(f"🛑 Cancelling {task_name}")
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                    except Exception as e:
                        logger.error(f"❌ Error cancelling {task_name}: {e}")
            
            # Close WebSocket
            if self.websocket:
                logger.info("🔌 Closing WebSocket")
                await self.websocket.close()
            
            # Close HTTP session
            if self.session:
                logger.info("🔌 Closing HTTP session")
                await self.session.close()
                
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
    
    async def disconnect(self):
        """Disconnect from Discord"""
        self.connected = False
        await self.cleanup_connection()
        
        self.websocket = None
        self.session = None
        self.channel_id = None
        self.guild_id = None
        self.user = None
        
        self.send_to_frontend({'type': 'disconnected'})
        logger.info("✅ Disconnected from Discord")
    
    def pause_queue(self):
        """Pause message queue"""
        self.queue_paused = True
        self.send_to_frontend({'type': 'queue_paused'})
    
    def resume_queue(self):
        """Resume message queue"""
        self.queue_paused = False
        self.send_to_frontend({'type': 'queue_resumed'})
    
    async def clear_queue(self):
        """Clear message queue"""
        async with self.queue_lock:
            cleared_count = len(self.message_queue)
            self.message_queue.clear()
            self.send_queue_size_update()
        
        self.send_to_frontend({'type': 'queue_cleared'})
        return cleared_count
    
    def send_to_frontend(self, data):
        """Send data to frontend via stdout"""
        try:
            print(json.dumps(data), flush=True)
        except Exception as e:
            logger.error(f"Failed to send to frontend: {e}")

class ZephyrBackend:
    def __init__(self):
        self.client = DiscordClient()
        self.loop = None
        
        # Autobeef system
        self.autobeef_active = False
        self.autobeef_task = None
        self.autobeef_delay = 5
        self.message_count = 0
        self.session_start_time = time.time()
        self.autobeef_start_time = None
        self.autobeef_session_time = 0
        
    def start(self):
        """Start the backend"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        # Start input processing in a separate thread
        input_thread = threading.Thread(
            target=self.process_input, daemon=True
        )
        input_thread.start()
        
        # Run the main loop
        try:
            self.loop.run_forever()
        except KeyboardInterrupt:
            pass
        finally:
            self.loop.run_until_complete(self.client.cleanup_connection())
    
    def process_input(self):
        """Process input from the main process"""
        while True:
            try:
                line = sys.stdin.readline()
                if not line:
                    break
                
                data = json.loads(line.strip())
                asyncio.run_coroutine_threadsafe(
                    self.handle_message(data), self.loop
                )
                
            except json.JSONDecodeError:
                logger.error("Invalid JSON received")
            except Exception as e:
                logger.error(f"Input processing error: {e}")
    
    async def handle_message(self, data):
        """Handle messages from frontend"""
        action = data.get('action')
        
        try:
            if action == 'connect':
                token = data.get('token', '').strip()
                channel_id = data.get('channel_id', '').strip()
                
                if not token:
                    raise Exception("Discord token is required")
                if not channel_id:
                    raise Exception("Channel ID is required")
                if not channel_id.isdigit():
                    raise Exception("Channel ID must be numeric")
                
                await self.client.connect(token, channel_id)
                
            elif action == 'disconnect':
                await self.client.disconnect()
                
            elif action == 'send_message':
                if not self.client.connected:
                    raise Exception("Not connected to Discord")
                    
                content = data.get('content', '').strip()
                if not content:
                    raise Exception("Message content cannot be empty")
                    
                delay = data.get('delay', 0)
                message_id = await self.client.add_message_to_queue(content, delay)
                
            elif action == 'pause_queue':
                self.client.pause_queue()
                
            elif action == 'resume_queue':
                self.client.resume_queue()
                
            elif action == 'clear_queue':
                cleared = await self.client.clear_queue()
                logger.info(f"Cleared {cleared} messages from queue")
                
            elif action == 'start_autobeef':
                delay = data.get('delay', 5)
                await self.start_autobeef(delay)
                
            elif action == 'stop_autobeef':
                await self.stop_autobeef()
                
            elif action == 'update_settings':
                settings = data.get('settings', {})
                if 'message_delay' in settings:
                    # Handle message delay setting if needed
                    pass
                    
            elif action == 'autobeef_send_message':
                # Handle autobeef message from frontend
                if not self.client.connected:
                    logger.warning("Cannot send autobeef message - not connected")
                    return
                    
                content = data.get('content', '').strip()
                if content:
                    await self.client.add_message_to_queue(content, 0)
                    self.message_count += 1
                    
            else:
                logger.warning(f"Unknown action: {action}")
                
        except Exception as e:
            logger.error(f"Error handling message: {e}")
            if action == 'connect':
                self.client.send_to_frontend({
                    'type': 'connection_error',
                    'error': str(e)
                })
    
    async def start_autobeef(self, delay):
        """Start autobeef functionality"""
        if not self.client.connected:
            logger.error("Cannot start autobeef - not connected to Discord")
            return
            
        if self.autobeef_active:
            logger.info("Autobeef already running")
            return
            
        self.autobeef_active = True
        self.autobeef_delay = delay
        self.autobeef_start_time = time.time()
        
        # Start autobeef task
        self.autobeef_task = asyncio.create_task(self._autobeef_loop())
        
        # Send confirmation to frontend
        self.client.send_to_frontend({
            'type': 'autobeef_started',
            'delay': delay
        })
        
        # Start session stats task
        asyncio.create_task(self._session_stats_loop())
        
        logger.info(f"🤖 Autobeef started with {delay}s delay")
    
    async def stop_autobeef(self):
        """Stop autobeef functionality"""
        if not self.autobeef_active:
            logger.info("Autobeef not running")
            return
            
        self.autobeef_active = False
        
        # Cancel autobeef task
        if self.autobeef_task:
            self.autobeef_task.cancel()
            try:
                await self.autobeef_task
            except asyncio.CancelledError:
                pass
            self.autobeef_task = None
        
        # Update session time
        if self.autobeef_start_time:
            self.autobeef_session_time += time.time() - self.autobeef_start_time
            self.autobeef_start_time = None
        
        # Send confirmation to frontend
        self.client.send_to_frontend({
            'type': 'autobeef_stopped'
        })
        
        logger.info("🛑 Autobeef stopped")
    
    async def _autobeef_loop(self):
        """Main autobeef loop - requests messages from frontend"""
        try:
            while self.autobeef_active and self.client.connected:
                try:
                    # Request a message from frontend wordlist system
                    self.client.send_to_frontend({
                        'type': 'autobeef_request_message'
                    })
                    
                    # Wait for next message
                    await asyncio.sleep(self.autobeef_delay)
                    
                except Exception as e:
                    logger.error(f"Error in autobeef loop: {e}")
                    await asyncio.sleep(1)
                    
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Autobeef loop error: {e}")
    
    async def _session_stats_loop(self):
        """Send session statistics to frontend"""
        try:
            while self.autobeef_active:
                current_time = time.time()
                total_session_time = current_time - self.session_start_time
                
                # Calculate autobeef session time
                autobeef_time = self.autobeef_session_time
                if self.autobeef_start_time:
                    autobeef_time += current_time - self.autobeef_start_time
                
                # Calculate WPM (rough estimate)
                wpm = 0
                if total_session_time > 0:
                    wpm = (self.message_count * 5) / (total_session_time / 60)  # Assume 5 words per message
                
                # Send stats to frontend
                self.client.send_to_frontend({
                    'type': 'session_stats',
                    'total_session_time': total_session_time,
                    'autobeef_session_time': autobeef_time,
                    'message_count': self.message_count,
                    'wpm': wpm
                })
                
                await asyncio.sleep(1)  # Update every second
                
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Session stats loop error: {e}")

if __name__ == "__main__":
    backend = ZephyrBackend()
    backend.start()