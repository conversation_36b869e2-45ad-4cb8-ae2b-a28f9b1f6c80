import asyncio
import json
import sys
import time
import threading
import queue
import aiohttp
import websockets
import logging
from datetime import datetime
import uuid
import hashlib
import random

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebChatClient:
    def __init__(self):
        self.username = None
        self.room_id = None
        self.user_id = None
        self.session = None
        self.websocket = None
        self.connected = False
        self.message_queue = queue.Queue()
        self.chat_history = []
        self.online_users = []
        
        # Mock chat server
        self.chat_server_url = "wss://echo.websocket.org"
        self.api_base_url = "https://jsonplaceholder.typicode.com"
        
    async def connect(self, username, room_id):
        """Connect to the web chat"""
        self.username = username
        self.room_id = room_id
        self.user_id = self.generate_user_id(username)
        
        try:
            logger.info(f"🔌 Connecting to web chat as {username}...")
            
            # Create HTTP session
            self.session = aiohttp.ClientSession()
            
            # Simulate connection delay
            await asyncio.sleep(1)
            
            # Mark as connected
            self.connected = True
            
            # Send success message
            self.send_to_frontend({
                'type': 'connection_success',
                'user': {
                    'id': self.user_id,
                    'username': self.username,
                    'room': self.room_id
                },
                'channel_name': f"Web Chat - {self.room_id}"
            })
            
            logger.info(f"✅ Connected to web chat successfully!")
            
            # Start sending welcome messages
            await self.send_welcome_messages()
            
        except Exception as e:
            logger.error(f"❌ Connection error: {e}")
            self.send_to_frontend({
                'type': 'connection_error',
                'error': str(e)
            })
    
    def generate_user_id(self, username):
        """Generate a unique user ID"""
        return hashlib.md5(f"{username}_{time.time()}".encode()).hexdigest()[:8]
    
    async def send_welcome_messages(self):
        """Send some welcome messages to show the chat is working"""
        welcome_messages = [
            {
                "id": str(uuid.uuid4()),
                "content": f"Welcome to the chat, {self.username}! 👋",
                "author": {
                    "id": "system",
                    "username": "System",
                    "avatar": "https://via.placeholder.com/40/007bff/ffffff?text=S",
                    "color": "#007bff"
                },
                "timestamp": datetime.now().isoformat(),
                "type": "system"
            },
            {
                "id": str(uuid.uuid4()),
                "content": "This is a demo web chat. Your messages will appear here!",
                "author": {
                    "id": "bot001",
                    "username": "ChatBot",
                    "avatar": "https://via.placeholder.com/40/28a745/ffffff?text=B",
                    "color": "#28a745"
                },
                "timestamp": datetime.now().isoformat(),
                "type": "bot"
            }
        ]
        
        for msg in welcome_messages:
            self.send_to_frontend({
                'type': 'message_received',
                **msg
            })
            await asyncio.sleep(0.5)
    
    async def send_chat_message(self, content):
        """Send a chat message"""
        if not self.connected:
            logger.error("❌ Not connected to chat")
            return
        
        try:
            logger.info(f"📤 Sending message: {content[:50]}...")
            
            # Create message object
            message = {
                "id": str(uuid.uuid4()),
                "content": content,
                "author": {
                    "id": self.user_id,
                    "username": self.username,
                    "avatar": f"https://via.placeholder.com/40/6c757d/ffffff?text={self.username[0].upper()}",
                    "color": "#6c757d"
                },
                "timestamp": datetime.now().isoformat(),
                "type": "user"
            }
            
            # Send to frontend immediately
            self.send_to_frontend({
                'type': 'message_received',
                **message
            })
            
            # Send confirmation
            self.send_to_frontend({
                'type': 'message_sent',
                'content': content,
                'timestamp': datetime.now().isoformat()
            })
            
            # Maybe send a bot response
            await self.maybe_send_bot_response(content)
            
            logger.info("✅ Message sent successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to send message: {e}")
    
    async def maybe_send_bot_response(self, original_message):
        """Sometimes send a bot response"""
        if random.random() < 0.4:  # 40% chance
            await asyncio.sleep(random.uniform(1, 3))
            
            responses = [
                "That's interesting! 🤔",
                "I agree with that 👍",
                "Tell me more about that",
                "Nice point! 💯",
                "I see what you mean",
                "That's a good question ❓",
                "Exactly! 🎯",
                "Thanks for sharing! 😊",
                "That makes sense 💡",
                f"@{self.username} great message!"
            ]
            
            bot_users = [
                {"id": "bot1", "username": "Alice", "color": "#e74c3c"},
                {"id": "bot2", "username": "Bob", "color": "#3498db"},
                {"id": "bot3", "username": "Charlie", "color": "#2ecc71"},
                {"id": "bot4", "username": "Diana", "color": "#f39c12"},
            ]
            
            bot = random.choice(bot_users)
            response = random.choice(responses)
            
            bot_message = {
                "id": str(uuid.uuid4()),
                "content": response,
                "author": {
                    "id": bot["id"],
                    "username": bot["username"],
                    "avatar": f"https://via.placeholder.com/40/{bot['color'][1:]}/ffffff?text={bot['username'][0]}",
                    "color": bot["color"]
                },
                "timestamp": datetime.now().isoformat(),
                "type": "bot"
            }
            
            self.send_to_frontend({
                'type': 'message_received',
                **bot_message
            })
    
    def add_message_to_queue(self, message_data):
        """Add message to queue"""
        content = message_data.get('content', '')
        if content.strip():
            asyncio.create_task(self.send_chat_message(content))
    
    async def disconnect(self):
        """Disconnect from chat"""
        try:
            self.connected = False
            
            if self.session:
                await self.session.close()
            
            self.send_to_frontend({
                'type': 'disconnected'
            })
            
            logger.info("🔌 Disconnected from web chat")
            
        except Exception as e:
            logger.error(f"❌ Error during disconnect: {e}")
    
    def send_to_frontend(self, data):
        """Send data to frontend"""
        try:
            print(json.dumps(data), flush=True)
        except Exception as e:
            logger.error(f"❌ Error sending to frontend: {e}")

class ZephyrBackend:
    def __init__(self):
        self.client = WebChatClient()
        self.loop = None
        
    def start(self):
        """Start the backend"""
        logger.info("🚀 Starting Zephyr Web Chat Backend...")
        
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        # Start input processing
        input_thread = threading.Thread(target=self.process_input, daemon=True)
        input_thread.start()
        
        # Run main loop
        try:
            self.loop.run_forever()
        except KeyboardInterrupt:
            logger.info("🛑 Backend shutting down...")
        finally:
            self.loop.close()
    
    def process_input(self):
        """Process input from frontend"""
        logger.info("🎧 Input processing thread started")
        
        while True:
            try:
                line = sys.stdin.readline()
                if not line:
                    logger.info("📪 No more input, breaking")
                    break
                
                line = line.strip()
                if not line:
                    continue
                    
                logger.info(f"📨 Raw input received: {line}")
                
                try:
                    data = json.loads(line)
                    logger.info(f"📨 Parsed JSON: {data}")
                    
                    # Schedule the coroutine in the event loop
                    future = asyncio.run_coroutine_threadsafe(
                        self.handle_frontend_message(data),
                        self.loop
                    )
                    
                    # Wait for completion with timeout
                    try:
                        future.result(timeout=5.0)
                    except asyncio.TimeoutError:
                        logger.error("⏰ Message handling timed out")
                        
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Invalid JSON: {e}")
                    logger.error(f"❌ Raw line was: {repr(line)}")
                    
            except Exception as e:
                logger.error(f"❌ Input processing error: {e}")
                import traceback
                traceback.print_exc()
    
    async def handle_frontend_message(self, data):
        """Handle messages from frontend"""
        action = data.get('action')
        logger.info(f"🎯 Handling action: {action}")
        
        try:
            if action == 'connect':
                username = data.get('token', 'User')[:20] or 'Anonymous'
                room_id = data.get('channel_id', 'general') or 'general'
                
                logger.info(f"🔌 Connecting as '{username}' to room '{room_id}'")
                await self.client.connect(username, room_id)
                
            elif action == 'send_message':
                content = data.get('content', '').strip()
                if content:
                    logger.info(f"📤 Sending message: {content[:30]}...")
                    self.client.add_message_to_queue({'content': content})
                else:
                    logger.warning("⚠️ Empty message content")
                    
            elif action == 'disconnect':
                logger.info("🔌 Disconnecting...")
                await self.client.disconnect()
                
            else:
                logger.warning(f"⚠️ Unknown action: {action}")
                
        except Exception as e:
            logger.error(f"❌ Error handling message: {e}")
            import traceback
            traceback.print_exc()
            
            if action == 'connect':
                self.client.send_to_frontend({
                    'type': 'connection_error',
                    'error': str(e)
                })

if __name__ == "__main__":
    backend = ZephyrBackend()
    backend.start()



