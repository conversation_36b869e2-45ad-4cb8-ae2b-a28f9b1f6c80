{"name": "zephyr-chat-server", "version": "1.0.0", "description": "Real-time chat server for Zephyr", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"socket.io": "^4.7.5", "express": "^4.18.2", "cors": "^2.8.5", "uuid": "^9.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "sqlite3": "^5.1.6", "helmet": "^7.1.0", "rate-limiter-flexible": "^3.0.8"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["chat", "websocket", "real-time"], "author": "Zephyr Team", "license": "MIT"}