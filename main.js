const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

let mainWindow;
let pythonProcess;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    frame: false,
    titleBarStyle: 'hidden'
  });

  mainWindow.loadFile('renderer/index.html');
  
  // Start Python backend
  startPythonBackend();
}

function startPythonBackend() {
  const pythonScript = path.join(__dirname, 'backend', 'discord_client_fixed_v2.py');
  pythonProcess = spawn('python', [pythonScript], {
    stdio: ['pipe', 'pipe', 'pipe'],
    encoding: 'utf8'
  });

  pythonProcess.stdout.on('data', (data) => {
    const lines = data.toString().split('\n');
    
    lines.forEach(line => {
      if (line.trim()) {
        try {
          const message = JSON.parse(line.trim());
          console.log('Discord message received:', message);
          
          if (mainWindow && mainWindow.webContents) {
            mainWindow.webContents.send('python-message', message);
          }
        } catch (error) {
          console.error('Error parsing Discord message:', error);
        }
      }
    });
  });

  pythonProcess.stderr.on('data', (data) => {
    console.log('Discord client log:', data.toString());
  });

  pythonProcess.on('close', (code) => {
    console.log(`Discord client exited with code ${code}`);
    pythonProcess = null;
  });

  pythonProcess.on('error', (error) => {
    console.error('Python process error:', error);
  });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (pythonProcess) {
    pythonProcess.kill();
  }
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC Handlers
ipcMain.handle('send-to-python', async (event, data) => {
  console.log('Sending to Python:', data);

  // Debug emoji specifically
  if (data.action === 'set_autoreact' && data.emoji) {
    console.log('🔍 Main process emoji debug:');
    console.log('  Original emoji:', data.emoji);
    console.log('  Emoji type:', typeof data.emoji);
    console.log('  Emoji length:', data.emoji.length);
    console.log('  Emoji codepoints:', [...data.emoji].map(c => c.codePointAt(0).toString(16)));
  }

  return new Promise((resolve, reject) => {
    if (pythonProcess && pythonProcess.stdin && pythonProcess.stdin.writable) {
      try {
        const jsonData = JSON.stringify(data) + '\n';

        // Debug the JSON data being sent
        if (data.action === 'set_autoreact' && data.emoji) {
          console.log('🔍 JSON being sent to Python:', jsonData.trim());

          // Debug the actual bytes being sent
          const buffer = Buffer.from(jsonData, 'utf8');
          console.log('🔍 Buffer bytes:', buffer);
          console.log('🔍 Buffer as hex:', buffer.toString('hex'));
        }

        // Ensure we're writing UTF-8 encoded data
        const buffer = Buffer.from(jsonData, 'utf8');
        pythonProcess.stdin.write(buffer);
        resolve({ success: true });
      } catch (error) {
        console.error('Error writing to Python process:', error);
        reject(error);
      }
    } else {
      console.error('Python process not available');
      reject(new Error('Python process not available'));
    }
  });
});

ipcMain.handle('save-token-file', async (event, token) => {
  try {
    const tokenPath = path.join(__dirname, 'maintoken.txt');
    fs.writeFileSync(tokenPath, token, 'utf8');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('read-token-file', async (event) => {
  try {
    const tokenPath = path.join(__dirname, 'maintoken.txt');
    if (fs.existsSync(tokenPath)) {
      const token = fs.readFileSync(tokenPath, 'utf8').trim();
      return { success: true, token };
    }
    return { success: false, error: 'Token file not found' };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('write-wordlist-file', async (event, content) => {
  try {
    const wordlistPath = path.join(__dirname, 'wordlist.txt');
    fs.writeFileSync(wordlistPath, content, 'utf8');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('read-wordlist-file', async (event) => {
  try {
    const wordlistPath = path.join(__dirname, 'wordlist.txt');
    if (fs.existsSync(wordlistPath)) {
      const content = fs.readFileSync(wordlistPath, 'utf8');
      return { success: true, content };
    }
    return { success: false, error: 'Wordlist file not found' };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('write-wordindex-file', async (event, content) => {
  try {
    const indexPath = path.join(__dirname, 'wordindex.txt');
    fs.writeFileSync(indexPath, content, 'utf8');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('read-wordindex-file', async (event) => {
  try {
    const indexPath = path.join(__dirname, 'wordindex.txt');
    if (fs.existsSync(indexPath)) {
      const content = fs.readFileSync(indexPath, 'utf8');
      return { success: true, content };
    }
    return { success: false, error: 'Word index file not found' };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('minimize-window', async (event) => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('close-window', async (event) => {
  if (mainWindow) {
    mainWindow.close();
  }
});

// Additional wordlist handlers
ipcMain.handle('save-wordlist', async (event, wordsPhrases) => {
  try {
    const wordlistPath = path.join(__dirname, 'wordlist.txt');
    const content = wordsPhrases.join('\n');
    fs.writeFileSync(wordlistPath, content, 'utf8');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('load-wordlist', async (event) => {
  try {
    const wordlistPath = path.join(__dirname, 'wordlist.txt');
    if (fs.existsSync(wordlistPath)) {
      const content = fs.readFileSync(wordlistPath, 'utf8');
      const wordsPhrases = content.split('\n').filter(line => line.trim());
      return { success: true, wordsPhrases };
    }
    return { success: true, wordsPhrases: [] };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

