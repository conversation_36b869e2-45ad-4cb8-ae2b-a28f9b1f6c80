{"name": "zephyr-v6", "version": "6.0.0", "description": "Zephyr v6 - Advanced Discord Client", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder"}, "keywords": ["discord", "client", "electron", "zephyr"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"electron": "^37.2.3", "electron-builder": "^24.6.4"}, "dependencies": {"axios": "^1.6.0", "ws": "^8.14.2"}, "build": {"appId": "com.zephyr.v6", "productName": "Zephyr v6", "directories": {"output": "dist"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"]}}