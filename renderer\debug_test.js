// Add this to the browser console to test message content processing
function debugMessageContent() {
    console.log('🔍 DEBUGGING MESSAGE CONTENT PROCESSING');
    
    // Override the processMessageContent function temporarily
    if (window.zephyrClient) {
        const originalProcessMessageContent = 
            window.zephyrClient.processMessageContent;
        
        window.zephyrClient.processMessageContent = function(data) {
            console.log('🚨 DEBUG: Processing message content');
            console.log('📝 Raw data.content:', JSON.stringify(data.content));
            console.log('📊 Content type:', typeof data.content);
            console.log(
                '📏 Content length:', 
                data.content ? data.content.length : 'N/A'
            );
            console.log('👤 Author:', data.author?.username);
            console.log('🎯 Mentions:', data.mentions);
            console.log('💬 Full message:', JSON.stringify(data, null, 2));
            
            const result = originalProcessMessageContent.call(this, data);
            
            console.log('✅ Processed result:', result);
            console.log('🔍 hasText:', result.hasText);
            console.log('📝 text:', result.text);
            
            return result;
        };
        
        console.log(
            '✅ Debug override installed. Send some messages to see detailed logging.'
        );
    } else {
        console.log('❌ ZephyrClient not found. Make sure the app is loaded.');
    }
}

// Also override buildContentHTML for debugging
function debugContentHTML() {
    if (window.zephyrClient) {
        const originalBuildContentHTML = window.zephyrClient.buildContentHTML;
        
        window.zephyrClient.buildContentHTML = function(content) {
            console.log('🏗️ DEBUG: Building content HTML');
            console.log('📊 Content object:', content);
            console.log('✅ hasText:', content.hasText);
            console.log('📝 text:', content.text);
            
            let html = '';
            
            // Force show content for debugging
            if (content.hasText) {
                html += `<div class="message-text" style="color: #dcddde; 
                    line-height: 1.4; word-wrap: break-word; margin: 4px 0;">
                    ${this.formatMessageContent(content.text)}
                </div>`;
                console.log('✅ Added text content to HTML');
            } else {
                // Show debug info instead of "no content"
                html += `<div class="message-text" style="color: #ff6b6b; 
                    font-style: italic; margin: 4px 0; 
                    background: rgba(255,0,0,0.1); padding: 4px;">
                    <strong>DEBUG:</strong> hasText=${content.hasText}, 
                    text="${content.text}", len=${content.text?.length || 0}
                </div>`;
                console.log('⚠️ No text content, showing debug info');
            }
            
            // Add other content types
            if (content.attachments.length > 0) {
                html += this.renderAttachments(content.attachments);
            }
            if (content.embeds.length > 0) {
                html += this.renderEmbeds(content.embeds);
            }
            if (content.poll) {
                html += this.renderPoll(content.poll);
            }
            if (content.thread) {
                html += this.renderThreadInfo(content.thread);
            }
            if (content.reactions.length > 0) {
                html += this.renderReactions(content.reactions);
            }
            
            console.log('🏗️ Final HTML:', html);
            return html;
        };
        
        console.log('✅ Content HTML debug override installed.');
    }
}

console.log(
    '🔧 Debug functions loaded. Run debugMessageContent() and ' +
    'debugContentHTML() to enable debugging.'
);