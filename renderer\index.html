<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zephyr Reworked v2</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="discord-app">
        <!-- Left Sidebar -->
        <div class="left-sidebar">
            <div class="server-header">
                <div class="server-name">
                    <span id="serverName">Zephyr</span>
                </div>
                <div class="connection-status" id="connectionStatus">
                    <i class="fas fa-circle"></i>
                    <span>Disconnected</span>
                </div>
            </div>

            <!-- Configuration Section -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-cog"></i>
                    <span>Configuration</span>
                </div>
                
                <div class="config-form">
                    <div class="input-group">
                        <label for="tokenInput">User Token</label>
                        <div class="token-input-wrapper">
                            <input type="password" id="tokenInput" placeholder="Enter your Discord token">
                            <button class="toggle-visibility" id="toggleToken">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <button class="btn btn-purple" id="saveToken">Save Token</button>
                    </div>
                    
                    <div class="input-group">
                        <label for="channelInput">Channel ID</label>
                        <input type="text" id="channelInput" placeholder="Enter channel ID">
                        <button class="btn btn-success" id="connectChannel">Connect</button>
                    </div>
                </div>
            </div>

            <!-- Feature Tabs -->
            <div class="sidebar-section">
                <div class="section-header">
                    <i class="fas fa-tools"></i>
                    <span>Features</span>
                </div>
                
                <div class="feature-tabs">
                    <div class="feature-tab active" data-tab="manual">
                        <i class="fas fa-keyboard"></i>
                        <span>Manual</span>
                    </div>
                    <div class="feature-tab" data-tab="keybinds">
                        <i class="fas fa-key"></i>
                        <span>Keybinds</span>
                    </div>
                    <div class="feature-tab" data-tab="autobeef">
                        <i class="fas fa-robot"></i>
                        <span>Autobeef</span>
                    </div>
                    <div class="feature-tab" data-tab="autoreact">
                        <i class="fas fa-skull"></i>
                        <span>Autoreact</span>
                    </div>
                    <div class="feature-tab" data-tab="queue">
                        <i class="fas fa-ellipsis-h"></i>
                        <span>Queue</span>
                    </div>
                    <div class="feature-tab" data-tab="wordlist">
                        <i class="fas fa-file-text"></i>
                        <span>Wordlist</span>
                    </div>
                    <div class="feature-tab" data-tab="typo-adder">
                        <i class="fas fa-spell-check"></i>
                        <span>Typo Adder</span>
                    </div>
                    <div class="feature-tab" data-tab="burst">
                        <i class="fas fa-bolt"></i>
                        <span>Burst</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="chat-area">
            <!-- Chat Header -->
            <div class="chat-header">
                <div class="channel-info">
                    <i class="fas fa-hashtag"></i>
                    <span id="channelName"></span>
                </div>
                <div class="header-controls">
                    <button class="header-btn" id="minimizeBtn">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button class="header-btn close-btn" id="closeBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Messages Container -->
            <div class="messages-container" id="messagesContainer">
                <!-- Messages will be populated here -->
            </div>

            <!-- Message Input -->
            <div class="message-input-area">
                <div class="typing-indicator" id="typingIndicator" style="display: none;">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <span class="typing-text">Someone is typing...</span>
                </div>
                
                <div class="message-input-container">
                    <div class="input-wrapper">
                        <textarea 
                            id="messageInput" 
                            placeholder="Type a message..." 
                            rows="1"
                        ></textarea>
                        <div class="input-controls">
                            <div class="queue-info">
                                <span class="queue-label">Queue:</span>
                                <span id="queueCountInput" class="queue-count">0</span>
                            </div>
                            <button class="send-btn" id="sendButton">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Message Options Bar -->
                    <div class="message-options" id="messageOptions">
                        <div class="option-group">
                            <label class="option-toggle">
                                <input type="checkbox" id="spacingEnabled">
                                <span>Spacing</span>
                                <span class="delay-display" id="delayDisplay">0.1s</span>
                            </label>
                            <input type="range" id="messageDelay" min="0.1" max="3.0" step="0.1" value="0.1" class="delay-slider">
                        </div>
                        
                        <div class="option-group">
                            <label class="option-toggle">
                                <input type="checkbox" id="prefixSuffixEnabled">
                                <span>Prefix/Suffix</span>
                            </label>
                        </div>
                        
                        <div class="wpm-counter">
                            <i class="fas fa-tachometer-alt"></i>
                            <span id="wpmCount">0</span> WPM
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Feature Panel -->
        <div class="feature-panel" id="featurePanel">
            <!-- Manual Panel -->
            <div class="tab-content active" id="manual-content">
                <div class="panel-header">
                    <h3><i class="fas fa-keyboard"></i> Manual Controls</h3>
                    <button class="close-panel" id="closePanel">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="panel-body">
                    <!-- Prefix/Suffix Settings -->
                    <div class="settings-group">
                        <h4>Prefix & Suffix Settings</h4>
                        <div class="input-row">
                            <input type="text" id="messagePrefix" placeholder="Message prefix">
                            <input type="text" id="messageSuffix" placeholder="Message suffix">
                        </div>
                    </div>
                    

                </div>
            </div>

            <!-- Keybinds Panel -->
            <div class="tab-content" id="keybinds-content">
                <div class="panel-header">
                    <h3><i class="fas fa-key"></i> Keybinds</h3>
                    <button class="close-panel" id="closePanel">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="panel-body">
                    <div class="settings-group">
                        <h4>Keybind Settings</h4>
                        <div class="keybind-list">
                            <div class="keybind-item">
                                <span>Pause Queue:</span>
                                <input type="text" class="keybind-input" id="pauseKeybind" readonly>
                                <button class="btn btn-small" id="setPauseKeybind">Set</button>
                            </div>
                            <div class="keybind-item">
                                <span>Toggle Spacing:</span>
                                <input type="text" class="keybind-input" id="spacingKeybind" readonly>
                                <button class="btn btn-small" id="setSpacingKeybind">Set</button>
                            </div>
                            <div class="keybind-item">
                                <span>Autobeef Toggle:</span>
                                <input type="text" class="keybind-input" id="autobeefKeybind" value="escape" readonly>
                                <button class="btn btn-small" id="setAutobeefKeybind">Set</button>
                            </div>
                            <div class="keybind-item">
                                <span>Burst Mode:</span>
                                <input type="text" class="keybind-input" id="burstKeybind" readonly>
                                <button class="btn btn-small" id="setBurstKeybind">Set</button>
                            </div>
                            <div class="keybind-item">
                                <span>Priority Message:</span>
                                <input type="text" class="keybind-input" id="priorityKeybind" readonly>
                                <button class="btn btn-small" id="setPriorityKeybind">Set</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Autobeef Panel -->
            <div class="tab-content" id="autobeef-content">
                <div class="panel-header">
                    <h3><i class="fas fa-robot"></i> Autobeef System</h3>
                    <button class="close-panel" id="closePanel">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="panel-body">
                    <div class="autobeef-status-section">
                        <div class="autobeef-status">
                            <div class="status-dot" id="autobeefStatusDot"></div>
                            <span id="autobeefStatusText">Stopped</span>
                        </div>
                    </div>
                    
                    <div class="settings-group">
                        <h4>Settings</h4>
                        <div class="range-setting">
                            <label>Delay: <span id="autobeefDelayDisplay">5s</span></label>
                            <input type="range" id="autobeefDelay" min="1" max="60" value="5">
                        </div>
                        <div class="checkbox-setting">
                            <label>
                                <input type="checkbox" id="autobeefBypassQueue">
                                <span>Bypass Queue (Direct Send)</span>
                            </label>
                            <small class="setting-note">Send messages directly without queue. Failed messages won't retry.</small>
                        </div>
                        <div class="checkbox-setting">
                            <label>
                                <input type="checkbox" id="autobeefNoRepeatUntil98" checked>
                                <span>No Repeat Until 98% Used</span>
                            </label>
                            <small class="setting-note">Don't repeat messages until 98% of wordlist is used (optional)</small>
                        </div>
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" id="messagesSent">0</div>
                            <div class="stat-label">Messages Sent</div>
                            <button class="btn btn-small btn-danger" id="clearMessagesSent">Clear</button>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="sessionTime">00:00</div>
                            <div class="stat-label">Session Time</div>
                        </div>
                    </div>
                    
                    <div class="autobeef-controls">
                        <button class="btn btn-success" id="autobeefToggle">Start Autobeef</button>
                    </div>
                </div>
            </div>

            <!-- Autoreact Panel -->
            <div class="tab-content" id="autoreact-content">
                <div class="panel-header">
                    <h3><i class="fas fa-skull"></i> Auto-React System</h3>
                    <button class="close-panel" id="closePanel">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="panel-body">
                    <div class="settings-group">
                        <h4>Auto-React Settings</h4>
                        <div class="checkbox-setting">
                            <label>
                                <input type="checkbox" id="autoreactEnabled">
                                <span>Auto-React to Own Messages</span>
                            </label>
                            <small class="setting-note">Automatically react to your own messages in all channels.</small>
                        </div>
                        <div class="emoji-setting">
                            <label>Reaction Emoji:</label>
                            <input type="text" id="autoreactEmoji" value="" maxlength="10" placeholder="Enter emoji (optional)">
                            <small class="setting-note">Emoji to use for auto-reactions. Leave empty to disable reactions.</small>
                        </div>
                    </div>

                    <div class="info-box">
                        <i class="fas fa-info-circle"></i>
                        <div>
                            <h4>How Auto-React Works</h4>
                            <p>When enabled, this feature will automatically add your chosen emoji reaction to every message you send across all channels.</p>
                            <ul>
                                <li>Works in all channels, not just the current one</li>
                                <li>Reacts to messages sent by the main token</li>
                                <li>Uses the emoji you specify below</li>
                                <li>Runs independently of other features</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Queue Panel -->
            <div class="tab-content" id="queue-content">
                <div class="panel-header">
                    <h3><i class="fas fa-ellipsis-h"></i> Message Queue</h3>
                    <button class="close-panel" id="closePanel">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="panel-body">
                    <div class="queue-controls">
                        <div class="queue-info">
                            <span>Queue: <span id="queueCount">0</span></span>
                        </div>
                        <div class="queue-buttons">
                            <button class="btn btn-warning" id="pauseQueue">
                                <i class="fas fa-pause"></i> Pause
                            </button>
                            <button class="btn btn-danger" id="clearQueue">
                                <i class="fas fa-trash"></i> Clear
                            </button>
                        </div>
                    </div>
                    
                    <div class="queue-list" id="queueList">
                        <!-- Queue items will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Wordlist Panel -->
            <div class="tab-content" id="wordlist-content">
                <div class="panel-header">
                    <h3><i class="fas fa-file-text"></i> Wordlist Generator</h3>
                    <button class="close-panel" id="closePanel">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="panel-body">
                    <div class="wordlist-generator">
                        <!-- Add Words/Phrases Section -->
                        <div class="wordlist-input-section">
                            <h4>Add Words/Phrases</h4>
                            <div class="input-row">
                                <input type="text" id="newWord" placeholder="Enter word or phrase (e.g., 'UR MY SON')">
                                <button class="btn btn-blue" id="addWord">Add Word/Phrase</button>
                            </div>
                            <div class="input-row">
                                <button class="btn btn-secondary" id="loadIndex">Load Index</button>
                                <button class="btn btn-danger" id="clearWords">Clear All</button>
                            </div>
                        </div>
                        
                        <!-- Words/Phrases Preview Section -->
                        <div class="words-preview-section">
                            <div class="preview-column full-width">
                                <h4>Words/Phrases (<span id="wordCount">0</span>)</h4>
                                <div class="words-preview-container">
                                    <div id="wordsPreview" class="words-preview">
                                        <p class="no-words">No words/phrases added yet...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Generator Settings -->
                        <div class="generator-settings">
                            <h4>Generator Settings</h4>
                            <div class="settings-row">
                                <div class="range-setting">
                                    <label>Words per Sentence: <span id="wordsPerSentenceDisplay">5</span></label>
                                    <input type="range" id="wordsPerSentence" min="1" max="30" value="5">
                                </div>
                                <div class="range-setting">
                                    <label>Typo Chance: <span id="typoChanceDisplay">20%</span></label>
                                    <input type="range" id="typoChance" min="0" max="100" value="20">
                                </div>
                            </div>
                            <div class="settings-row">
                                <div class="range-setting">
                                    <label>Lines to Generate: <span id="linesToGenerateDisplay">1</span></label>
                                    <input type="range" id="linesToGenerate" min="1" max="10000" value="1">
                                </div>
                            </div>
                            <div class="settings-row">
                                <div class="checkbox-setting">
                                    <label>
                                        <input type="checkbox" id="noRepeatingLines">
                                        <span>No Repeating Lines</span>
                                    </label>
                                    <small class="setting-note">Generate unique lines only. Max unique combinations: <span id="maxUniqueCount">0</span></small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Generated Sentence Display -->
                        <div class="generated-sentence-section">
                            <h4>Generated Text</h4>
                            <div class="generated-sentence-container">
                                <textarea id="generatedSentence" class="generated-sentence" readonly>Click "Generate Text" to create random sentences</textarea>
                            </div>
                            <div class="generator-buttons">
                                <button class="btn btn-success" id="generateSentence">Generate Text</button>
                                <button class="btn btn-secondary" id="copyGenerated">Copy</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Typo Adder Panel -->
            <div class="tab-content" id="typo-adder-content">
                <div class="panel-header">
                    <h3><i class="fas fa-spell-check"></i> Typo Adder</h3>
                    <button class="close-panel" id="closePanel">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="panel-body">
                    <div class="typo-adder">
                        <!-- Input Section -->
                        <div class="typo-input-section">
                            <h4>Input Wordlist</h4>
                            <div class="input-row">
                                <textarea id="typoInputWordlist" placeholder="Enter your wordlist here (one word/phrase per line)..." rows="8"></textarea>
                            </div>
                            <div class="input-row">
                                <button class="btn btn-blue" id="loadFromWordlistGen">Load from Wordlist Generator</button>
                                <button class="btn btn-secondary" id="clearTypoInput">Clear Input</button>
                            </div>
                        </div>

                        <!-- Typo Settings -->
                        <div class="typo-settings">
                            <h4>Typo Settings</h4>
                            <div class="settings-row">
                                <div class="range-setting">
                                    <label>Typo Chance: <span id="typoAdderChanceDisplay">30%</span></label>
                                    <input type="range" id="typoAdderChance" min="0" max="100" value="30">
                                </div>
                                <div class="range-setting">
                                    <label>Typos per Word: <span id="typosPerWordDisplay">1</span></label>
                                    <input type="range" id="typosPerWord" min="1" max="5" value="1">
                                </div>
                            </div>
                            <div class="settings-row">
                                <div class="checkbox-setting">
                                    <label>
                                        <input type="checkbox" id="uniqueTyposMode">
                                        <span>Generate Unique Typos</span>
                                    </label>
                                    <small class="setting-note">Create multiple unique typo variations of the same wordlist</small>
                                </div>
                            </div>
                            <div class="settings-row" id="uniqueTyposSettings" style="display: none;">
                                <div class="range-setting">
                                    <label>Unique Variations: <span id="uniqueVariationsDisplay">5x</span></label>
                                    <input type="range" id="uniqueVariations" min="2" max="100" value="5">
                                </div>
                            </div>
                        </div>

                        <!-- Generated Typos Display -->
                        <div class="typo-output-section">
                            <h4>Generated Typos</h4>
                            <div class="typo-output-container">
                                <textarea id="typoOutput" class="typo-output" readonly>Click "Generate Typos" to create typos from your wordlist</textarea>
                            </div>
                            <div class="typo-buttons">
                                <button class="btn btn-success" id="generateTypos">Generate Typos</button>
                                <button class="btn btn-secondary" id="copyTypos">Copy Typos</button>
                                <button class="btn btn-purple" id="addToWordlistGen">Add to Wordlist Generator</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Burst Panel -->
            <div class="tab-content" id="burst-content">
                <div class="panel-header">
                    <h3><i class="fas fa-bolt"></i> Burst System</h3>
                    <button class="close-panel" id="closePanel">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="panel-body">
                    <!-- Burst Mode Settings -->
                    <div class="settings-group">
                        <h4>Burst Mode Settings</h4>
                        <div class="burst-mode-header">
                            <label class="burst-toggle">
                                <input type="checkbox" id="burstModeEnabled">
                                <span>Enable Burst Mode</span>
                            </label>
                            <div class="burst-status" id="burstStatus">
                                <div class="status-dot" id="burstStatusDot"></div>
                                <span id="burstStatusText">Disabled</span>
                            </div>
                        </div>

                        <div class="burst-settings" id="burstSettings">
                            <div class="burst-type-setting">
                                <label>Double Burst Interval: <span id="doubleBurstDisplay">10s</span></label>
                                <input type="range" id="doubleBurstInterval" min="5" max="120" value="10" step="1">
                                <small>Sends 2 messages simultaneously every interval</small>
                            </div>

                            <div class="burst-type-setting">
                                <label>Triple Burst Interval: <span id="tripleBurstDisplay">15s</span></label>
                                <input type="range" id="tripleBurstInterval" min="5" max="120" value="15" step="1">
                                <small>Sends 3 messages simultaneously every interval</small>
                            </div>

                            <div class="burst-type-setting">
                                <label>Quad Burst Interval: <span id="quadBurstDisplay">20s</span></label>
                                <input type="range" id="quadBurstInterval" min="5" max="120" value="20" step="1">
                                <small>Sends 4 messages simultaneously every interval</small>
                            </div>

                            <div class="burst-info">
                                <p><i class="fas fa-info-circle"></i> Burst mode requires message delay ≥ 2 seconds</p>
                                <p><i class="fas fa-exclamation-triangle"></i> Bursts will not overlap - each waits for others to complete</p>
                            </div>
                        </div>
                    </div>

                    <!-- Burst Type Selection -->
                    <div class="settings-group">
                        <h4>Burst Type</h4>
                        <div class="burst-type-selection">
                            <label class="radio-option">
                                <input type="radio" name="burstType" value="threading" id="burstTypeThreading" checked>
                                <span>Threading (Current)</span>
                                <small>Uses threading for simultaneous message sending</small>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="burstType" value="tasks" id="burstTypeTasks">
                                <span>Tasks (Fast Sequential)</span>
                                <small>Sends messages as fast as possible with 0.01s delay</small>
                            </label>
                        </div>
                    </div>

                    <!-- Mini Burst Feature -->
                    <div class="settings-group">
                        <h4>Mini Burst</h4>
                        <div class="mini-burst-header">
                            <label class="mini-burst-toggle">
                                <input type="checkbox" id="miniBurstEnabled">
                                <span>Enable Mini Burst</span>
                            </label>
                            <div class="mini-burst-status" id="miniBurstStatus">
                                <div class="status-dot" id="miniBurstStatusDot"></div>
                                <span id="miniBurstStatusText">Disabled</span>
                            </div>
                        </div>

                        <div class="mini-burst-settings" id="miniBurstSettings">
                            <div class="range-setting">
                                <label>Activation Delay: <span id="miniBurstActivationDisplay">5-10s</span></label>
                                <div class="range-group">
                                    <input type="range" id="miniBurstActivationMin" min="3" max="15" value="5" step="1">
                                    <span>to</span>
                                    <input type="range" id="miniBurstActivationMax" min="5" max="20" value="10" step="1">
                                </div>
                                <small>Random delay before mini burst activates</small>
                            </div>

                            <div class="range-setting">
                                <label>Duration: <span id="miniBurstDurationDisplay">2s</span></label>
                                <input type="range" id="miniBurstDuration" min="1" max="5" value="2" step="0.5">
                                <small>How long mini burst stays active</small>
                            </div>

                            <div class="mini-burst-info">
                                <p><i class="fas fa-info-circle"></i> Mini burst randomly activates after 5-10 seconds</p>
                                <p><i class="fas fa-clock"></i> Sets message delay to 0.01s for 2 seconds, then returns to normal</p>
                                <p><i class="fas fa-repeat"></i> Cycles continuously while enabled</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <script src="renderer.js"></script>
</body>
</html>
