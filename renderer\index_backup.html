
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zephyr - Discord Client</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <div class="header">
            <div class="header-left">
                <h1><i class="fas fa-wind"></i>Zephyr</h1>
            </div>
            <div class="header-right">
                <div class="connection-status disconnected" id="connectionStatus">
                    <i class="fas fa-circle"></i>
                    <span>Disconnected</span>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Configuration Section -->
                <div class="config-section">
                    <h3>Configuration</h3>
                    
                    <div class="input-group">
                        <label for="tokenInput">User Token</label>
                        <div class="token-input-container">
                            <input type="password" id="tokenInput" placeholder="Enter your Discord user token">
                            <button class="toggle-btn" id="toggleToken">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <button class="btn btn-primary" id="saveToken">Save Token</button>
                    </div>
                    
                    <div class="input-group">
                        <label for="channelInput">Channel ID</label>
                        <input type="text" id="channelInput" placeholder="Enter channel ID">
                        <button class="btn btn-success" id="connectChannel">Connect</button>
                    </div>
                </div>

                <!-- Tabs Section -->
                <div class="tabs-section">
                    <h3>Features</h3>
                    <div class="tabs-container">
                        <button class="tab-button" data-tab="manual">
                            <i class="fas fa-cog"></i>
                            <span>Manual</span>
                        </button>
                        <button class="tab-button" data-tab="autobeef">
                            <i class="fas fa-robot"></i>
                            <span>Autobeef</span>
                        </button>
                        <button class="tab-button" data-tab="queue">
                            <i class="fas fa-list"></i>
                            <span>Queue</span>
                        </button>
                        <button class="tab-button" data-tab="wordlist">
                            <i class="fas fa-file-text"></i>
                            <span>Wordlist Gen</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tab Panels -->
            <div class="tab-panels" id="tabPanels">
                <!-- Manual Tab Panel -->
                <div class="tab-panel" id="manual-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-cog"></i>Manual Controls</h3>
                        <button class="close-panel" data-panel="manual">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="panel-content">
                        <!-- Keybinds -->
                        <div class="feature-group">
                            <h4>Keybinds</h4>
                            <div class="control-item">
                                <label>Pause Queue Keybind:</label>
                                <div class="keybind-input">
                                    <input type="text" id="pauseKeybind" placeholder="Press any key..." readonly>
                                    <button class="btn btn-small btn-secondary" id="setPauseKeybind">Set</button>
                                    <button class="btn btn-small btn-danger" id="clearPauseKeybind">Clear</button>
                                </div>
                                <small class="keybind-note">Press the key to pause/resume the message queue</small>
                            </div>
                            
                            <div class="control-item">
                                <label>Spacing Toggle Keybind:</label>
                                <div class="keybind-input">
                                    <input type="text" id="spacingKeybind" placeholder="Press any key..." readonly>
                                    <button class="btn btn-small btn-secondary" id="setSpacingKeybind">Set</button>
                                    <button class="btn btn-small btn-danger" id="clearSpacingKeybind">Clear</button>
                                </div>
                                <small class="keybind-note">Press the key to toggle message spacing on/off</small>
                            </div>
                        </div>

                        <!-- Spacing Feature -->
                        <div class="feature-group">
                            <h4>Message Spacing</h4>
                            <div class="control-item">
                                <label>
                                    <input type="checkbox" id="spacingEnabled">
                                    Enable Message Spacing
                                </label>
                                <small class="feature-note">Prevents spam by enforcing minimum delay between messages</small>
                            </div>
                        </div>

                        <!-- Message Delay -->
                        <div class="feature-group">
                            <h4>Message Delay</h4>
                            <div class="control-item">
                                <label>Delay between messages:</label>
                                <div class="range-slider">
                                    <input type="range" id="messageDelaySlider" min="0.1" max="3.0" step="0.1" value="0.1">
                                    <span class="range-value" id="messageDelayValue">0.1s</span>
                                </div>
                                <small class="feature-note">Prevents spam by enforcing minimum delay between messages</small>
                            </div>
                        </div>

                        <!-- Prefix/Suffix Feature -->
                        <div class="feature-group">
                            <h4>Prefix & Suffix</h4>
                            <div class="control-item">
                                <label>
                                    <input type="checkbox" id="prefixSuffixEnabled">
                                    Enable Prefix/Suffix
                                </label>
                            </div>
                            
                            <div class="prefix-suffix-settings" id="prefixSuffixSettings" style="display: none;">
                                <!-- Prefix Settings -->
                                <div class="control-item">
                                    <label>
                                        <input type="checkbox" id="prefixEnabled" checked>
                                        Enable Prefix
                                    </label>
                                </div>
                                
                                <div class="control-item" id="prefixControls">
                                    <label for="messagePrefix">Message Prefix:</label>
                                    <input type="text" id="messagePrefix" placeholder="Text to add at the start of messages">
                                    
                                    <div style="margin-top: 8px;">
                                        <label>
                                            <input type="checkbox" id="prefixNewline">
                                            Add newline after prefix
                                        </label>
                                    </div>
                                </div>
                                
                                <!-- Suffix Settings -->
                                <div class="control-item">
                                    <label>
                                        <input type="checkbox" id="suffixEnabled" checked>
                                        Enable Suffix
                                    </label>
                                </div>
                                
                                <div class="control-item" id="suffixControls">
                                    <label for="messageSuffix">Message Suffix:</label>
                                    <input type="text" id="messageSuffix" placeholder="Text to add at the end of messages">
                                    
                                    <div style="margin-top: 8px;">
                                        <label>
                                            <input type="checkbox" id="suffixNewline">
                                            Add newline before suffix
                                        </label>
                                    </div>
                                </div>
                                
                                <!-- Advanced Options -->
                                <div class="control-item">
                                    <h5 style="color: #ffffff; margin-bottom: 10px;">Advanced Options</h5>
                                    
                                    <div style="margin-bottom: 8px;">
                                        <label>
                                            <input type="checkbox" id="prefixSuffixSpacing">
                                            Add spacing around prefix/suffix
                                        </label>
                                    </div>
                                    
                                    <div style="margin-bottom: 8px;">
                                        <label>
                                            <input type="checkbox" id="prefixSuffixRandomize">
                                            Randomize prefix/suffix from list (separate with |)
                                        </label>
                                    </div>
                                </div>
                                
                                <!-- Keybind -->
                                <div class="control-item">
                                    <label>Toggle Prefix/Suffix Keybind:</label>
                                    <div class="keybind-input">
                                        <input type="text" id="prefixSuffixKeybind" placeholder="Press any key..." readonly>
                                        <button class="btn btn-small btn-secondary" id="setPrefixSuffixKeybind">Set</button>
                                        <button class="btn btn-small btn-danger" id="clearPrefixSuffixKeybind">Clear</button>
                                    </div>
                                </div>
                                
                                <!-- Preview -->
                                <div class="control-item">
                                    <label>Preview:</label>
                                    <div id="prefixSuffixPreview" style="background: #2f3136; border: 1px solid #40444b; border-radius: 4px; padding: 8px; font-family: 'Courier New', monospace; color: #dcddde; min-height: 40px;">
                                        Type a message to see preview...
                                    </div>
                                </div>
                                
                                <div class="control-item">
                                    <small class="feature-note">When enabled, prefix and suffix will be automatically added to all messages. Use the keybind to quickly toggle on/off.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Autobeef Tab Panel -->
                <div class="tab-panel" id="autobeef-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-robot"></i>Autobeef</h3>
                        <button class="close-panel" data-panel="autobeef">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="panel-content">
                        <div class="feature-group">
                            <h4>Coming Soon</h4>
                            <div class="control-item">
                                <p style="color: #b9bbbe; font-style: italic;">Autobeef features will be added in a future update.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Queue Tab Panel -->
                <div class="tab-panel" id="queue-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-list"></i>Message Queue</h3>
                        <button class="close-panel" data-panel="queue">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="panel-content">
                        <div class="feature-group">
                            <h4>Queue Controls</h4>
                            <div class="control-item">
                                <button class="btn btn-warning" id="pauseQueue">
                                    <i class="fas fa-pause"></i> Pause
                                </button>
                                <button class="btn btn-danger" id="clearQueue">
                                    <i class="fas fa-trash"></i> Clear
                                </button>
                            </div>
                        </div>
                        
                        <div class="feature-group">
                            <h4>Queue Status</h4>
                            <div id="queueContent" class="queue-content">
                                <!-- Queue items will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Wordlist Generator Tab Panel -->
                <div class="tab-panel" id="wordlist-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-file-text"></i>Wordlist Generator</h3>
                        <button class="close-panel" data-panel="wordlist">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="panel-content">
                        <!-- Word Generation Settings -->
                        <div class="feature-group">
                            <h4>Generation Settings</h4>
                            <div class="control-item">
                                <label for="wordCount">Number of words to generate:</label>
                                <input type="number" id="wordCount" min="1" max="1000" value="10">
                            </div>
                            
                            <div class="control-item">
                                <label for="wordLength">Word length:</label>
                                <div class="range-slider">
                                    <input type="range" id="wordLengthSlider" min="3" max="15" step="1" value="6">
                                    <span class="range-value" id="wordLengthValue">6</span>
                                </div>
                            </div>
                            
                            <div class="control-item">
                                <label>Word type:</label>
                                <select id="wordType" style="width: 100%; padding: 8px; background: #40444b; border: 1px solid #202225; border-radius: 4px; color: #dcddde;">
                                    <option value="random">Random Letters</option>
                                    <option value="pronounceable">Pronounceable</option>
                                    <option value="numbers">Numbers Only</option>
                                    <option value="mixed">Letters + Numbers</option>
                                </select>
                            </div>
                        </div>

                        <!-- Generation Controls -->
                        <div class="feature-group">
                            <h4>Controls</h4>
                            <div class="control-item">
                                <button class="btn btn-primary" id="generateWords">
                                    <i class="fas fa-magic"></i> Generate Words
                                </button>
                                <button class="btn btn-secondary" id="clearWordlist">
                                    <i class="fas fa-trash"></i> Clear
                                </button>
                                <button class="btn btn-success" id="copyWordlist">
                                    <i class="fas fa-copy"></i> Copy All
                                </button>
                            </div>
                        </div>

                        <!-- Generated Words Display -->
                        <div class="feature-group">
                            <h4>Generated Words</h4>
                            <div class="control-item">
                                <textarea id="generatedWords" placeholder="Generated words will appear here..." 
                                         style="min-height: 200px; font-family: 'Courier New', monospace;" readonly></textarea>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="feature-group">
                            <h4>Quick Actions</h4>
                            <div class="control-item">
                                <button class="btn btn-small btn-info" id="sendRandomWord">
                                    <i class="fas fa-paper-plane"></i> Send Random Word
                                </button>
                                <button class="btn btn-small btn-warning" id="addToQueue">
                                    <i class="fas fa-plus"></i> Add All to Queue
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Container -->
            <div class="chat-container">
                <div class="channel-header" id="channelHeader">
                    <i class="fas fa-hashtag"></i>
                    <span>Select a channel</span>
                </div>
                
                <div class="messages-container" id="messagesContainer">
                    <div class="welcome-message">
                        <i class="fas fa-wind" style="font-size: 48px; color: #7289da;"></i>
                        <h2>Welcome to Zephyr</h2>
                        <p>Enter your token and channel ID to get started</p>
                    </div>
                </div>
                
                <div class="message-input-container">
                    <div class="typing-indicator" id="typingIndicator" style="display: none;">
                        <span>Someone is typing...</span>
                    </div>
                    <div class="input-wrapper">
                        <textarea id="messageInput" placeholder="Type a message..." rows="1"></textarea>
                        <div class="input-controls">
                            <button id="sendButton" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Bottom Counters -->
                    <div class="bottom-counters">
                        <div class="counter-item queue-counter">
                            <i class="fas fa-list"></i>
                            <span>Queue: <span id="queueCount">0</span></span>
                        </div>
                        <div class="counter-item wpm-counter">
                            <i class="fas fa-keyboard"></i>
                            <span>WPM: <span id="wpmCount">0</span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Context Menu -->
    <div id="contextMenu" class="context-menu" style="display: none;">
        <div class="context-item" id="copyMention">
            <i class="fas fa-at"></i>
            Copy Mention
        </div>
        <div class="context-item" id="copyId">
            <i class="fas fa-id-card"></i>
            Copy User ID
        </div>
    </div>

    <!-- Rate Limit Modal -->
    <div id="rateLimitModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-clock"></i> Rate Limited</h3>
            </div>
            <div class="modal-body">
                <p>You are being rate limited. Please wait before sending more messages.</p>
                <div class="rate-limit-info">
                    <div class="rate-limit-item">
                        <span>Time remaining:</span>
                        <span id="rateLimitCountdown">--</span>
                    </div>
                    <div class="rate-limit-item">
                        <span>Requests remaining:</span>
                        <span id="rateLimitRemaining">--</span>
                    </div>
                    <div class="rate-limit-item">
                        <span>Reset time:</span>
                        <span id="rateLimitReset">--</span>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="rateLimitProgress"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
        this.channelInput = document.getElementById('channelInput');
        this.messageInput = document.getElementById('messageInput');
        
        // Button elements
        this.toggleTokenBtn = document.getElementById('toggleToken');
        this.saveTokenBtn = document.getElementById('saveToken');
        this.connectChannelBtn = document.getElementById('connectChannel');
        this.sendButton = document.getElementById('sendButton');
        this.toggleQueueBtn = document.getElementById('toggleQueue');
        
        // Display elements
        this.connectionStatus = document.getElementById('connectionStatus');
        this.queueCounter = document.getElementById('queueCounter');
        this.queueCount = document.getElementById('queueCount');
        this.wpmCount = document.getElementById('wpmCount');
        this.channelHeader = document.getElementById('channelHeader');
        this.messagesContainer = document.getElementById('messagesContainer');
        this.typingIndicator = document.getElementById('typingIndicator');
        
        // New queue controls
        this.pauseQueueBtn = document.getElementById('pauseQueue');
        this.clearQueueBtn = document.getElementById('clearQueue');
        
        // Modal and context menu
        this.contextMenu = document.getElementById('contextMenu');
        this.queuePanel = document.getElementById('queuePanel');
        this.queueContent = document.getElementById('queueContent');
    }

    setupEventListeners() {
        // Token management
        this.toggleTokenBtn.addEventListener('click', () => this.toggleTokenVisibility());
        this.saveTokenBtn.addEventListener('click', () => this.saveToken());
        
        // Channel connection
        this.connectChannelBtn.addEventListener('click', () => this.connectToChannel());
        
        // Message sending
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                if (e.shiftKey) {
                    // Allow Shift+Enter for manual line breaks
                    return;
                } else {
                    // Regular Enter sends the message
                    e.preventDefault();
                    this.sendMessage();
                }
            }
        });
        
        // WPM tracking
        this.messageInput.addEventListener('input', () => this.trackTyping());
        
        // Queue controls
        if (this.pauseQueueBtn) {
            this.pauseQueueBtn.addEventListener('click', () => this.toggleQueuePause());
        }
        if (this.clearQueueBtn) {
            this.clearQueueBtn.addEventListener('click', () => this.clearQueue());
        }
        
        // Queue management
        if (this.toggleQueueBtn) {
            this.toggleQueueBtn.addEventListener('click', () => this.toggleQueuePanel());
        }
        
        // Context menu
        document.addEventListener('click', () => this.hideContextMenu());
        document.getElementById('copyMention').addEventListener('click', () => this.copyMention());
        document.getElementById('copyId').addEventListener('click', () => this.copyUserId());
        
        // Python backend messages
        ipcRenderer.on('python-message', (event, data) => {
            this.handlePythonMessage(data);
        });
        
        // Close modal on click outside (only if modal exists)
        this.rateLimitModal = document.getElementById('rateLimitModal');
        if (this.rateLimitModal) {
            this.rateLimitModal.addEventListener('click', (e) => {
                if (e.target === this.rateLimitModal) {
                    this.hideRateLimitModal();
                }
            });
        }
        
        // Initialize other modal elements
        this.rateLimitCountdown = document.getElementById('rateLimitCountdown');
        this.rateLimitProgress = document.getElementById('rateLimitProgress');
        this.rateLimitRemaining = document.getElementById('rateLimitRemaining');
        this.rateLimitReset = document.getElementById('rateLimitReset');
        
        // Manual tab event listeners
        this.setupManualTabListeners();
        
        // Global keybind listener
        document.addEventListener('keydown', (e) => this.handleGlobalKeybind(e));
    }

    async loadSavedToken() {
        try {
            const result = await ipcRenderer.invoke('read-token-file');
            if (result.success && result.token) {
                this.tokenInput.value = result.token;
                this.token = result.token;
                this.updateConnectionStatus('Token loaded', 'success');
                this.showNotification('Token loaded from file', 'success');
            }
        } catch (error) {
            console.error('Error loading token:', error);
        }
    }
    
    startWpmTracking() {
        // Initialize WPM display
        if (this.wpmCount) {
            this.wpmCount.textContent = '0';
        }
        
        // Update WPM every second
        this.wpmInterval = setInterval(() => {
            this.updateWpm();
        }, 1000);
    }
    
    trackTyping() {
        const now = Date.now();
        this.typingHistory.push(now);
        
        // Keep only last 15 seconds of typing data
        this.typingHistory = this.typingHistory.filter(time => now - time <= 15000);
    }
    
    updateWpm() {
        const now = Date.now();
        const recentTyping = this.typingHistory.filter(time => now - time <= 15000);
        
        // Calculate WPM based on keystrokes in last 15 seconds
        // Assuming average word length of 5 characters
        const wpm = Math.round((recentTyping.length / 5) * (60 / 15));
        
        if (this.wpmCount) {
            this.wpmCount.textContent = wpm;
        }
    }
    
    async toggleQueuePause() {
        this.queuePaused = !this.queuePaused;
        
        if (this.queuePaused) {
            this.pauseQueueBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
            this.pauseQueueBtn.classList.remove('btn-warning');
            this.pauseQueueBtn.classList.add('btn-success');
            
            // Send pause command to backend
            try {
                await ipcRenderer.invoke('send-to-python', {
                    action: 'pause_queue'
                });
            } catch (error) {
                console.error('Failed to pause queue:', error);
            }
        } else {
            this.pauseQueueBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
            this.pauseQueueBtn.classList.remove('btn-success');
            this.pauseQueueBtn.classList.add('btn-warning');
            
            // Send resume command to backend
            try {
                await ipcRenderer.invoke('send-to-python', {
                    action: 'resume_queue'
                });
            } catch (error) {
                console.error('Failed to resume queue:', error);
            }
        }
        
        this.updateQueueDisplay();
    }
    
    async clearQueue() {
        this.messageQueue = [];
        this.updateQueueDisplay();
        
        // Send clear command to backend
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'clear_queue'
            });
            this.showNotification('Queue cleared', 'success');
        } catch (error) {
            console.error('Failed to clear queue:', error);
            this.showNotification('Failed to clear queue: ' + error.message, 'error');
        }
    }
    
    updateQueueDisplay() {
        const queueSize = this.messageQueue.length;
        
        if (this.queueCount) {
            this.queueCount.textContent = queueSize;
        }
        
        if (this.queueCounter) {
            this.queueCounter.textContent = queueSize;
        }
        
        // Update queue panel if it exists
        if (this.queueContent) {
            this.queueContent.innerHTML = '';
            
            this.messageQueue.forEach((msg, index) => {
                const queueItem = document.createElement('div');
                queueItem.className = `queue-item ${msg.status || 'pending'}`;
                queueItem.innerHTML = `
                    <span class="queue-number">${index + 1}</span>
                    <span class="queue-message">${msg.content.substring(0, 50)}${msg.content.length > 50 ? '...' : ''}</span>
                    <span class="queue-status">${msg.status || 'pending'}</span>
                `;
                this.queueContent.appendChild(queueItem);
            });
        }
    }

    setupTabSystem() {
        // Get tab elements
        this.tabButtons = document.querySelectorAll('.tab-button');
        this.tabPanels = document.querySelectorAll('.tab-panel');
        this.closePanelButtons = document.querySelectorAll('.close-panel');
        this.tabPanelsContainer = document.getElementById('tabPanels');
        
        // Set up tab button event listeners
        this.tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.getAttribute('data-tab');
                this.openTab(tabName);
            });
        });
        
        // Set up close panel button event listeners
        this.closePanelButtons.forEach(button => {
            button.addEventListener('click', () => {
                const panelName = button.getAttribute('data-panel');
                this.closePanel(panelName);
            });
        });
        
        // Initially hide the tab panels container
        if (this.tabPanelsContainer) {
            this.tabPanelsContainer.style.display = 'none';
        }
    }
    
    openTab(tabName) {
        // Remove active class from all tab buttons
        this.tabButtons.forEach(button => {
            button.classList.remove('active');
        });
        
        // Add active class to clicked tab button
        const activeButton = document.querySelector(`[data-tab="${tabName}"]`);
        if (activeButton) {
            activeButton.classList.add('active');
        }
        
        // Hide all tab panels
        this.tabPanels.forEach(panel => {
            panel.classList.remove('active');
        });
        
        // Show the selected tab panel
        const activePanel = document.getElementById(`${tabName}-panel`);
        if (activePanel) {
            activePanel.classList.add('active');
            if (this.tabPanelsContainer) {
                this.tabPanelsContainer.style.display = 'flex';
            }
        }
    }
    
    closePanel(panelName) {
        // Hide the specific panel
        const panel = document.getElementById(`${panelName}-panel`);
        if (panel) {
            panel.classList.remove('active');
        }
        
        // Remove active state from corresponding tab button
        const tabButton = document.querySelector(`[data-tab="${panelName}"]`);
        if (tabButton) {
            tabButton.classList.remove('active');
        }
        
        // Check if any panels are still active
        const activePanels = document.querySelectorAll('.tab-panel.active');
        if (activePanels.length === 0 && this.tabPanelsContainer) {
            this.tabPanelsContainer.style.display = 'none';
        }
    }

    setupManualTabListeners() {
        // Keybind setup
        const setPauseKeybind = document.getElementById('setPauseKeybind');
        const clearPauseKeybind = document.getElementById('clearPauseKeybind');
        const setSpacingKeybind = document.getElementById('setSpacingKeybind');
        const clearSpacingKeybind = document.getElementById('clearSpacingKeybind');
        const pauseQueueKeybind = document.getElementById('pauseQueueKeybind');
        const spacingKeybind = document.getElementById('spacingKeybind');
        
        if (setPauseKeybind) {
            setPauseKeybind.addEventListener('click', () => this.setKeybind('pauseQueue'));
        }
        if (clearPauseKeybind) {
            clearPauseKeybind.addEventListener('click', () => this.clearKeybind('pauseQueue'));
        }
        if (setSpacingKeybind) {
            setSpacingKeybind.addEventListener('click', () => this.setKeybind('spacing'));
        }
        if (clearSpacingKeybind) {
            clearSpacingKeybind.addEventListener('click', () => this.clearKeybind('spacing'));
        }
        
        // Spacing toggle
        const spacingEnabled = document.getElementById('spacingEnabled');
        if (spacingEnabled) {
            spacingEnabled.addEventListener('change', (e) => {
                this.spacingEnabled = e.target.checked;
            });
        }
        
        // Message delay slider
        const messageDelaySlider = document.getElementById('messageDelaySlider');
        const messageDelayValue = document.getElementById('messageDelayValue');
        if (messageDelaySlider && messageDelayValue) {
            messageDelaySlider.addEventListener('input', (e) => {
                this.messageDelay = parseFloat(e.target.value);
                messageDelayValue.textContent = `${this.messageDelay}s`;
                
                // Restart normal sending with new delay if burst mode is active
                if (this.burstEnabled && this.normalSendingInterval) {
                    clearInterval(this.normalSendingInterval);
                    this.startNormalMessageSending();
                }
            });
        }
        
        // Burst settings
        const burstEnabled = document.getElementById('burstEnabled');
        const burstSettings = document.getElementById('burstSettings');
        if (burstEnabled && burstSettings) {
            burstEnabled.addEventListener('change', (e) => {
                this.burstEnabled = e.target.checked;
                burstSettings.style.display = e.target.checked ? 'block' : 'none';
                
                if (e.target.checked) {
                    this.startBurstSystem();
                } else {
                    this.stopBurstSystem();
                }
            });
        }
        
        // Burst timer sliders
        const doubleBurstTimer = document.getElementById('doubleBurstTimer');
        const doubleBurstValue = document.getElementById('doubleBurstValue');
        if (doubleBurstTimer && doubleBurstValue) {
            doubleBurstTimer.addEventListener('input', (e) => {
                this.burstSettings.timers.double = parseInt(e.target.value);
                doubleBurstValue.textContent = `${e.target.value}s`;
                this.updateBurstPreview();
                if (this.burstEnabled) this.restartBurstSystem();
            });
        }
        
        const tripleBurstTimer = document.getElementById('tripleBurstTimer');
        const tripleBurstValue = document.getElementById('tripleBurstValue');
        if (tripleBurstTimer && tripleBurstValue) {
            tripleBurstTimer.addEventListener('input', (e) => {
                this.burstSettings.timers.triple = parseInt(e.target.value);
                tripleBurstValue.textContent = `${e.target.value}s`;
                this.updateBurstPreview();
                if (this.burstEnabled) this.restartBurstSystem();
            });
        }
        
        const quadBurstTimer = document.getElementById('quadBurstTimer');
        const quadBurstValue = document.getElementById('quadBurstValue');
        if (quadBurstTimer && quadBurstValue) {
            quadBurstTimer.addEventListener('input', (e) => {
                this.burstSettings.timers.quad = parseInt(e.target.value);
                quadBurstValue.textContent = `${e.target.value}s`;
                this.updateBurstPreview();
                if (this.burstEnabled) this.restartBurstSystem();
            });
        }
        
        // Burst sequence drag and drop
        this.setupBurstDragAndDrop();
        
        // Burst buttons
        const resetBurstSequence = document.getElementById('resetBurstSequence');
        const previewBurstSequence = document.getElementById('previewBurstSequence');
        if (resetBurstSequence) {
            resetBurstSequence.addEventListener('click', () => this.resetBurstSequence());
        }
        if (previewBurstSequence) {
            previewBurstSequence.addEventListener('click', () => this.updateBurstPreview());
        }
        
        // No burst count inputs anymore - using timers instead
    }
    
    setKeybind(type) {
        const input = document.getElementById(`${type}Keybind`);
        if (!input) return;
        
        input.placeholder = 'Press any key...';
        input.focus();
        
        const handleKeyPress = (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            if (e.key === 'Backspace') {
                this.clearKeybind(type);
            } else {
                this.keybinds[type] = e.key.toLowerCase();
                input.value = e.key.toUpperCase();
                input.placeholder = 'Press any key...';
            }
            
            input.removeEventListener('keydown', handleKeyPress);
            input.blur();
        };
        
        input.addEventListener('keydown', handleKeyPress);
    }
    
    clearKeybind(type) {
        this.keybinds[type] = null;
        const input = document.getElementById(`${type}Keybind`);
        if (input) {
            input.value = '';
            input.placeholder = 'Press any key...';
        }
    }
    
    handleGlobalKeybind(e) {
        const key = e.key.toLowerCase();
        
        // Check for pause queue keybind
        if (this.keybinds.pauseQueue && key === this.keybinds.pauseQueue) {
            e.preventDefault();
            this.toggleQueuePause();
        }
        
        // Check for spacing keybind
        if (this.keybinds.spacing && key === this.keybinds.spacing) {
            e.preventDefault();
            this.spacingEnabled = !this.spacingEnabled;
            const spacingCheckbox = document.getElementById('spacingEnabled');
            if (spacingCheckbox) {
                spacingCheckbox.checked = this.spacingEnabled;
            }
        }
    }
    
    setupBurstDragAndDrop() {
        const burstSequence = document.getElementById('burstSequence');
        if (!burstSequence) return;
        
        let draggedElement = null;
        
        burstSequence.addEventListener('dragstart', (e) => {
            draggedElement = e.target.closest('.burst-item');
            if (draggedElement) {
                draggedElement.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
            }
        });
        
        burstSequence.addEventListener('dragend', (e) => {
            if (draggedElement) {
                draggedElement.classList.remove('dragging');
                draggedElement = null;
            }
        });
        
        burstSequence.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
        });
        
        burstSequence.addEventListener('drop', (e) => {
            e.preventDefault();
            const dropTarget = e.target.closest('.burst-item');
            
            if (draggedElement && dropTarget && draggedElement !== dropTarget) {
                const rect = dropTarget.getBoundingClientRect();
                const midpoint = rect.top + rect.height / 2;
                
                if (e.clientY < midpoint) {
                    burstSequence.insertBefore(draggedElement, dropTarget);
                } else {
                    burstSequence.insertBefore(draggedElement, dropTarget.nextSibling);
                }
                
                this.updateBurstSequenceFromDOM();
                this.updateBurstPreview();
            }
        });
    }
    
    updateBurstSequenceFromDOM() {
        const burstItems = document.querySelectorAll('.burst-item');
        this.burstSettings.sequence = [];
        
        burstItems.forEach(item => {
            const type = item.getAttribute('data-type');
            this.burstSettings.sequence.push(type);
        });
        
        // Restart burst system if it's active
        if (this.burstEnabled) {
            this.restartBurstSystem();
        }
    }
    
    resetBurstSequence() {
        this.burstSettings.sequence = ['double', 'triple', 'quad'];
        this.burstSettings.currentBurstIndex = 0;
        this.burstSettings.timers = {
            double: 5,
            triple: 15,
            quad: 30
        };
        
        // Update DOM sliders
        const doubleBurstTimer = document.getElementById('doubleBurstTimer');
        const tripleBurstTimer = document.getElementById('tripleBurstTimer');
        const quadBurstTimer = document.getElementById('quadBurstTimer');
        const doubleBurstValue = document.getElementById('doubleBurstValue');
        const tripleBurstValue = document.getElementById('tripleBurstValue');
        const quadBurstValue = document.getElementById('quadBurstValue');
        
        if (doubleBurstTimer) {
            doubleBurstTimer.value = 5;
            if (doubleBurstValue) doubleBurstValue.textContent = '5s';
        }
        if (tripleBurstTimer) {
            tripleBurstTimer.value = 15;
            if (tripleBurstValue) tripleBurstValue.textContent = '15s';
        }
        if (quadBurstTimer) {
            quadBurstTimer.value = 30;
            if (quadBurstValue) quadBurstValue.textContent = '30s';
        }
        
        // Reset DOM sequence order
        const burstSequence = document.getElementById('burstSequence');
        if (burstSequence) {
            burstSequence.innerHTML = `
                <div class="burst-item" data-type="double" draggable="true">
                    <div class="burst-handle">⋮⋮</div>
                    <div class="burst-info">
                        <span class="burst-type">Double</span>
                        <span class="burst-description">2 queued messages simultaneously</span>
                    </div>
                </div>
                <div class="burst-item" data-type="triple" draggable="true">
                    <div class="burst-handle">⋮⋮</div>
                    <div class="burst-info">
                        <span class="burst-type">Triple</span>
                        <span class="burst-description">3 queued messages simultaneously</span>
                    </div>
                </div>
                <div class="burst-item" data-type="quad" draggable="true">
                    <div class="burst-handle">⋮⋮</div>
                    <div class="burst-info">
                        <span class="burst-type">Quad</span>
                        <span class="burst-description">4 queued messages simultaneously</span>
                    </div>
                </div>
            `;
            this.setupBurstDragAndDrop();
        }
        
        if (this.burstEnabled) {
            this.restartBurstSystem();
        }
        this.updateBurstPreview();
    }
    
    updateBurstPreview() {
        const preview = document.getElementById('burstPreview');
        if (!preview) return;
        
        let previewText = 'Time-Based Burst Sequence:\n\n';
        
        this.burstSettings.sequence.forEach((burstType, index) => {
            const stepNum = index + 1;
            const timer = this.burstSettings.timers[burstType];
            const burstSize = burstType === 'double' ? 2 : burstType === 'triple' ? 3 : 4;
            
            previewText += `${stepNum}. Normal messaging for ${timer}s → `;
            previewText += `${burstType.toUpperCase()} burst (${burstSize} queued messages simultaneously)\n`;
        });
        
        previewText += '\nAfter completing all bursts, the cycle repeats.\n';
        previewText += 'Burst messages are sent with 0.00ms delay using Promise.all()';
        
        preview.textContent = previewText;
    }

    async saveToken() {
        const token = this.tokenInput.value.trim();
        if (!token) {
            this.showNotification('Please enter a user token', 'error');
            return;
        }

        try {
            const result = await ipcRenderer.invoke('save-token-file', token);
            if (result.success) {
                this.token = token;
                this.showNotification('Token saved successfully', 'success');
            } else {
                this.showNotification('Failed to save token: ' + result.error, 'error');
            }
        } catch (error) {
            this.showNotification('Error saving token: ' + error.message, 'error');
        }
    }

    toggleTokenVisibility() {
        const isPassword = this.tokenInput.type === 'password';
        this.tokenInput.type = isPassword ? 'text' : 'password';
        this.toggleTokenBtn.innerHTML = isPassword ? '<i class="fas fa-eye-slash"></i>' : '<i class="fas fa-eye"></i>';
    }

    async connectToChannel() {
        const channelId = this.channelInput.value.trim();
        const token = this.tokenInput.value.trim();
        
        if (!token) {
            this.showNotification('Please enter a user token', 'error');
            return;
        }
        
        if (!channelId) {
            this.showNotification('Please enter a channel ID', 'error');
            return;
        }

        this.channelId = channelId;
        this.token = token;
        
        this.updateConnectionStatus('Connecting...', 'connecting');
        this.connectChannelBtn.disabled = true;
        
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'connect',
                token: token,
                channel_id: channelId
            });
        } catch (error) {
            this.updateConnectionStatus('Connection failed', 'disconnected');
            this.connectChannelBtn.disabled = false;
            this.showNotification('Connection error: ' + error.message, 'error');
        }
    }

    async sendMessage() {
        let content = this.messageInput.value.trim();
        if (!content) return;
        
        if (!this.connected) {
            this.showNotification('Not connected to Discord. Please connect first.', 'error');
            return;
        }

        // Apply spacing feature
        if (this.spacingEnabled) {
            content = content.replace(/ /g, '\n');
        }

        // Check message delay
        const now = Date.now();
        const timeSinceLastMessage = now - this.lastMessageTime;
        const requiredDelay = this.messageDelay * 1000; // Convert to milliseconds
        
        if (timeSinceLastMessage < requiredDelay) {
            const remainingDelay = requiredDelay - timeSinceLastMessage;
            setTimeout(() => this.processSendMessage(content), remainingDelay);
            this.messageInput.value = '';
            return;
        }

        this.processSendMessage(content);
        this.messageInput.value = '';
    }

    async processSendMessage(content) {
        this.lastMessageTime = Date.now();

        // Handle burst messages
        if (this.burstEnabled) {
            // Add message to burst queue in order
            this.burstSettings.burstQueue.push(content);
            // Don't send immediately - wait for burst timer
        } else {
            // Normal sending with delay
            this.queueSingleMessage(content);
        }
    }

    startBurstSystem() {
        if (!this.burstEnabled) return;
        
        this.burstSettings.normalModeStartTime = Date.now();
        this.burstSettings.currentBurstIndex = 0;
        this.burstSettings.burstModeActive = false;
        this.burstSettings.burstQueue = [];
        
        // Start normal message sending interval
        this.startNormalMessageSending();
        
        this.scheduleNextBurst();
    }
    
    stopBurstSystem() {
        // Clear all burst timers
        this.burstTimers.forEach(timer => clearTimeout(timer));
        this.burstTimers.clear();
        
        // Clear normal sending interval
        if (this.normalSendingInterval) {
            clearInterval(this.normalSendingInterval);
            this.normalSendingInterval = null;
        }
        
        this.burstSettings.burstModeActive = false;
        this.burstSettings.burstQueue = [];
    }
    
    restartBurstSystem() {
        this.stopBurstSystem();
        if (this.burstEnabled) {
            this.startBurstSystem();
        }
    }
    
    startNormalMessageSending() {
        // Send messages from burst queue at regular intervals (message delay)
        this.normalSendingInterval = setInterval(() => {
            if (this.burstSettings.burstQueue.length > 0 && !this.queuePaused && !this.burstSettings.burstModeActive) {
                const message = this.burstSettings.burstQueue.shift(); // Get first message (in order)
                this.sendNormalMessage(message);
            }
        }, this.messageDelay * 1000);
    }
    
    sendNormalMessage(content) {
        const messageId = Date.now().toString();
        const queueItem = {
            id: messageId,
            content: content,
            timestamp: new Date(),
            status: 'pending',
            retries: 0,
            maxRetries: 3,
            isBurst: false,
            isNormalBurstMode: true
        };

        this.messageQueue.push(queueItem);
        this.updateQueueDisplay();
        
        this.sendQueuedMessage(queueItem);
    }
    
    scheduleNextBurst() {
        if (!this.burstEnabled) return;
        
        const currentBurstType = this.burstSettings.sequence[this.burstSettings.currentBurstIndex];
        if (!currentBurstType) {
            // Reset to beginning of sequence
            this.burstSettings.currentBurstIndex = 0;
            return this.scheduleNextBurst();
        }
        
        const timerDuration = this.burstSettings.timers[currentBurstType] * 1000; // Convert to ms
        
        const timerId = setTimeout(() => {
            this.executeBurst(currentBurstType);
        }, timerDuration);
        
        this.burstTimers.set(currentBurstType, timerId);
    }
    
    executeBurst(burstType) {
        if (!this.burstEnabled || this.queuePaused) {
            // Still schedule next burst even if paused
            this.moveToNextBurst();
            return;
        }
        
        const burstSize = burstType === 'double' ? 2 : burstType === 'triple' ? 3 : 4;
        
        // Temporarily stop normal sending during burst
        this.burstSettings.burstModeActive = true;
        
        // Get messages from burst queue (in order)
        const messagesToBurst = this.burstSettings.burstQueue.splice(0, burstSize);
        
        if (messagesToBurst.length > 0) {
            // Send burst messages with true simultaneity
            this.sendTrulySimultaneousBurst(messagesToBurst, burstType);
        }
        
        // Resume normal sending after a brief moment
        setTimeout(() => {
            this.burstSettings.burstModeActive = false;
        }, 100);
        
        this.moveToNextBurst();
    }
    
    moveToNextBurst() {
        // Move to next burst in sequence
        this.burstSettings.currentBurstIndex++;
        if (this.burstSettings.currentBurstIndex >= this.burstSettings.sequence.length) {
            this.burstSettings.currentBurstIndex = 0; // Loop back to start
        }
        
        // Schedule next burst
        this.scheduleNextBurst();
    }
    
    async sendTrulySimultaneousBurst(messages, burstType) {
        const baseId = Date.now();
        const burstItems = [];
        
        // Prepare all queue items first
        messages.forEach((content, index) => {
            const messageId = `${baseId}_${index}`;
            const queueItem = {
                id: messageId,
                content: content,
                timestamp: new Date(),
                status: 'pending',
                retries: 0,
                maxRetries: 3,
                isBurst: true,
                burstType: burstType,
                burstGroup: baseId
            };
            
            this.messageQueue.push(queueItem);
            burstItems.push(queueItem);
        });
        
        this.updateQueueDisplay();
        
        try {
            // Use requestAnimationFrame for maximum precision timing
            await new Promise((resolve) => {
                requestAnimationFrame(async () => {
                    // Create all promises at the exact same moment
                    const burstPromises = burstItems.map(queueItem => {
                        // Use immediate IPC call for true simultaneity
                        return ipcRenderer.invoke('send-to-python', {
                            action: 'send_message',
                            content: queueItem.content,
                            message_id: queueItem.id,
                            is_burst: true,
                            burst_type: burstType
                        });
                    });
                    
                    // Execute all promises simultaneously
                    try {
                        await Promise.all(burstPromises);
                        console.log(`${burstType.toUpperCase()} burst sent successfully (${messages.length} messages) - TRUE SIMULTANEITY`);
                        resolve();
                    } catch (error) {
                        console.error(`${burstType.toUpperCase()} burst send error:`, error);
                        // Update failed items
                        burstItems.forEach(item => {
                            this.updateQueueItemStatus(item.id, 'failed');
                        });
                        resolve();
                    }
                });
            });
        } catch (error) {
            console.error(`Burst execution error:`, error);
        }
    }

    queueSingleMessage(content) {
        const messageId = Date.now().toString();
        const queueItem = {
            id: messageId,
            content: content,
            timestamp: new Date(),
            status: 'pending',
            retries: 0,
            maxRetries: 3,
            isBurst: false
        };

        this.messageQueue.push(queueItem);
        this.updateQueueDisplay();
        
        if (!this.queuePaused) {
            this.sendQueuedMessage(queueItem);
        }
    }

    async sendQueuedMessage(queueItem, isBurstMode = false) {
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'send_message',
                content: queueItem.content,
                message_id: queueItem.id,
                is_burst: isBurstMode
            });
        } catch (error) {
            this.updateQueueItemStatus(queueItem.id, 'failed');
            if (!isBurstMode) {
                this.showNotification('Failed to send message: ' + error.message, 'error');
            }
        }
    }

    updateQueueItemStatus(messageId, status, error = null) {
        const item = this.messageQueue.find(q => q.id === messageId);
        if (item) {
            item.status = status;
            if (error) item.error = error;
            this.updateQueueDisplay();
        }
    }

    removeFromQueue(messageId) {
        this.messageQueue = this.messageQueue.filter(q => q.id !== messageId);
        this.updateQueueDisplay();
    }

    toggleQueuePanel() {
        const isVisible = this.queueContent.style.display !== 'none';
        this.queueContent.style.display = isVisible ? 'none' : 'block';
        this.toggleQueueBtn.innerHTML = isVisible ? 
            '<i class="fas fa-chevron-up"></i>' : 
            '<i class="fas fa-chevron-down"></i>';
    }

    handlePythonMessage(data) {
        console.log('Python message:', data);
        switch (data.type) {
            case 'connection_success':
                this.handleConnectionSuccess(data);
                break;
            case 'connection_error':
                this.handleConnectionError(data);
                break;
            case 'message_received':
                this.handleMessageReceived(data);
                break;
            case 'message_sent':
                this.handleMessageSent(data);
                break;
            case 'message_failed':
                this.handleMessageFailed(data);
                break;
            case 'message_updated':
                this.handleMessageUpdated(data);
                break;
            case 'message_deleted':
                this.handleMessageDeleted(data);
                break;
            case 'reaction_added':
                this.handleReactionAdded(data);
                break;
            case 'reaction_removed':
                this.handleReactionRemoved(data);
                break;
            case 'thread_created':
                this.handleThreadCreated(data);
                break;
            case 'thread_updated':
                this.handleThreadUpdated(data);
                break;
            case 'rate_limit':
                this.handleRateLimit(data);
                break;
            case 'typing_start':
                this.handleTypingStart(data);
                break;
            case 'typing_stop':
                this.handleTypingStop(data);
                break;
            case 'rate_limit_info':
                this.updateRateLimitInfo(data);
                break;
            case 'queue_paused':
                this.handleQueuePaused(data);
                break;
            case 'queue_resumed':
                this.handleQueueResumed(data);
                break;
            case 'queue_cleared':
                this.handleQueueCleared(data);
                break;
        }
    }

    handleConnectionSuccess(data) {
        this.connected = true;
        this.currentUser = data.user;
        this.updateConnectionStatus('Connected', 'connected');
        this.connectChannelBtn.disabled = false;
        this.messageInput.disabled = false;
        this.sendButton.disabled = false;
        this.messageInput.placeholder = 'Type a message...';
        
        this.channelHeader.innerHTML = `
            <i class="fas fa-hashtag"></i>
            <span>${data.channel_name || 'Channel'}</span>
        `;
        
        this.clearWelcomeMessage();
        this.showNotification('Connected successfully!', 'success');
    }

    handleConnectionError(data) {
        this.connected = false;
        this.updateConnectionStatus('Connection failed', 'disconnected');
        this.connectChannelBtn.disabled = false;
        this.messageInput.disabled = false;
        this.sendButton.disabled = false;
        this.messageInput.placeholder = 'Connect to Discord to send messages...';
        this.showNotification('Connection failed: ' + data.error, 'error');
    }

    handleMessageReceived(data) {
        console.log('=== FRONTEND MESSAGE RECEIVED ===');
        console.log('Full message data received in frontend:', JSON.stringify(data, null, 2));
        console.log('Message ID:', data.id);
        console.log('Message content:', data.content);
        console.log('Message author:', data.author);
        console.log('Content type:', typeof data.content);
        console.log('Content length:', data.content ? data.content.length : 'N/A');
        
        // Check if this is our own message that we just sent
        if (this.currentUser && data.author && data.author.id === this.currentUser.id) {
            console.log('This is our own message - removing from queue');
            // Find and remove the corresponding queued message
            const queuedMessage = this.messageQueue.find(q => 
                q.content === data.content && 
                (q.status === 'pending' || q.status === 'sent')
            );
            if (queuedMessage) {
                console.log('Found matching queued message, removing:', queuedMessage.id);
                this.removeFromQueue(queuedMessage.id);
            }
        }
        
        console.log('=== END FRONTEND MESSAGE RECEIVED ===');
        this.displayMessage(data);
    }

    handleMessageSent(data) {
        console.log('Message sent:', data);
        this.updateQueueItemStatus(data.message_id, 'sent');
    }

    handleMessageFailed(data) {
        const item = this.messageQueue.find(q => q.id === data.message_id);
        if (item) {
            item.retries++;
            if (item.retries < item.maxRetries) {
                this.updateQueueItemStatus(data.message_id, 'retrying');
                // Retry after delay
                setTimeout(() => {
                    this.retryMessage(item);
                }, data.retry_after || 5000);
            } else {
                this.updateQueueItemStatus(data.message_id, 'failed', data.error);
            }
        }
    }

    async retryMessage(item) {
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'send_message',
                content: item.content,
                message_id: item.id
            });
        } catch (error) {
            this.updateQueueItemStatus(item.id, 'failed', error.message);
        }
    }

    handleRateLimit(data) {
        console.log('Rate limited for', data.retry_after, 'ms');
    }

    handleTypingStart(data) {
        this.typingIndicator.textContent = `${data.user} is typing...`;
    }

    handleTypingStop(data) {
        this.typingIndicator.textContent = '';
    }

    handleQueuePaused(data) {
        console.log('Queue paused by backend');
        this.queuePaused = true;
        if (this.pauseQueueBtn) {
            this.pauseQueueBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
            this.pauseQueueBtn.classList.remove('btn-warning');
            this.pauseQueueBtn.classList.add('btn-success');
        }
        this.updateQueueDisplay();
    }

    handleQueueResumed(data) {
        console.log('Queue resumed by backend');
        this.queuePaused = false;
        if (this.pauseQueueBtn) {
            this.pauseQueueBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
            this.pauseQueueBtn.classList.remove('btn-success');
            this.pauseQueueBtn.classList.add('btn-warning');
        }
        this.updateQueueDisplay();
    }

    handleQueueCleared(data) {
        console.log('Queue cleared by backend');
        this.messageQueue = [];
        this.updateQueueDisplay();
    }

    displayMessage(data) {
        console.log('=== PROCESSING MESSAGE ===');
        console.log('Raw message data:', JSON.stringify(data, null, 2));
        
        // Validate required data
        if (!data || !data.author) {
            console.error('Invalid message data - missing author:', data);
            return;
        }
        
        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = 'message';
        
        // Check if this message mentions the current user
        const mentionsMe = this.checkIfMentionsMe(data);
        if (mentionsMe) {
            messageElement.classList.add('mentions-me');
        }
        
        // Check if this is a reply
        const isReply = data.referenced_message || data.message_reference;
        if (isReply) {
            messageElement.classList.add('is-reply');
        }
        
        // Set data attributes safely
        try {
            messageElement.dataset.userId = String(data.author.id || 'unknown');
            messageElement.dataset.messageId = String(data.id || Date.now());
        } catch (e) {
            console.error('Error setting data attributes:', e);
        }
        
        // Add context menu
        messageElement.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showContextMenu(e, data.author);
        });
        
        // Process author information safely
        const author = this.processAuthorData(data.author);
        console.log('Processed author:', author);
        
        // Process message content
        const content = this.processMessageContent(data);
        console.log('Processed content:', content);
        
        // Process timestamp
        const timestamp = this.processTimestamp(data.timestamp);
        console.log('Processed timestamp:', timestamp);
        
        // Build message HTML using a more robust approach
        const messageHTML = this.buildMessageHTML(author, content, timestamp, data);
        
        messageElement.innerHTML = messageHTML;
        
        console.log('Final message HTML:', messageHTML);
        console.log('Appending to container...');
        
        // Append and scroll
        this.messagesContainer.appendChild(messageElement);
        this.scrollToBottom();
        
        console.log('Message successfully added. Total messages:', this.messagesContainer.children.length);
        console.log('=== END MESSAGE PROCESSING ===\n');
    }
    
    checkIfMentionsMe(data) {
        if (!this.currentUser) return false;
        
        // Check direct mentions in content
        const content = data.content || '';
        const mentionPattern = new RegExp(`<@!?${this.currentUser.id}>`, 'g');
        if (mentionPattern.test(content)) return true;
        
        // Check mentions array
        if (data.mentions && Array.isArray(data.mentions)) {
            return data.mentions.some(mention => mention.id === this.currentUser.id);
        }
        
        // Check role mentions if user has roles
        if (data.mention_roles && Array.isArray(data.mention_roles) && this.currentUser.roles) {
            return data.mention_roles.some(roleId => this.currentUser.roles.includes(roleId));
        }
        
        return false;
    }
    
    processAuthorData(authorData) {
        if (!authorData) {
            return {
                id: 'unknown',
                username: 'Unknown User',
                displayName: 'Unknown User',
                avatar: 'https://cdn.discordapp.com/embed/avatars/0.png',
                color: '#ffffff',
                isBot: false
            };
        }
        
        return {
            id: String(authorData.id || 'unknown'),
            username: String(authorData.username || 'Unknown'),
            displayName: String(authorData.display_name || authorData.username || 'Unknown User'),
            avatar: String(authorData.avatar || 'https://cdn.discordapp.com/embed/avatars/0.png'),
            color: String(authorData.color || '#ffffff'),
            isBot: Boolean(authorData.bot)
        };
    }
    
    processMessageContent(data) {
        const result = {
            text: '',
            hasText: false,
            attachments: [],
            embeds: [],
            poll: null,
            thread: null,
            reactions: [],
            messageType: 'normal'
        };
        
        // Process text content with more robust checking
        console.log('Processing content for message:', data.id, 'Content value:', JSON.stringify(data.content), 'Type:', typeof data.content);
        
        if (data.content !== undefined && data.content !== null) {
            const textContent = String(data.content).trim();
            console.log('Processed text content:', JSON.stringify(textContent), 'Length:', textContent.length);
            if (textContent.length > 0) {
                result.text = textContent;
                result.hasText = true;
                console.log('✓ Found text content for message:', data.id, ':', textContent);
            } else {
                console.log('⚠ Text content is empty or whitespace only for message:', data.id);
            }
        } else {
            console.log('⚠ No content property found in message data for message:', data.id);
        }
        
        // Process attachments
        if (Array.isArray(data.attachments) && data.attachments.length > 0) {
            result.attachments = data.attachments;
            result.messageType = this.determineAttachmentType(data.attachments);
            console.log('Found attachments:', data.attachments.length);
        }
        
        // Process embeds
        if (Array.isArray(data.embeds) && data.embeds.length > 0) {
            result.embeds = data.embeds;
            if (result.messageType === 'normal') result.messageType = 'embed';
            console.log('Found embeds:', data.embeds.length);
        }
        
        // Process poll
        if (data.poll && typeof data.poll === 'object') {
            result.poll = data.poll;
            result.messageType = 'poll';
            console.log('Found poll');
        }
        
        // Process thread
        if (data.thread && typeof data.thread === 'object') {
            result.thread = data.thread;
            result.messageType = 'thread';
            console.log('Found thread');
        }
        
        // Process reactions
        if (Array.isArray(data.reactions) && data.reactions.length > 0) {
            result.reactions = data.reactions;
            console.log('Found reactions:', data.reactions.length);
        }
        
        return result;
    }
    
    determineAttachmentType(attachments) {
        if (!Array.isArray(attachments) || attachments.length === 0) return 'normal';
        
        const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        const hasImages = attachments.some(att => 
            att.content_type && imageTypes.includes(att.content_type.toLowerCase())
        );
        
        return hasImages ? 'image' : 'file';
    }
    
    processTimestamp(timestamp) {
        try {
            if (!timestamp) return new Date();
            
            // Handle different timestamp formats
            if (typeof timestamp === 'string') {
                return new Date(timestamp);
            } else if (typeof timestamp === 'number') {
                // Handle both milliseconds and seconds
                return new Date(timestamp > 1000000000000 ? timestamp : timestamp * 1000);
            } else if (timestamp instanceof Date) {
                return timestamp;
            }
            
            return new Date();
        } catch (e) {
            console.error('Error processing timestamp:', e);
            return new Date();
        }
    }
    
    buildMessageHTML(author, content, timestamp, rawData) {
        // Build reply HTML if this is a reply
        let replyHTML = '';
        if (rawData.referenced_message || rawData.message_reference) {
            replyHTML = this.buildReplyHTML(rawData.referenced_message, rawData.message_reference);
        }
        
        // Build avatar HTML
        const avatarHTML = `
            <img class="message-avatar" 
                 src="${this.escapeHtml(author.avatar)}" 
                 alt="${this.escapeHtml(author.username)}" 
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
            <div class="message-avatar fallback" 
                 style="display: none; align-items: center; justify-content: center; background: #7289da; color: white; font-weight: bold; width: 40px; height: 40px; border-radius: 50%; font-size: 16px;">
                ${this.escapeHtml(author.username.charAt(0).toUpperCase())}
            </div>
        `;
        
        // Build header HTML
        const botIndicator = author.isBot ? '<span class="bot-indicator" style="background: #5865f2; color: white; padding: 2px 4px; border-radius: 3px; font-size: 10px; margin-left: 5px;">BOT</span>' : '';
        const editedIndicator = rawData.edited_timestamp ? '<span class="edited-indicator" style="color: #72767d; font-size: 12px; margin-left: 4px;">(edited)</span>' : '';
        const typeIndicator = this.buildTypeIndicator(content.messageType);
        
        const headerHTML = `
            <div class="message-header">
                <span class="message-author" style="color: ${this.escapeHtml(author.color)}; font-weight: 600;">
                    ${this.escapeHtml(author.displayName)}
                </span>
                ${botIndicator}
                <span class="message-timestamp" style="color: #72767d; font-size: 12px; margin-left: 8px;">
                    ${this.formatTimestamp(timestamp)}
                </span>
                ${editedIndicator}
                ${typeIndicator}
            </div>
        `;
        
        // Build content HTML
        const contentHTML = this.buildContentHTML(content);
        
        // Combine everything
        return `
            ${avatarHTML}
            <div class="message-content">
                ${replyHTML}
                ${headerHTML}
                ${contentHTML}
            </div>
        `;
    }
    
    buildReplyHTML(referencedMessage, messageReference) {
        if (!referencedMessage && !messageReference) return '';
        
        // If we have the full referenced message
        if (referencedMessage && referencedMessage.author) {
            const replyAuthor = referencedMessage.author.username || 'Unknown User';
            const replyContent = referencedMessage.content || 'Click to see attachment';
            const truncatedContent = replyContent.length > 50 ? replyContent.substring(0, 50) + '...' : replyContent;
            
            return `
                <div class="message-reply" style="display: flex; align-items: center; margin-bottom: 4px; padding: 4px 8px; background: rgba(79, 84, 92, 0.16); border-left: 4px solid #4f545c; border-radius: 3px;">
                    <i class="fas fa-reply" style="color: #b9bbbe; margin-right: 6px; font-size: 12px;"></i>
                    <span class="reply-author" style="color: #ffffff; font-weight: 500; font-size: 14px; margin-right: 4px;">${this.escapeHtml(replyAuthor)}</span>
                    <span class="reply-content" style="color: #dcddde; font-size: 14px; opacity: 0.8;">${this.escapeHtml(truncatedContent)}</span>
                </div>
            `;
        }
        
        // If we only have message reference (message might be deleted or not loaded)
        if (messageReference) {
            return `
                <div class="message-reply" style="display: flex; align-items: center; margin-bottom: 4px; padding: 4px 8px; background: rgba(79, 84, 92, 0.16); border-left: 4px solid #4f545c; border-radius: 3px;">
                    <i class="fas fa-reply" style="color: #b9bbbe; margin-right: 6px; font-size: 12px;"></i>
                    <span class="reply-content" style="color: #dcddde; font-size: 14px; opacity: 0.8; font-style: italic;">Replying to a message</span>
                </div>
            `;
        }
        
        return '';
    }
    
    buildTypeIndicator(messageType) {
        const indicators = {
            thread: '<span class="type-indicator thread" style="background: rgba(88, 101, 242, 0.2); color: #5865f2; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;"><i class="fas fa-comments"></i> Thread</span>',
            poll: '<span class="type-indicator poll" style="background: rgba(67, 181, 129, 0.2); color: #43b581; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;"><i class="fas fa-poll"></i> Poll</span>',
            image: '<span class="type-indicator image" style="background: rgba(255, 165, 0, 0.2); color: #faa61a; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;"><i class="fas fa-image"></i> Image</span>',
            file: '<span class="type-indicator file" style="background: rgba(114, 137, 218, 0.2); color: #7289da; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;"><i class="fas fa-file"></i> File</span>',
            embed: '<span class="type-indicator embed" style="background: rgba(114, 137, 218, 0.2); color: #7289da; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;"><i class="fas fa-link"></i> Embed</span>'
        };
        
        return indicators[messageType] || '';
    }
    

    buildContentHTML(content) {
        let html = '';
        
        // Add text content section only if there's actual text
        if (content.hasText) {
            html += `<div class="message-text" style="color: #dcddde; line-height: 1.4; word-wrap: break-word; margin: 4px 0;">
                ${this.formatMessageContent(content.text)}
            </div>`;
        } else if (!content.attachments.length && !content.embeds.length && !content.poll && !content.thread) {
            // Only show placeholder if there's truly no content at all
            html += `<div class="message-text" style="color: #72767d; font-style: italic; margin: 4px 0;">
                <em>This message has no content</em>
            </div>`;
        }
        
        // Add attachments
        if (content.attachments.length > 0) {
            html += this.renderAttachments(content.attachments);
        }
        
        // Add embeds
        if (content.embeds.length > 0) {
            html += this.renderEmbeds(content.embeds);
        }
        
        // Add poll
        if (content.poll) {
            html += this.renderPoll(content.poll);
        }
        
        // Add thread info
        if (content.thread) {
            html += this.renderThreadInfo(content.thread);
        }
        
        // Add reactions
        if (content.reactions.length > 0) {
            html += this.renderReactions(content.reactions);
        }
        
        return html;
    }
    
    renderAttachments(attachments) {
        let html = '<div class="message-attachments">';
        
        for (const attachment of attachments) {
            const isImage = attachment.content_type && attachment.content_type.startsWith('image/');
            
            if (isImage) {
                html += `
                    <div class="attachment-image">
                        <img src="${attachment.url}" alt="${attachment.filename}" 
                             style="max-width: 400px; max-height: 300px; border-radius: 4px; cursor: pointer;"
                             onclick="window.open('${attachment.url}', '_blank')">
                        <div class="attachment-info">
                            <span class="attachment-name">${attachment.filename}</span>
                            <span class="attachment-size">${this.formatFileSize(attachment.size)}</span>
                        </div>
                    </div>
                `;
            } else {
                html += `
                    <div class="attachment-file">
                        <i class="fas fa-file"></i>
                        <div class="attachment-info">
                            <a href="${attachment.url}" target="_blank" class="attachment-name">${attachment.filename}</a>
                            <span class="attachment-size">${this.formatFileSize(attachment.size)}</span>
                        </div>
                    </div>
                `;
            }
        }
        
        html += '</div>';
        return html;
    }
    
    renderEmbeds(embeds) {
        let html = '<div class="message-embeds">';
        
        for (const embed of embeds) {
            const borderColor = embed.color ? '#' + embed.color.toString(16).padStart(6, '0') : '#7289da';
            html += `
                <div class="embed" style="border-left: 4px solid ${borderColor}; padding: 8px; margin: 4px 0; background: rgba(46, 48, 54, 0.3);">
                    ${embed.author ? `<div class="embed-author" style="font-size: 12px; font-weight: bold;">${embed.author.name}</div>` : ''}
                    ${embed.title ? `<div class="embed-title" style="font-weight: bold; margin: 4px 0;">${embed.title}</div>` : ''}
                    ${embed.description ? `<div class="embed-description" style="margin: 4px 0;">${embed.description}</div>` : ''}
                    ${embed.image ? `<img class="embed-image" src="${embed.image.url}" alt="Embed image" style="max-width: 400px; border-radius: 4px;">` : ''}
                    ${embed.thumbnail ? `<img class="embed-thumbnail" src="${embed.thumbnail.url}" alt="Embed thumbnail" style="max-width: 80px; float: right;">` : ''}
                    ${embed.fields && embed.fields.length > 0 ? this.renderEmbedFields(embed.fields) : ''}
                </div>
            `;
        }
        
        html += '</div>';
        return html;
    }
    
    renderEmbedFields(fields) {
        let html = '<div class="embed-fields" style="display: grid; gap: 8px;">';
        
        for (const field of fields) {
            html += `
                <div class="embed-field ${field.inline ? 'inline' : ''}" style="${field.inline ? 'display: inline-block; margin-right: 16px;' : ''}">
                    <div class="embed-field-name" style="font-weight: bold; font-size: 12px;">${field.name}</div>
                    <div class="embed-field-value" style="font-size: 14px;">${field.value}</div>
                </div>
            `;
        }
        
        html += '</div>';
        return html;
    }
    
    renderPoll(poll) {
        let html = `
            <div class="message-poll" style="background: rgba(46, 48, 54, 0.3); padding: 12px; border-radius: 4px; margin: 8px 0;">
                <div class="poll-question" style="font-weight: bold; margin-bottom: 8px;"><i class="fas fa-poll"></i> ${poll.question}</div>
                <div class="poll-answers">
        `;
        
        for (const answer of poll.answers) {
            html += `
                <div class="poll-answer" style="display: flex; justify-content: space-between; padding: 6px; margin: 4px 0; background: rgba(64, 68, 75, 0.6); border-radius: 3px;">
                    <span class="poll-answer-text">${answer.text}</span>
                    <span class="poll-answer-votes" style="color: #b9bbbe;">${answer.votes} votes</span>
                </div>
            `;
        }
        
        html += `
                </div>
                ${poll.expires_at ? `<div class="poll-expires" style="font-size: 12px; color: #72767d; margin-top: 8px;">Expires: ${new Date(poll.expires_at).toLocaleString()}</div>` : ''}
            </div>
        `;
        
        return html;
    }
    
    renderThreadInfo(thread) {
        return `
            <div class="message-thread-info" style="background: rgba(88, 101, 242, 0.1); padding: 8px; border-radius: 4px; margin: 4px 0;">
                <i class="fas fa-comments" style="color: #5865f2;"></i>
                <span class="thread-name" style="font-weight: bold; margin: 0 8px;">${thread.name}</span>
                <span class="thread-stats" style="color: #b9bbbe; font-size: 12px;">${thread.message_count} messages, ${thread.member_count} members</span>
            </div>
        `;
    }
    
    renderReactions(reactions) {
        let html = '<div class="message-reactions" style="margin-top: 4px;">';
        
        for (const reaction of reactions) {
            const emoji = reaction.emoji.id ? 
                `<img src="https://cdn.discordapp.com/emojis/${reaction.emoji.id}.${reaction.emoji.animated ? 'gif' : 'png'}" alt="${reaction.emoji.name}" class="custom-emoji" style="width: 16px; height: 16px;">` :
                reaction.emoji.name;
            
            html += `
                <div class="reaction ${reaction.me ? 'me' : ''}" style="display: inline-flex; align-items: center; background: rgba(64, 68, 75, 0.6); border-radius: 3px; padding: 2px 6px; margin: 2px; font-size: 12px; ${reaction.me ? 'background: rgba(88, 101, 242, 0.3);' : ''}">
                    ${emoji}
                    <span class="reaction-count" style="margin-left: 4px;">${reaction.count}</span>
                </div>
            `;
        }
        
        html += '</div>';
        return html;
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatMessageContent(content) {
        // Escape HTML first
        let formatted = this.escapeHtml(content);
        
        // Handle big text messages (headers)
        formatted = formatted.replace(/^# (.+)$/gm, '<h1 class="message-header-1">$1</h1>');
        formatted = formatted.replace(/^## (.+)$/gm, '<h2 class="message-header-2">$1</h2>');
        formatted = formatted.replace(/^### (.+)$/gm, '<h3 class="message-header-3">$1</h3>');
        
        // Handle Discord formatting
        formatted = formatted
            // Bold text
            .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
            // Italic text
            .replace(/\*(.+?)\*/g, '<em>$1</em>')
            // Underline text
            .replace(/__(.+?)__/g, '<u>$1</u>')
            // Strikethrough text
            .replace(/~~(.+?)~~/g, '<del>$1</del>')
            // Inline code
            .replace(/`(.+?)`/g, '<code class="inline-code">$1</code>')
            // Code blocks
            .replace(/```
(?:(\w+)\n)?([\s\S]*?)
```/g, '<pre class="code-block"><code class="language-$1">$2</code></pre>')
            // Spoiler text
            .replace(/\|\|(.+?)\|\|/g, '<span class="spoiler" onclick="this.classList.toggle(\'revealed\')">$1</span>')
            // User mentions
            .replace(/<@!?(\d+)>/g, '<span class="mention user-mention">@user</span>')
            // Channel mentions
            .replace(/<#(\d+)>/g, '<span class="mention channel-mention">#channel</span>')
            // Role mentions
            .replace(/<@&(\d+)>/g, '<span class="mention role-mention">@role</span>')
            // Links
            .replace(/https?:\/\/[^\s]+/g, '<a href="$&" target="_blank" class="message-link">$&</a>')
            // Line breaks
            .replace(/\n/g, '<br>');
        
        return formatted;
    }

    showContextMenu(event, author) {
        this.selectedMessage = author;
        this.contextMenu.style.display = 'block';
        this.contextMenu.style.left = event.pageX + 'px';
        this.contextMenu.style.top = event.pageY + 'px';
    }

    hideContextMenu() {
        this.contextMenu.style.display = 'none';
        this.selectedMessage = null;
    }

    copyMention() {
        if (this.selectedMessage) {
            const mention = `<@${this.selectedMessage.id}>`;
            navigator.clipboard.writeText(mention);
            this.showNotification('Mention copied to clipboard', 'success');
        }
        this.hideContextMenu();
    }

    copyUserId() {
        if (this.selectedMessage) {
            navigator.clipboard.writeText(this.selectedMessage.id);
            this.showNotification('User ID copied to clipboard', 'success');
        }
        this.hideContextMenu();
    }

    showRateLimitModal(retryAfter) {
        this.rateLimitModal.style.display = 'flex';
        let timeLeft = Math.ceil(retryAfter / 1000);
        
        const updateCountdown = () => {
            this.rateLimitCountdown.textContent = timeLeft;
            const progress = ((retryAfter / 1000 - timeLeft) / (retryAfter / 1000)) * 100;
            this.rateLimitProgress.style.width = progress + '%';
            
            if (timeLeft <= 0) {
                this.hideRateLimitModal();
                return;
            }
            
            timeLeft--;
            setTimeout(updateCountdown, 1000);
        };
        
        updateCountdown();
    }

    hideRateLimitModal() {
        this.rateLimitModal.style.display = 'none';
    }

    updateRateLimitInfo(data) {
        this.rateLimitInfo = data;
        this.rateLimitRemaining.textContent = data.remaining || 'Unknown';
        this.rateLimitReset.textContent = data.reset ? 
            new Date(data.reset * 1000).toLocaleTimeString() : 'Unknown';
    }

    updateConnectionStatus(text, status) {
        this.connectionStatus.className = `connection-status ${status}`;
        this.connectionStatus.innerHTML = `<i class="fas fa-circle"></i><span>${text}</span>`;
    }

    clearWelcomeMessage() {
        const welcomeMessage = this.messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    formatTimestamp(date) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // Create a simple toast notification
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: ${type === 'success' ? '#43b581' : type === 'error' ? '#f04747' : '#7289da'};
            color: white;
            border-radius: 6px;
            z-index: 3000;
            animation: slideIn 0.3s ease-out;
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.animation = 'slideOut 0.3s ease-out';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
    
    handleMessageUpdated(data) {
        console.log('Message updated:', data);
        const messageElement = this.messagesContainer.querySelector(`[data-message-id="${data.id}"]`);
        if (messageElement) {
            const contentElement = messageElement.querySelector('.message-text');
            if (contentElement) {
                contentElement.innerHTML = this.formatMessageContent(data.content);
            }
            
            // Add edited indicator if not already present
            const header = messageElement.querySelector('.message-header');
            if (header && !header.querySelector('.edited-indicator')) {
                const editedSpan = document.createElement('span');
                editedSpan.className = 'edited-indicator';
                editedSpan.style.cssText = 'color: #72767d; font-size: 12px; margin-left: 4px;';
                editedSpan.textContent = '(edited)';
                header.appendChild(editedSpan);
            }
        }
    }
    
    handleMessageDeleted(data) {
        console.log('Message deleted:', data);
        const messageElement = this.messagesContainer.querySelector(`[data-message-id="${data.id}"]`);
        if (messageElement) {
            messageElement.style.opacity = '0.5';
            const contentElement = messageElement.querySelector('.message-text');
            if (contentElement) {
                contentElement.innerHTML = '<em style="color: #72767d;">This message was deleted</em>';
            }
        }
    }
    
    handleReactionAdded(data) {
        console.log('Reaction added:', data);
        this.showNotification('Reaction added to message', 'info');
    }
    
    handleReactionRemoved(data) {
        console.log('Reaction removed:', data);
        this.showNotification('Reaction removed from message', 'info');
    }
    
    handleThreadCreated(data) {
        console.log('Thread created:', data);
        this.showNotification(`New thread created: ${data.thread.name}`, 'info');
    }
    
    handleThreadUpdated(data) {
        console.log('Thread updated:', data);
        this.showNotification(`Thread updated: ${data.thread.name}`, 'info');
    }
}

// Add CSS for toast animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize the client when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.zephyrClient = new ZephyrClient();
});