const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class ZephyrDiscordClient {
    constructor() {
        this.activeTab = 'manual';
        this.isConnected = false;
        this.currentUser = null;
        this.currentChannel = null;
        this.typingUsers = new Set();
        
        // Wordlist Generator
        this.wordlistGenerator = {
            wordsPhrases: [], // Combined words and phrases
            wordsPerSentence: 5,
            typoChance: 20,
            linesToGenerate: 1,
            usedCombinations: new Set()
        };
        
        // Autobeef tracking
        this.autobeefMessageCount = 0;
        this.sessionStartTime = Date.now();
        this.autobeefSessionTime = 0; // Total time autobeef has been running
        this.autobeefStartTime = null; // When current session started
        this.autobeefPausedForManual = false;
        this.autobeefActive = false;
        this.autobeefInterval = null;
        this.usedAutobeefMessages = new Set(); // Track used messages to avoid repeats

        // Advanced anti-repeat system
        this.messageHistory = new Map(); // Maps normalized message -> { original, lastUsed, useCount }
        this.recentMessages = []; // Last 30 messages for anti-repeat
        this.maxRecentMessages = 30;
        
        // Connection state
        this.isConnected = false;
        this.currentChannel = null;
        
        // Queue system
        this.messageQueue = [];
        this.queuePaused = false;
        
        // Keybind system
        this.keybinds = {
            pauseQueue: 'F1',
            spacing: null,
            autobeef: 'Escape',
            burstMode: 'F2',
            priorityMessage: 'RightAlt'
        };
        this.settingKeybind = null;
        
        // Burst mode system
        this.burstModeEnabled = false;
        this.burstIntervals = {
            double: 10,
            triple: 15,
            quad: 20
        };
        
        // New burst feature (separate from existing burst)
        this.burstFeatureActive = false;

        // Burst type selection
        this.burstType = 'threading'; // 'threading' or 'tasks'

        // Mini burst feature
        this.miniBurstEnabled = false;
        this.miniBurstActive = false;
        this.miniBurstTimeout = null;
        this.miniBurstDeactivateTimeout = null;
        this.miniBurstSettings = {
            activationMin: 5,
            activationMax: 10,
            duration: 2
        };
        this.originalDelay = null; // Store original delay for mini burst

        // Priority message feature
        this.priorityMessageActive = false;
        this.priorityMessageWasPaused = {
            queue: false,
            autobeef: false
        };
        
        this.init();
        
        console.log('🚀 Zephyr initialized with enhanced wordlist generator');
    }
    
    init() {
        console.log('🚀 Initializing Zephyr Discord Client...');
        
        this.initializeElements();
        this.setupEventListeners();
        this.setupPythonMessageHandler();
        this.setupKeybinds();
        this.loadSavedData();
        this.loadKeybinds();
        this.loadSettings();
        this.loadWordlistFromFile();
        this.loadWordIndex();
        this.initializeBurstModeUI();
        this.loadAutoreactSettings();
        // Removed health check timer - no more popups
        
        console.log('✅ Zephyr Discord Client initialized');
    }
    
    // Removed all health check methods - no more popups
    
    initializeElements() {
        // Connection elements
        this.tokenInput = document.getElementById('tokenInput');
        this.channelInput = document.getElementById('channelInput');
        this.connectBtn = document.getElementById('connectChannel');
        this.saveTokenBtn = document.getElementById('saveToken');
        this.toggleTokenBtn = document.getElementById('toggleToken');
        this.connectionStatus = document.getElementById('connectionStatus');
        this.channelName = document.getElementById('channelName');
        
        // Message elements
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.messagesContainer = document.getElementById('messagesContainer');
        
        // Feature tabs
        this.featureTabs = document.querySelectorAll('.feature-tab');
        this.tabContents = document.querySelectorAll('.tab-content');

        this.messagePrefix = document.getElementById('messagePrefix');
        this.messageSuffix = document.getElementById('messageSuffix');
        this.prefixSuffixEnabled = document.getElementById('prefixSuffixEnabled');
        this.messageDelay = document.getElementById('messageDelay');
        this.delayDisplay = document.getElementById('delayDisplay');
        this.spacingEnabled = document.getElementById('spacingEnabled');
        
        // Burst mode elements
        this.burstModeEnabledCheckbox = document.getElementById('burstModeEnabled');
        this.burstSettings = document.getElementById('burstSettings');
        this.burstStatusDot = document.getElementById('burstStatusDot');
        this.burstStatusText = document.getElementById('burstStatusText');
        this.doubleBurstInterval = document.getElementById('doubleBurstInterval');
        this.doubleBurstDisplay = document.getElementById('doubleBurstDisplay');
        this.tripleBurstInterval = document.getElementById('tripleBurstInterval');
        this.tripleBurstDisplay = document.getElementById('tripleBurstDisplay');
        this.quadBurstInterval = document.getElementById('quadBurstInterval');
        this.quadBurstDisplay = document.getElementById('quadBurstDisplay');

        // Burst type elements
        this.burstTypeThreading = document.getElementById('burstTypeThreading');
        this.burstTypeTasks = document.getElementById('burstTypeTasks');

        // Mini burst elements
        this.miniBurstEnabled = document.getElementById('miniBurstEnabled');
        this.miniBurstSettings = document.getElementById('miniBurstSettings');
        this.miniBurstStatusDot = document.getElementById('miniBurstStatusDot');
        this.miniBurstStatusText = document.getElementById('miniBurstStatusText');
        this.miniBurstActivationMin = document.getElementById('miniBurstActivationMin');
        this.miniBurstActivationMax = document.getElementById('miniBurstActivationMax');
        this.miniBurstActivationDisplay = document.getElementById('miniBurstActivationDisplay');
        this.miniBurstDuration = document.getElementById('miniBurstDuration');
        this.miniBurstDurationDisplay = document.getElementById('miniBurstDurationDisplay');
        
        // Wordlist generator elements
        this.newWordPhraseInput = document.getElementById('newWord'); // Reuse existing input
        this.addWordPhraseBtn = document.getElementById('addWord'); // Reuse existing button
        this.loadIndexBtn = document.getElementById('loadIndex');
        this.clearWordPhrasesBtn = document.getElementById('clearWords'); // Reuse existing button
        this.wordPhrasesPreview = document.getElementById('wordsPreview'); // Reuse existing preview
        this.wordPhraseCount = document.getElementById('wordCount'); // Reuse existing count
        this.wordsPerSentenceSlider = document.getElementById('wordsPerSentence');
        this.wordsPerSentenceDisplay = document.getElementById('wordsPerSentenceDisplay');
        this.typoChanceSlider = document.getElementById('typoChance');
        this.typoChanceDisplay = document.getElementById('typoChanceDisplay');
        this.linesToGenerateSlider = document.getElementById('linesToGenerate');
        this.linesToGenerateDisplay = document.getElementById('linesToGenerateDisplay');
        this.noRepeatingLinesCheckbox = document.getElementById('noRepeatingLines');
        this.maxUniqueCountDisplay = document.getElementById('maxUniqueCount');
        this.generateSentenceBtn = document.getElementById('generateSentence');
        this.generatedSentence = document.getElementById('generatedSentence');
        this.copyGeneratedBtn = document.getElementById('copyGenerated');

        // Typo adder elements
        this.typoInputWordlist = document.getElementById('typoInputWordlist');
        this.loadFromWordlistGenBtn = document.getElementById('loadFromWordlistGen');
        this.clearTypoInputBtn = document.getElementById('clearTypoInput');
        this.typoAdderChanceSlider = document.getElementById('typoAdderChance');
        this.typoAdderChanceDisplay = document.getElementById('typoAdderChanceDisplay');
        this.typosPerWordSlider = document.getElementById('typosPerWord');
        this.typosPerWordDisplay = document.getElementById('typosPerWordDisplay');
        this.uniqueTyposModeCheckbox = document.getElementById('uniqueTyposMode');
        this.uniqueVariationsSlider = document.getElementById('uniqueVariations');
        this.uniqueVariationsDisplay = document.getElementById('uniqueVariationsDisplay');
        this.uniqueTyposSettings = document.getElementById('uniqueTyposSettings');
        this.generateTyposBtn = document.getElementById('generateTypos');
        this.typoOutput = document.getElementById('typoOutput');
        this.copyTyposBtn = document.getElementById('copyTypos');
        this.addToWordlistGenBtn = document.getElementById('addToWordlistGen');

        // Autobeef anti-repeat elements
        this.autobeefNoRepeatUntil98 = document.getElementById('autobeefNoRepeatUntil98');
        
        // Autobeef elements
        this.messagesSent = document.getElementById('messagesSent');
        this.sessionTime = document.getElementById('sessionTime');
        this.clearMessagesSentBtn = document.getElementById('clearMessagesSent');
        this.autobeefToggle = document.getElementById('autobeefToggle');
        this.autobeefDelay = document.getElementById('autobeefDelay');
        this.autobeefDelayDisplay = document.getElementById('autobeefDelayDisplay');
        this.autobeefBypassQueue = document.getElementById('autobeefBypassQueue');
        this.autoreactEnabled = document.getElementById('autoreactEnabled');
        this.autoreactEmoji = document.getElementById('autoreactEmoji');
        this.autobeefStatusDot = document.getElementById('autobeefStatusDot');
        this.autobeefStatusText = document.getElementById('autobeefStatusText');
        
        // Queue elements
        this.queueCount = document.getElementById('queueCount');
        this.queueCountInput = document.getElementById('queueCountInput');
        this.pauseQueue = document.getElementById('pauseQueue');
        this.clearQueue = document.getElementById('clearQueue');
        this.queueList = document.getElementById('queueList');
        
        // Window controls
        this.minimizeBtn = document.getElementById('minimizeBtn');
        this.closeBtn = document.getElementById('closeBtn');

        // Panel close buttons
        this.closePanelBtns = document.querySelectorAll('.close-panel');
        
        // WPM counter
        this.wpmCounter = document.getElementById('wpmCount');
        
        // Keybind elements
        this.pauseKeybindInput = document.getElementById('pauseKeybind');
        this.spacingKeybindInput = document.getElementById('spacingKeybind');
        this.autobeefKeybindInput = document.getElementById('autobeefKeybind');
        this.burstKeybindInput = document.getElementById('burstKeybind');
        this.priorityKeybindInput = document.getElementById('priorityKeybind');
        this.setPauseKeybindBtn = document.getElementById('setPauseKeybind');
        this.setSpacingKeybindBtn = document.getElementById('setSpacingKeybind');
        this.setBurstKeybindBtn = document.getElementById('setBurstKeybind');
        this.setPriorityKeybindBtn = document.getElementById('setPriorityKeybind');
        this.setAutobeefKeybindBtn = document.getElementById('setAutobeefKeybind');
    }
    
    setupEventListeners() {
        // Connection events - Updated to handle connect/disconnect toggle
        this.connectBtn?.addEventListener('click', () => {
            if (this.isConnected) {
                this.disconnectFromChannel();
            } else {
                this.connectToChannel();
            }
        });
        
        this.saveTokenBtn?.addEventListener('click', () => this.saveToken());
        this.toggleTokenBtn?.addEventListener('click', () => this.toggleTokenVisibility());
        
        // Message events
        this.sendButton?.addEventListener('click', () => this.sendMessage());
        this.messageInput?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
            this.autoResizeTextarea();
        });
        
        // Feature tab events
        this.featureTabs.forEach(tab => {
            tab.addEventListener('click', () => this.switchTab(tab.dataset.tab));
        });
        
        // Wordlist generator events
        this.addWordPhraseBtn?.addEventListener('click', () => this.addWordPhrase());
        this.loadIndexBtn?.addEventListener('click', () => this.loadWordIndex());
        this.clearWordPhrasesBtn?.addEventListener('click', () => this.clearWordPhrases());
        this.newWordPhraseInput?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.addWordPhrase();
            }
        });
        
        // Slider events
        this.wordsPerSentenceSlider?.addEventListener('input', (e) => {
            this.wordlistGenerator.wordsPerSentence = parseInt(e.target.value);
            this.updateSliderDisplays();
            this.updateMaxUniqueCount();
            this.saveWordlistSettings();
        });
        
        this.typoChanceSlider?.addEventListener('input', (e) => {
            this.wordlistGenerator.typoChance = parseInt(e.target.value);
            this.updateSliderDisplays();
            this.saveWordlistSettings();
        });
        
        this.linesToGenerateSlider?.addEventListener('input', (e) => {
            this.wordlistGenerator.linesToGenerate = parseInt(e.target.value);
            this.updateSliderDisplays();
            this.saveWordlistSettings();
        });
        
        // Generation events
        this.generateSentenceBtn?.addEventListener('click', () => this.generateRandomSentences());
        this.copyGeneratedBtn?.addEventListener('click', () => this.copyGeneratedText());

        // No repeating lines checkbox
        this.noRepeatingLinesCheckbox?.addEventListener('change', () => {
            this.updateMaxUniqueCount();
        });

        // Typo adder events
        this.loadFromWordlistGenBtn?.addEventListener('click', () => this.loadFromWordlistGenerator());
        this.clearTypoInputBtn?.addEventListener('click', () => this.clearTypoInput());
        this.typoAdderChanceSlider?.addEventListener('input', (e) => {
            this.typoAdderChanceDisplay.textContent = `${e.target.value}%`;
        });
        this.typosPerWordSlider?.addEventListener('input', (e) => {
            this.typosPerWordDisplay.textContent = e.target.value;
        });
        this.uniqueTyposModeCheckbox?.addEventListener('change', () => {
            this.toggleUniqueTyposSettings();
        });
        this.uniqueVariationsSlider?.addEventListener('input', (e) => {
            this.uniqueVariationsDisplay.textContent = `${e.target.value}x`;
        });
        this.generateTyposBtn?.addEventListener('click', () => this.generateTypos());
        this.copyTyposBtn?.addEventListener('click', () => this.copyTypos());
        this.addToWordlistGenBtn?.addEventListener('click', () => this.addTyposToWordlistGen());
        
        // Window control events
        this.minimizeBtn?.addEventListener('click', () => this.minimizeWindow());
        this.closeBtn?.addEventListener('click', () => this.closeWindow());

        // Panel close button events
        this.closePanelBtns?.forEach(btn => {
            btn.addEventListener('click', () => this.closeFeaturePanel());
        });
        
        // Message delay slider
        this.messageDelay?.addEventListener('input', (e) => {
            if (this.delayDisplay) {
                this.delayDisplay.textContent = `${e.target.value}s`;
            }
        });
        
        // Pairing modal events
        this.closePairingModal?.addEventListener('click', () => this.closePairingModalHandler());
        this.savePairingBtn?.addEventListener('click', () => this.savePhrasePariring());
        this.pairingModal?.addEventListener('click', (e) => {
            if (e.target === this.pairingModal) {
                this.closePairingModalHandler();
            }
        });
        
        // Queue control events
        this.pauseQueue?.addEventListener('click', () => this.toggleQueuePause());
        this.clearQueue?.addEventListener('click', () => this.clearMessageQueue());
        
        // Message delay slider - send to backend
        this.messageDelay?.addEventListener('input', async (e) => {
            if (this.delayDisplay) {
                this.delayDisplay.textContent = `${e.target.value}s`;
            }
            
            try {
                await ipcRenderer.invoke('send-to-python', {
                    action: 'update_settings',
                    settings: {
                        message_delay: parseFloat(e.target.value)
                    }
                });
            } catch (error) {
                console.error('Error updating message delay:', error);
            }
        });
        
        // Autobeef toggle event
        this.autobeefToggle?.addEventListener('click', () => this.toggleAutobeef());
        
        // Autobeef delay slider
        this.autobeefDelay?.addEventListener('input', (e) => {
            if (this.autobeefDelayDisplay) {
                this.autobeefDelayDisplay.textContent = `${e.target.value}s`;
            }
        });

        // Autobeef bypass queue checkbox
        this.autobeefBypassQueue?.addEventListener('change', (e) => {
            this.updateAutobeefBypassQueue(e.target.checked);
        });

        // Autoreact controls
        this.autoreactEnabled?.addEventListener('change', (e) => {
            this.updateAutoreact();
        });

        this.autoreactEmoji?.addEventListener('input', (e) => {
            // Validate and clean emoji input
            this.validateEmojiInput(e.target);
            this.updateAutoreact();
        });
        
        // Burst mode events
        this.burstModeEnabledCheckbox?.addEventListener('change', (e) => {
            this.toggleBurstMode(e.target.checked);
        });
        
        this.doubleBurstInterval?.addEventListener('input', (e) => {
            this.burstIntervals.double = parseInt(e.target.value);
            this.doubleBurstDisplay.textContent = `${e.target.value}s`;
            this.updateBurstIntervals();
        });
        
        this.tripleBurstInterval?.addEventListener('input', (e) => {
            this.burstIntervals.triple = parseInt(e.target.value);
            this.tripleBurstDisplay.textContent = `${e.target.value}s`;
            this.updateBurstIntervals();
        });
        
        this.quadBurstInterval?.addEventListener('input', (e) => {
            this.burstIntervals.quad = parseInt(e.target.value);
            this.quadBurstDisplay.textContent = `${e.target.value}s`;
            this.updateBurstIntervals();
        });
        
        // Burst type selection events
        this.burstTypeThreading?.addEventListener('change', (e) => {
            if (e.target.checked) {
                this.burstType = 'threading';
                console.log('🔧 Burst type set to threading');
            }
        });

        this.burstTypeTasks?.addEventListener('change', (e) => {
            if (e.target.checked) {
                this.burstType = 'tasks';
                console.log('🔧 Burst type set to tasks (fast sequential)');
            }
        });

        // Mini burst events
        this.miniBurstEnabled?.addEventListener('change', (e) => {
            this.toggleMiniBurst(e.target.checked);
        });

        this.miniBurstActivationMin?.addEventListener('input', (e) => {
            this.miniBurstSettings.activationMin = parseInt(e.target.value);
            this.updateMiniBurstDisplay();
        });

        this.miniBurstActivationMax?.addEventListener('input', (e) => {
            this.miniBurstSettings.activationMax = parseInt(e.target.value);
            this.updateMiniBurstDisplay();
        });

        this.miniBurstDuration?.addEventListener('input', (e) => {
            this.miniBurstSettings.duration = parseFloat(e.target.value);
            this.miniBurstDurationDisplay.textContent = `${e.target.value}s`;
        });

        // Keybind setting events
        this.setPauseKeybindBtn?.addEventListener('click', () => this.setKeybind('pauseQueue'));
        this.setSpacingKeybindBtn?.addEventListener('click', () => this.setKeybind('spacing'));
        this.setAutobeefKeybindBtn?.addEventListener('click', () => this.setKeybind('autobeef'));
        this.setBurstKeybindBtn?.addEventListener('click', () => this.setKeybind('burstMode'));
        this.setPriorityKeybindBtn?.addEventListener('click', () => this.setKeybind('priorityMessage'));
    }
    
    setupPythonMessageHandler() {
        // Listen for messages from Python backend via main process
        ipcRenderer.on('python-message', (event, data) => {
            console.log('📨 Received message from Python backend:', data);
            
            try {
                switch (data.type) {
                    case 'message_received':
                        this.displayMessage(data.message, data.user);
                        break;
                    case 'connection_success':
                        this.handleConnectionSuccess(data);
                        break;
                    case 'connection_error':
                        this.handleConnectionError(data);
                        break;
                    case 'connection_warning':
                        this.handleConnectionWarning(data);
                        break;
                    case 'message_sent':
                        this.handleMessageSent(data);
                        break;
                    case 'queue_updated':
                        this.updateQueueDisplay(data.count);
                        break;
                    case 'disconnected':
                        this.handleDisconnected();
                        break;
                    default:
                        console.log('📨 Unhandled message type:', data.type, data);
                }
            } catch (error) {
                console.error('❌ Error handling Python message:', error, data);
            }
        });
        
        console.log('✅ Python message handler setup complete');
    }
    
    setupKeybinds() {
        document.addEventListener('keydown', (e) => {
            // Don't trigger keybinds when setting keybinds
            if (this.settingKeybind) {
                return;
            }
            
            const keyCombo = this.getKeyCombo(e);
            if (!keyCombo) return;
            
            console.log('🔑 Key pressed:', keyCombo, 'Current keybinds:', this.keybinds);
            
            // Prevent default browser behavior for our keybinds
            const allKeybinds = Object.values(this.keybinds).filter(k => k);
            if (allKeybinds.includes(keyCombo)) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            // Check custom keybinds
            if (this.keybinds.autobeef && keyCombo === this.keybinds.autobeef) {
                console.log('🔑 Autobeef keybind triggered');
                this.toggleAutobeef();
                return;
            }
            
            if (this.keybinds.pauseQueue && keyCombo === this.keybinds.pauseQueue) {
                console.log('🔑 Pause queue keybind triggered');
                const wasPaused = this.queuePaused;
                this.toggleQueuePause();
                this.showNotification(`Queue ${wasPaused ? 'resumed' : 'paused'}`, 'info');
                return;
            }
            
            if (this.keybinds.spacing && keyCombo === this.keybinds.spacing) {
                console.log('🔑 Spacing keybind triggered');
                if (this.spacingEnabled) {
                    this.spacingEnabled.checked = !this.spacingEnabled.checked;
                    const isEnabled = this.spacingEnabled.checked;
                    this.showNotification(`Spacing ${isEnabled ? 'enabled' : 'disabled'}`, 'info');
                }
                return;
            }
            
            if (this.keybinds.burstMode && keyCombo === this.keybinds.burstMode) {
                console.log('🔑 Burst mode keybind triggered');
                this.toggleBurstFeature();
                return;
            }
            
            if (this.keybinds.priorityMessage && keyCombo === this.keybinds.priorityMessage) {
                console.log('🔑 Priority message keybind triggered');
                this.togglePriorityMessage();
                return;
            }
        });
    }
    
    getKeyCombo(e) {
        const parts = [];
        if (e.ctrlKey) parts.push('Ctrl');

        // Distinguish between left and right Alt keys
        if (e.altKey) {
            // Check if it's the right Alt key (AltGraph or location 2)
            if (e.key === 'AltGraph' || e.location === KeyboardEvent.DOM_KEY_LOCATION_RIGHT) {
                parts.push('RightAlt');
            } else {
                parts.push('Alt');
            }
        }

        if (e.shiftKey) parts.push('Shift');

        let key = e.key;
        if (key === ' ') key = 'Space';

        // Handle single modifier keys
        if (key === 'Control' || key === 'Alt' || key === 'AltGraph' || key === 'Shift') {
            // If it's just a single modifier key, return it
            if ((key === 'Alt' || key === 'AltGraph') && !e.ctrlKey && !e.shiftKey) {
                // Check if it's the right Alt key
                if (key === 'AltGraph' || e.location === KeyboardEvent.DOM_KEY_LOCATION_RIGHT) {
                    return 'RightAlt';
                } else {
                    return 'Alt';
                }
            }
            if (key === 'Control' && !e.altKey && !e.shiftKey) return 'Control';
            if (key === 'Shift' && !e.altKey && !e.ctrlKey) return 'Shift';
            return null;
        }

        parts.push(key);
        return parts.join('+');
    }
    
    setKeybind(action) {
        if (this.settingKeybind) return;
        
        this.settingKeybind = action;
        const inputElement = this.getKeybindInput(action);
        const buttonElement = this.getKeybindButton(action);
        
        if (!inputElement || !buttonElement) return;
        
        inputElement.value = 'Press any key...';
        inputElement.style.background = '#5865f2';
        buttonElement.textContent = 'Cancel';
        
        const handleKeyPress = (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            const keyCombo = this.getKeyCombo(e);
            if (keyCombo) {
                this.keybinds[action] = keyCombo;
                inputElement.value = keyCombo;
                this.saveKeybinds();
            }
            
            this.finishKeybindSetting(inputElement, buttonElement);
            document.removeEventListener('keydown', handleKeyPress, true);
        };
        
        const handleCancel = () => {
            this.finishKeybindSetting(inputElement, buttonElement);
            document.removeEventListener('keydown', handleKeyPress, true);
            buttonElement.removeEventListener('click', handleCancel);
        };
        
        document.addEventListener('keydown', handleKeyPress, true);
        buttonElement.addEventListener('click', handleCancel);
    }
    
    finishKeybindSetting(inputElement, buttonElement) {
        const currentAction = this.settingKeybind;
        this.settingKeybind = null;
        inputElement.style.background = '';
        buttonElement.textContent = 'Set';
        
        // Update display
        if (currentAction) {
            inputElement.value = this.keybinds[currentAction] || '';
        }
    }
    
    getKeybindInput(action) {
        switch (action) {
            case 'pauseQueue': return this.pauseKeybindInput;
            case 'spacing': return this.spacingKeybindInput;
            case 'autobeef': return this.autobeefKeybindInput;
            case 'burstMode': return this.burstKeybindInput;
            case 'priorityMessage': return this.priorityKeybindInput;
            default: return null;
        }
    }
    
    getKeybindButton(action) {
        switch (action) {
            case 'pauseQueue': return this.setPauseKeybindBtn;
            case 'spacing': return this.setSpacingKeybindBtn;
            case 'autobeef': return this.setAutobeefKeybindBtn;
            case 'burstMode': return this.setBurstKeybindBtn;
            case 'priorityMessage': return this.setPriorityKeybindBtn;
            default: return null;
        }
    }
    
    saveKeybinds() {
        localStorage.setItem('zephyr_keybinds', JSON.stringify(this.keybinds));
    }
    
    loadKeybinds() {
        const saved = localStorage.getItem('zephyr_keybinds');
        if (saved) {
            this.keybinds = { ...this.keybinds, ...JSON.parse(saved) };
        }
        
        // Update input displays
        if (this.pauseKeybindInput) this.pauseKeybindInput.value = this.keybinds.pauseQueue || 'F1';
        if (this.spacingKeybindInput) this.spacingKeybindInput.value = this.keybinds.spacing || '';
        if (this.autobeefKeybindInput) this.autobeefKeybindInput.value = this.keybinds.autobeef || 'Escape';
        if (this.burstKeybindInput) this.burstKeybindInput.value = this.keybinds.burstMode || 'F2';
        if (this.priorityKeybindInput) this.priorityKeybindInput.value = this.keybinds.priorityMessage || 'RightAlt';
    }
    
    setupPythonMessageHandler() {
        ipcRenderer.on('python-message', (event, data) => {
            console.log('📨 Python message received:', data);
            
            try {
                switch (data.type) {
                    case 'connection_success':
                        this.handleConnectionSuccess(data);
                        break;

                    case 'connection_error':
                        this.handleConnectionError(data);
                        break;

                    case 'connection_warning':
                        this.handleConnectionWarning(data);
                        break;

                    case 'disconnected':
                        this.handleDisconnected();
                        break;
                        
                    case 'message_received':
                        console.log('📨 Processing message_received:', data);
                        this.handleMessageReceived(data);
                        break;
                        
                    case 'message_sent':
                        console.log('📤 Message sent confirmation:', data);
                        break;
                        
                    case 'typing_start':
                        this.handleTypingStart(data);
                        break;
                        
                    case 'session_stats':
                        this.handleSessionStats(data);
                        break;
                        
                    case 'queue_paused':
                        this.handleQueuePaused(data);
                        break;
                        
                    case 'queue_resumed':
                        this.handleQueueResumed(data);
                        break;
                        
                    case 'queue_cleared':
                        this.handleQueueCleared(data);
                        break;
                        
                    case 'queue_size_update':
                        this.handleQueueSizeUpdate(data);
                        break;
                        
                    case 'settings_updated':
                        this.handleSettingsUpdated(data);
                        break;
                        
                    case 'autobeef_started':
                        this.handleAutobeefStarted(data);
                        break;
                        
                    case 'autobeef_stopped':
                        this.handleAutobeefStopped(data);
                        break;
                        
                    case 'autobeef_request_message':
                        this.handleAutobeefRequestMessage(data);
                        break;
                        
                    case 'burst_executed':
                        this.handleBurstExecuted(data);
                        break;
                        
                    case 'burst_complete':
                        this.handleBurstComplete(data);
                        break;
                        
                    case 'priority_message_sent':
                        this.handlePriorityMessageSent(data);
                        break;
                        
                    case 'priority_message_error':
                        this.handlePriorityMessageError(data);
                        break;
                        
                    // Removed connection warning handlers - no more popups
                        
                    default:
                        console.log('❓ Unknown message type:', data.type);
                }
            } catch (error) {
                console.error('❌ Error handling Python message:', error);
                console.error('❌ Message data:', data);
                
                // Try to recover by reinitializing elements if needed
                if (data.type === 'message_received') {
                    this.reinitializeMessageContainer();
                }
            }
        });
    }
    
    reinitializeMessageContainer() {
        console.log('🔄 Attempting to reinitialize message container...');
        
        // Try to find the container again
        this.messagesContainer = document.getElementById('messagesContainer');
        
        if (!this.messagesContainer) {
            console.error('❌ Could not find messagesContainer - creating fallback');
            
            // Create a fallback container if it doesn't exist
            const chatArea = document.querySelector('.chat-area') || document.querySelector('.main-content');
            if (chatArea) {
                const fallbackContainer = document.createElement('div');
                fallbackContainer.id = 'messagesContainer';
                fallbackContainer.className = 'messages-container';
                fallbackContainer.style.cssText = `
                    height: 400px;
                    overflow-y: auto;
                    border: 1px solid #ccc;
                    padding: 10px;
                    background: #f9f9f9;
                `;
                chatArea.appendChild(fallbackContainer);
                this.messagesContainer = fallbackContainer;
                console.log('✅ Created fallback message container');
            }
        } else {
            console.log('✅ Message container found and reinitialized');
        }
    }
    
    async connectToChannel() {
        const channelId = this.channelInput?.value?.trim();
        const token = this.tokenInput?.value?.trim();
        
        if (!token) {
            this.showNotification('Discord token is required', 'error');
            return;
        }
        
        if (!channelId || !channelId.match(/^\d{17,19}$/)) {
            this.showNotification('Valid Discord channel ID is required (18-19 digits)', 'error');
            return;
        }
        
        console.log('🔌 Connecting to Discord channel:', channelId);
        
        if (this.connectBtn) {
            this.connectBtn.disabled = true;
            this.connectBtn.textContent = 'Connecting...';
            this.connectBtn.className = 'btn btn-warning';
        }
        
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'connect',
                token: token,
                channel_id: channelId
            });
            
        } catch (error) {
            console.error('❌ Connection request failed:', error);
            this.handleConnectionError({ error: error.message });
        }
    }

    handleConnectionSuccess(data) {
        console.log('🎉 Connected to Discord:', data);

        this.isConnected = true;
        this.currentUser = data.user;
        this.currentChannel = data.channel_name;

        // Store user ID to persist through reconnections for purple name color
        if (data.user?.id) {
            this.storedUserId = data.user.id;
            console.log('💜 Stored user ID for purple name persistence:', this.storedUserId);
        }
        
        if (this.connectBtn) {
            this.connectBtn.disabled = false;
            this.connectBtn.textContent = 'Disconnect';
            this.connectBtn.className = 'btn btn-danger';
        }
        
        this.updateConnectionStatus(true);
        
        if (this.channelName) {
            this.channelName.textContent = `#${data.channel_name}`;
        }
        
        // Show user info
        this.showNotification(`Connected as ${data.user.display_name || data.user.username}`, 'success');
        
        // Update UI to show we're connected to Discord
        this.updateDiscordUI(data);
    }

    updateDiscordUI(data) {
        // Update connection status with Discord branding
        if (this.connectionStatus) {
            this.connectionStatus.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="status-dot bg-success me-2"></div>
                    <span>Connected to Discord</span>
                </div>
            `;
        }
        
        // Show current user info
        const userInfo = document.getElementById('userInfo');
        if (userInfo && data.user) {
            userInfo.innerHTML = `
                <div class="d-flex align-items-center">
                    ${data.user.avatar ? `<img src="${data.user.avatar}" class="rounded-circle me-2" width="24" height="24">` : ''}
                    <span class="text-muted">${data.user.display_name || data.user.username}</span>
                </div>
            `;
        }
    }

    handleConnectionError(data) {
        console.error('❌ Connection failed:', data);

        this.isConnected = false;

        if (this.connectBtn) {
            this.connectBtn.disabled = false;
            this.connectBtn.textContent = 'Connect';
            this.connectBtn.className = 'btn btn-success';
        }

        this.updateConnectionStatus(false);
        this.showNotification(`Connection failed: ${data.error || data.message}`, 'error');
    }

    handleConnectionWarning(data) {
        console.warn('⚠️ Connection warning:', data);

        // Don't change connection status for warnings, just show notification
        this.showNotification(data.message, 'warning');
    }
    
    async disconnectFromChannel() {
        console.log('🔌 Disconnecting from Discord...');
        
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'disconnect'
            });
            
        } catch (error) {
            console.error('❌ Disconnect failed:', error);
        }
        
        this.handleDisconnected();
    }

    handleDisconnected() {
        console.log('🔌 Disconnected from Discord');
        
        this.isConnected = false;
        this.currentUser = null;
        this.currentChannel = null;
        
        if (this.connectBtn) {
            this.connectBtn.disabled = false;
            this.connectBtn.textContent = 'Connect';
            this.connectBtn.className = 'btn btn-success';
        }
        
        this.updateConnectionStatus(false);
        
        if (this.channelName) {
            this.channelName.textContent = 'Not connected';
        }
        
        // Clear messages
        if (this.messagesContainer) {
            this.messagesContainer.innerHTML = '';
        }
        
        this.showNotification('Disconnected from Discord', 'info');
    }
    
    handleSessionStats(data) {
        // Update session time display
        const sessionTimeElement = document.getElementById('sessionTime');
        if (sessionTimeElement) {
            const minutes = Math.floor(data.total_session_time / 60);
            const seconds = Math.floor(data.total_session_time % 60);
            sessionTimeElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        // Update autobeef session time
        const autobeefTimeElement = document.getElementById('autobeefSessionTime');
        if (autobeefTimeElement) {
            const minutes = Math.floor(data.autobeef_session_time / 60);
            const seconds = Math.floor(data.autobeef_session_time % 60);
            autobeefTimeElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        // Update message count
        const messagesSentElement = document.getElementById('messagesSent');
        if (messagesSentElement) {
            messagesSentElement.textContent = data.message_count;
        }
        
        // Update WPM counter
        const wpmElement = document.getElementById('wpmCount');
        if (wpmElement) {
            wpmElement.textContent = Math.round(data.wpm || 0);
        }
    }
    
    handleQueuePaused(data) {
        this.queuePaused = true;
        if (this.pauseQueue) {
            this.pauseQueue.innerHTML = '<i class="fas fa-play"></i> Resume';
            this.pauseQueue.className = 'btn btn-success';
        }
    }
    
    handleQueueResumed(data) {
        this.queuePaused = false;
        if (this.pauseQueue) {
            this.pauseQueue.innerHTML = '<i class="fas fa-pause"></i> Pause';
            this.pauseQueue.className = 'btn btn-warning';
        }
    }
    
    handleQueueCleared(data) {
        this.messageQueue = [];
        this.updateQueueDisplay();
    }
    
    handleQueueSizeUpdate(data) {
        console.log('📊 Queue size update:', data.size);
        
        // Update queue count in message input area - always show
        const queueCountElement = document.querySelector('.queue-count');
        if (queueCountElement) {
            queueCountElement.textContent = data.size;
            queueCountElement.style.display = 'inline-block'; // Always visible
        }
        
        // Update queue count in queue tab
        if (this.queueCount) {
            this.queueCount.textContent = data.size;
        }
        
        // Update queue display visibility - always show
        const queueInfo = document.querySelector('.queue-info');
        if (queueInfo) {
            queueInfo.style.display = 'flex'; // Always visible
        }
    }
    
    handleSettingsUpdated(data) {
        if (data.message_delay !== undefined && this.delayDisplay) {
            this.delayDisplay.textContent = `${data.message_delay}s`;
        }
    }
    
    handleAutobeefStarted(data) {
        this.autobeefActive = true;
        this.updateAutobeefStatus();
        this.showNotification('Autobeef started', 'success');
        console.log('🤖 Autobeef started from backend');
    }
    
    handleAutobeefStopped(data) {
        this.autobeefActive = false;
        this.updateAutobeefStatus();
        this.showNotification('Autobeef stopped', 'info');
        console.log('🛑 Autobeef stopped from backend');
    }
    
    async handleAutobeefRequestMessage(data) {
        console.log('🤖 Backend requesting autobeef message');

        try {
            // Use the advanced anti-repeat system to get next valid message
            let message = await this.getNextValidMessage();

            if (!message) {
                console.warn('⚠️ No valid message could be generated');
                return;
            }

            // Apply prefix/suffix if enabled
            if (this.prefixSuffixEnabled?.checked) {
                const prefix = this.messagePrefix?.value || '';
                const suffix = this.messageSuffix?.value || '';
                message = `${prefix}${message}${suffix}`;
            }

            // Apply spacing if enabled (replace spaces with newlines)
            if (this.spacingEnabled?.checked) {
                message = message.replace(/ /g, '\n');
            }

            // Send the generated message back to backend
            await ipcRenderer.invoke('send-to-python', {
                action: 'autobeef_send_message',
                content: message
            });

            console.log('🤖 Sent autobeef message to backend:', message);

        } catch (error) {
            console.error('❌ Error generating autobeef message:', error);
        }
    }
    
    handleBurstExecuted(data) {
        console.log(`💥 ${data.burst_type} burst executed:`, data);
        
        // Show notification about the burst
        const burstTypeDisplay = {
            'double': 'Double',
            'triple': 'Triple', 
            'quad': 'Quad'
        }[data.burst_type] || data.burst_type;
        
        this.showNotification(
            `${burstTypeDisplay} burst sent ${data.message_count} messages simultaneously`, 
            'success'
        );
    }
    
    handleBurstComplete(data) {
        console.log(`💥 Burst feature complete:`, data);
        this.showNotification(
            `Burst complete: ${data.successful}/${data.total} messages sent successfully!`,
            'success'
        );

        // Immediately reset delay to 0.1s after burst completes
        this.resetDelayAfterBurst();
    }

    async resetDelayAfterBurst() {
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'reset_delay_after_burst'
            });
            console.log('⚡ Delay reset to 0.1s after burst completion');
        } catch (error) {
            console.error('❌ Failed to reset delay after burst:', error);
        }
    }
    
    handlePriorityMessageSent(data) {
        console.log(`🔥 Priority message sent:`, data);
        this.showNotification('Priority message sent successfully!', 'success');
    }
    
    handlePriorityMessageError(data) {
        console.log(`❌ Priority message error:`, data);
        this.showNotification(`Priority message failed: ${data.error}`, 'error');
    }
    
    // Removed connection warning popup methods - no more popups
    
    updateConnectionStatus(connected) {
        const statusElement = this.connectionStatus;
        if (!statusElement) return;
        
        const icon = statusElement.querySelector('i');
        const text = statusElement.querySelector('span');
        
        if (connected) {
            icon.className = 'fas fa-circle online';
            text.textContent = 'Connected';
            statusElement.className = 'connection-status online';
        } else {
            icon.className = 'fas fa-circle offline';
            text.textContent = 'Disconnected';
            statusElement.className = 'connection-status offline';
        }
    }
    
    // MESSAGE METHODS
    async sendMessage() {
        if (!this.isConnected) {
            this.showNotification('Not connected to Discord', 'error');
            return;
        }
        
        const content = this.messageInput?.value?.trim();
        if (!content) return;
        
        let finalMessage = content;
        
        // Apply prefix/suffix if enabled
        if (this.prefixSuffixEnabled?.checked) {
            const prefix = this.messagePrefix?.value || '';
            const suffix = this.messageSuffix?.value || '';
            finalMessage = `${prefix}${finalMessage}${suffix}`;
        }
        
        console.log('📤 Sending Discord message:', finalMessage);
        
        // Handle burst feature (30 simultaneous messages)
        if (this.burstFeatureActive) {
            console.log('💥 Burst feature active - sending 30 simultaneous messages');
            this.sendBurstMessages(finalMessage);
            this.burstFeatureActive = false;
            this.updateMessageInputGlow();
            this.clearMessageInput();
            return;
        }
        
        // Handle priority message feature
        if (this.priorityMessageActive) {
            console.log('🔥 Priority message active - sending after 2 second delay');
            setTimeout(() => {
                this.sendPriorityMessage(finalMessage);
            }, 2000);
            this.clearMessageInput();
            return;
        }
        
        // Handle spacing - replace spaces with newlines in a single message
        if (this.spacingEnabled?.checked) {
            finalMessage = finalMessage.replace(/ /g, '\n');
        }

        {
            // Send as single message
            try {
                await ipcRenderer.invoke('send-to-python', {
                    action: 'send_message',
                    content: finalMessage,
                    delay: 0,
                    message_id: Date.now().toString()
                });
            } catch (error) {
                console.error('❌ Failed to send message:', error);
                this.showNotification('Failed to send message', 'error');
            }
        }
        
        // Clear input
        if (this.messageInput) {
            this.messageInput.value = '';
            this.autoResizeTextarea();
        }
    }

    handleMessageReceived(data) {
        console.log('📨 Handling Discord message:', data);
        
        // Ensure we have the messages container
        if (!this.messagesContainer) {
            console.error('❌ Messages container not found!');
            this.messagesContainer = document.getElementById('messagesContainer');
            if (!this.messagesContainer) {
                console.error('❌ Could not find messagesContainer element in DOM!');
                return;
            }
        }
        
        // Validate message data
        if (!data) {
            console.error('❌ No message data received');
            return;
        }
        
        // Add message to chat display with error handling
        try {
            this.addMessageToChat(data);
        } catch (error) {
            console.error('❌ Error adding message to chat:', error);
            console.error('❌ Message data:', data);
        }
        
        // Update typing indicators
        if (data.user && data.user.id !== this.currentUser?.id) {
            this.removeTypingIndicator(data.user.id);
        }
    }

    addMessageToChat(data) {
        console.log('📝 Adding message to chat:', data);
        
        if (!this.messagesContainer) {
            console.error('❌ Messages container not found!');
            return;
        }
        
        // Handle different message data formats
        let messageData, userData;
        
        if (data.message && data.user) {
            // Discord format
            messageData = data.message;
            userData = data.user;
        } else if (data.content && data.author) {
            // Direct message format (like from web chat)
            messageData = {
                id: data.id || Date.now().toString(),
                content: data.content,
                timestamp: data.timestamp || new Date().toISOString()
            };
            userData = {
                id: data.author.id,
                username: data.author.username,
                display_name: data.author.username,
                avatar: data.author.avatar
            };
        } else {
            console.error('❌ Invalid message data format:', data);
            return;
        }
        
        // Validate required fields
        if (!messageData || !userData) {
            console.error('❌ Missing message or user data:', { messageData, userData });
            return;
        }
        
        if (!userData.username) {
            console.error('❌ Missing username in user data:', userData);
            return;
        }
        
        try {
            const messageElement = document.createElement('div');
            messageElement.className = 'message-item mb-2 p-2 rounded';
            messageElement.dataset.messageId = messageData.id || Date.now().toString();
            
            // Different styling for different message types
            let messageClass = 'bg-light';
            if (userData.id === this.currentUser?.id) {
                messageClass = 'bg-primary bg-opacity-10';
            }
            
            messageElement.className += ` ${messageClass}`;
            
            const timestamp = messageData.timestamp ?
                new Date(messageData.timestamp).toLocaleTimeString() :
                new Date().toLocaleTimeString();
            
            // Create avatar URL if available
            const avatarUrl = userData.avatar ?
                (userData.avatar.startsWith('http') ? userData.avatar : `https://cdn.discordapp.com/avatars/${userData.id}/${userData.avatar}.png`) :
                null;
            
            const displayName = userData.display_name || userData.username;
            const username = userData.username;

            // More robust check for own messages - check both current user and stored token user ID
            const isOwnMessage = userData.id === this.currentUser?.id ||
                                 (this.currentUser && userData.id === this.currentUser.id) ||
                                 this.isOwnMessageByToken(userData.id);

            // Check if this message pings the current user
            const isPingingMe = this.checkIfMessagePingsMe(messageData.content || '');

            // Format message content with pings, replies, etc.
            const formattedContent = this.formatMessageContent(messageData.content || '', messageData);

            // Build reply section if this is a reply
            const replySection = this.buildReplySection(messageData);

            // Build reactions section if there are reactions
            const reactionsSection = this.buildReactionsSection(messageData.reactions);

            messageElement.innerHTML = `
                <div class="message-avatar">
                    ${avatarUrl ?
                        `<img src="${avatarUrl}" alt="${username}" onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">` :
                        `<div class="avatar-placeholder">${username[0].toUpperCase()}</div>`
                    }
                </div>
                <div class="message-content">
                    ${replySection}
                    <div class="message-header">
                        <span class="message-author ${isOwnMessage ? 'own-message' : ''}" data-user-id="${userData.id}">${displayName}</span>
                        <span class="message-timestamp">${timestamp}</span>
                    </div>
                    <div class="message-text ${isPingingMe ? 'message-ping-me' : ''}">${formattedContent}</div>
                    ${reactionsSection}
                </div>
            `;

            // Add ping styling to the entire message if it pings the current user
            if (isPingingMe) {
                messageElement.classList.add('message-pings-me');
            }

            // Add right-click context menu for copying user mentions on the entire message
            messageElement.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.copyUserMention(userData.id, displayName);
            });

            // Also add right-click to author name for extra convenience
            const authorElement = messageElement.querySelector('.message-author');
            if (authorElement) {
                authorElement.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                    this.copyUserMention(userData.id, displayName);
                });
            }
            
            // Ensure container still exists before appending
            if (!this.messagesContainer || !this.messagesContainer.parentNode) {
                console.error('❌ Messages container was removed from DOM!');
                this.messagesContainer = document.getElementById('messagesContainer');
                if (!this.messagesContainer) {
                    console.error('❌ Could not re-find messagesContainer!');
                    return;
                }
            }
            
            this.messagesContainer.appendChild(messageElement);
            
            // Auto-scroll to bottom
            requestAnimationFrame(() => {
                if (this.messagesContainer) {
                    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
                }
            });
            
            console.log('✅ Message added to chat successfully');
            
            // Limit message history to prevent memory issues
            const messages = this.messagesContainer.children;
            if (messages.length > 500) {
                messages[0].remove();
            }
            
        } catch (error) {
            console.error('❌ Error creating message element:', error);
            console.error('❌ Data:', { messageData, userData });
        }
    }

    formatMessageContent(content, messageData = null) {
        if (!content) return '<em class="text-muted">No content</em>';

        let formattedContent = content;

        // Handle user mentions (@mentions)
        formattedContent = formattedContent.replace(/<@!?(\d+)>/g, (match, userId) => {
            // Try to get user info from message data or cache
            const userInfo = this.getUserInfo(userId, messageData);
            let displayName = 'Unknown User';

            if (userInfo) {
                displayName = userInfo.display_name || userInfo.global_name || userInfo.username;
            } else {
                // Fallback: try to find in mentions array
                if (messageData && messageData.mentions) {
                    const mentionedUser = messageData.mentions.find(user => user.id === userId);
                    if (mentionedUser) {
                        displayName = mentionedUser.display_name || mentionedUser.global_name || mentionedUser.username;
                    }
                }
            }

            // Check if this mention is for the current user
            const isCurrentUser = userId === (this.currentUser?.id || this.storedUserId);
            const mentionClass = isCurrentUser ? 'mention-me' : 'mention-user';

            return `<span class="user-mention ${mentionClass}" data-user-id="${userId}">@${displayName}</span>`;
        });

        // Handle role mentions
        formattedContent = formattedContent.replace(/<@&(\d+)>/g, (match, roleId) => {
            return `<span class="role-mention" data-role-id="${roleId}">@role</span>`;
        });

        // Handle channel mentions
        formattedContent = formattedContent.replace(/<#(\d+)>/g, (match, channelId) => {
            return `<span class="channel-mention" data-channel-id="${channelId}">#channel</span>`;
        });

        // Basic Discord formatting
        formattedContent = formattedContent
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/__(.*?)__/g, '<u>$1</u>')
            .replace(/~~(.*?)~~/g, '<del>$1</del>')
            .replace(/`(.*?)`/g, '<code class="bg-dark text-light px-1 rounded">$1</code>')
            .replace(/```(.*?)```/gs, '<pre class="bg-dark text-light p-2 rounded"><code>$1</code></pre>')
            .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" class="text-primary">$1</a>');

        return formattedContent;
    }

    // Helper method to get user info for mentions
    getUserInfo(userId, messageData) {
        // Try to get from message mentions first
        if (messageData && messageData.mentions) {
            const mentionedUser = messageData.mentions.find(user => user.id === userId);
            if (mentionedUser) return mentionedUser;
        }

        // Try to get from current user if it's self-mention
        if (userId === (this.currentUser?.id || this.storedUserId)) {
            return this.currentUser;
        }

        // Could implement a user cache here in the future
        return null;
    }

    // Helper method to check if a message is from the current user (more robust)
    isOwnMessageByToken(userId) {
        // Store the current user ID when we connect to persist through reconnections
        if (!this.storedUserId && this.currentUser?.id) {
            this.storedUserId = this.currentUser.id;
        }
        return userId === this.storedUserId;
    }

    // Copy user mention to clipboard
    async copyUserMention(userId, displayName) {
        try {
            const mention = `<@${userId}>`;
            await navigator.clipboard.writeText(mention);
            this.showNotification(`Copied ${displayName || 'User'}'s user id`, 'success');
            console.log('📋 Copied user mention:', mention, 'for user:', displayName);
        } catch (error) {
            console.error('❌ Failed to copy mention:', error);
            this.showNotification('Failed to copy mention', 'error');
        }
    }

    // Check if a message pings the current user
    checkIfMessagePingsMe(content) {
        if (!this.currentUser?.id && !this.storedUserId) return false;

        const userId = this.currentUser?.id || this.storedUserId;
        const mentionPattern = new RegExp(`<@!?${userId}>`, 'g');
        return mentionPattern.test(content);
    }

    // Build reply section for messages
    buildReplySection(messageData) {
        if (!messageData.referenced_message) return '';

        const replyData = messageData.referenced_message;
        const replyAuthor = replyData.author?.display_name || replyData.author?.global_name || replyData.author?.username || 'Unknown User';
        const replyContent = replyData.content ?
            (replyData.content.length > 60 ? replyData.content.substring(0, 60) + '...' : replyData.content) :
            'Click to see attachment';

        // Check if the replied message is from the current user
        const isReplyingToMe = replyData.author?.id === (this.currentUser?.id || this.storedUserId);
        const replyClass = isReplyingToMe ? 'reply-to-me' : '';

        return `
            <div class="message-reply ${replyClass}">
                <div class="reply-bar"></div>
                <div class="reply-content">
                    <span class="reply-author">${replyAuthor}</span>
                    <span class="reply-text">${this.formatMessageContent(replyContent)}</span>
                </div>
            </div>
        `;
    }

    // Build reactions section for messages
    buildReactionsSection(reactions) {
        if (!reactions || reactions.length === 0) return '';

        const reactionsHtml = reactions.map(reaction => {
            // Handle both custom emojis and unicode emojis
            let emoji;
            if (reaction.emoji.id) {
                // Custom emoji - use the name or fallback
                emoji = reaction.emoji.name || '❓';
            } else {
                // Unicode emoji
                emoji = reaction.emoji.name || '❓';
            }

            const count = reaction.count || 0;
            const meReacted = reaction.me || false;

            return `
                <div class="message-reaction ${meReacted ? 'me-reacted' : ''}"
                     data-emoji="${emoji}"
                     data-emoji-id="${reaction.emoji.id || ''}"
                     title="${count} reaction${count !== 1 ? 's' : ''}">
                    <span class="reaction-emoji">${emoji}</span>
                    <span class="reaction-count">${count}</span>
                </div>
            `;
        }).join('');

        return `<div class="message-reactions">${reactionsHtml}</div>`;
    }

    // COMMAND SYSTEM
    async processCommand(message) {
        const args = message.slice(this.commandPrefix.length).trim().split(' ');
        const command = args[0].toLowerCase();
        
        // Handle confirmation responses
        if (this.awaitingConfirmation) {
            if (command === 'yes' || command === 'y') {
                await this.executeConfirmedCommand();
                return { handled: true };
            } else if (command === 'no' || command === 'n') {
                this.awaitingConfirmation = null;
                await this.sendCommandResponse('❌ Command cancelled.');
                return { handled: true };
            }
        }
        
        switch (command) {
            case 'help':
                await this.showHelpCommand();
                return { handled: true };
                
            case 'wordlistadd':
                await this.wordlistAddCommand(args.slice(1));
                return { handled: true };
                
            case 'generatewordlist':
                await this.generateWordlistCommand();
                return { handled: true };
                
            case 'wordlistcount':
                await this.wordlistCountCommand();
                return { handled: true };
                
            case 'wordlistclear':
                await this.wordlistClearCommand();
                return { handled: true };
                
            case 'showindex':
                await this.showIndexCommand();
                return { handled: true };
                
            default:
                // Don't show "command not found" for just "+"
                if (message.trim() === this.commandPrefix) {
                    return { handled: true };
                }
                return { handled: false };
        }
    }
    
    async sendCommandResponse(response) {
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'send_message',
                message: response,
                delay: 0
            });
        } catch (error) {
            console.error('❌ Failed to send command response:', error);
            this.showNotification('Failed to send command response', 'error');
        }
    }
    
    async showHelpCommand() {
        const helpText = `\`\`\`
Commands:
+help - Show this help message
+wordlistadd <words> - Add words/phrases to wordlist
+generatewordlist - Generate and send 5 messages of 50 lines each
+wordlistcount - Show unique sentence combinations possible
+wordlistclear - Clear all words from wordlist (requires confirmation)
+showindex - Display all words/phrases in wordlist
\`\`\``;
        await this.sendCommandResponse(helpText);
    }
    
    async wordlistAddCommand(args) {
        if (args.length === 0) {
            await this.sendCommandResponse('❌ Usage: +wordlistadd <word1> <word2> ...');
            return;
        }
        
        const wordsToAdd = args.join(' ').split(' ').filter(word => word.trim());
        let addedCount = 0;
        
        for (const word of wordsToAdd) {
            const trimmedWord = word.trim();
            if (trimmedWord) {
                // Check for duplicates (case-insensitive)
                const exists = this.wordlistGenerator.wordsPhrases.some(item =>
                    item.toLowerCase() === trimmedWord.toLowerCase()
                );
                
                if (!exists) {
                    this.wordlistGenerator.wordsPhrases.push(trimmedWord);
                    addedCount++;
                }
            }
        }
        
        if (addedCount > 0) {
            this.updateWordPhrasesPreview();
            this.saveWordlistSettings();
            await this.sendCommandResponse(`✅ Added ${addedCount} unique word(s)/phrase(s) to wordlist. Total: ${this.wordlistGenerator.wordsPhrases.length}`);
        } else {
            await this.sendCommandResponse('⚠️ No new words/phrases added (all items already exist in wordlist)');
        }
    }
    
    async generateWordlistCommand() {
        if (this.wordlistGenerator.wordsPhrases.length === 0) {
            await this.sendCommandResponse('❌ No words/phrases in wordlist. Add some items first with +wordlistadd');
            return;
        }
        
        await this.sendCommandResponse('🎲 Generating wordlist... (5 messages of 50 lines each)');
        
        const wordsPerSentence = this.wordlistGenerator.wordsPerSentence;
        
        // Generate 5 messages of 50 lines each
        for (let messageNum = 1; messageNum <= 5; messageNum++) {
            const lines = [];
            
            for (let lineNum = 1; lineNum <= 50; lineNum++) {
                const lineItems = [];
                const usedInThisLine = new Set();
                
                // Generate multiple words/phrases per line based on wordsPerSentence setting
                for (let j = 0; j < wordsPerSentence; j++) {
                    let attempts = 0;
                    let selectedItem = null;
                    
                    // Try to find an unused item for this line
                    while (attempts < 50) {
                        const randomItem = this.wordlistGenerator.wordsPhrases[Math.floor(Math.random() * this.wordlistGenerator.wordsPhrases.length)];
                        
                        if (!usedInThisLine.has(randomItem)) {
                            selectedItem = randomItem;
                            usedInThisLine.add(randomItem);
                            break;
                        }
                        attempts++;
                    }
                    
                    // If we couldn't find an unused item, just use a random one
                    if (!selectedItem) {
                        selectedItem = this.wordlistGenerator.wordsPhrases[Math.floor(Math.random() * this.wordlistGenerator.wordsPhrases.length)];
                    }
                    
                    const finalItem = this.applyTypos(selectedItem);
                    lineItems.push(finalItem);
                }
                
                lines.push(lineItems.join(' '));
            }
            
            const messageContent = lines.join('\n');
            
            // Add delay between messages
            if (messageNum > 1) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            await this.sendCommandResponse(messageContent);
        }
    }
    
    async wordlistCountCommand() {
        if (this.wordlistGenerator.wordsPhrases.length === 0) {
            await this.sendCommandResponse('❌ No words/phrases in wordlist');
            return;
        }
        
        const itemCount = this.wordlistGenerator.wordsPhrases.length;
        
        const response = `📊 **Wordlist Statistics:**
• Total words/phrases: ${itemCount}
• Typo chance: ${this.wordlistGenerator.typoChance}%`;
        
        await this.sendCommandResponse(response);
    }
    
    async wordlistClearCommand() {
        if (this.wordlistGenerator.wordsPhrases.length === 0) {
            await this.sendCommandResponse('❌ Wordlist is already empty');
            return;
        }
        
        this.awaitingConfirmation = 'clear';
        await this.sendCommandResponse(`⚠️ **WARNING:** This will delete all ${this.wordlistGenerator.wordsPhrases.length} words/phrases from your wordlist!\nType \`+yes\` to confirm or \`+no\` to cancel.`);
    }
    
    async showIndexCommand() {
        if (this.wordlistGenerator.wordsPhrases.length === 0) {
            await this.sendCommandResponse('❌ No words/phrases in wordlist');
            return;
        }
        
        const indexList = this.wordlistGenerator.wordsPhrases.map((item, index) => `${index + 1}. ${item}`).join('\n');
        const response = `📋 **Wordlist Index (${this.wordlistGenerator.wordsPhrases.length} words/phrases):**\n\`\`\`\n${indexList}\n\`\`\``;
        
        await this.sendCommandResponse(response);
    }
    
    async executeConfirmedCommand() {
        if (this.awaitingConfirmation === 'clear') {
            this.wordlistGenerator.wordsPhrases = [];
            this.updateWordPhrasesPreview();
            this.saveWordlistSettings();
            await this.sendCommandResponse('✅ Wordlist cleared successfully');
        }
        this.awaitingConfirmation = null;
    }
    
    // Helper functions for calculations
    factorial(n) {
        if (n <= 1) return 1;
        let result = 1;
        for (let i = 2; i <= n; i++) {
            result *= i;
        }
        return result;
    }
    
    permutation(n, r) {
        if (r > n) return 0;
        let result = 1;
        for (let i = n; i > n - r; i--) {
            result *= i;
        }
        return result;
    }
    
    handleMessageSent(data) {
        console.log('📤 Message sent:', data.content);
        this.showNotification('Message sent successfully', 'success');
    }
    
    handleMessageFailed(data) {
        this.showNotification(`Message failed: ${data.error}`, 'error');
        console.error('❌ Message failed:', data.error);
    }
    // WORDLIST GENERATOR METHODS
    addWordPhrase() {
        const input = this.newWordPhraseInput?.value.trim();
        if (!input) return;
        
        // Check for duplicates (case-insensitive)
        const exists = this.wordlistGenerator.wordsPhrases.some(item =>
            item.toLowerCase() === input.toLowerCase()
        );
        
        if (!exists) {
            this.wordlistGenerator.wordsPhrases.push(input);
            this.updateWordPhrasesPreview();
            this.newWordPhraseInput.value = '';
            this.saveWordIndexToFile();
            this.showNotification(`Added word/phrase "${input}"`, 'success');
        } else {
            this.showNotification('Word/phrase already exists (case-insensitive check)', 'warning');
        }
    }
    
    removeWordPhrase(item) {
        const index = this.wordlistGenerator.wordsPhrases.indexOf(item);
        if (index > -1) {
            this.wordlistGenerator.wordsPhrases.splice(index, 1);
            this.updateWordPhrasesPreview();
            this.saveWordIndexToFile();
            this.showNotification(`Removed word/phrase "${item}"`, 'info');
        }
    }
    
    clearWordPhrases() {
        // Show confirmation modal
        this.showConfirmationModal(
            'Clear All Words/Phrases',
            'Are you sure you want to clear all words and phrases? This action cannot be undone.',
            () => {
                // Confirmed - clear the words
                this.wordlistGenerator.wordsPhrases = [];
                this.updateWordPhrasesPreview();
                this.saveWordIndexToFile();
                this.showNotification('All words/phrases cleared', 'info');
            }
        );
    }
    
    updateWordPhrasesPreview() {
        if (!this.wordPhrasesPreview || !this.wordPhraseCount) return;
        
        this.wordPhraseCount.textContent = this.wordlistGenerator.wordsPhrases.length;
        
        if (this.wordlistGenerator.wordsPhrases.length === 0) {
            this.wordPhrasesPreview.innerHTML = '<p class="no-words">No words/phrases added yet...</p>';
            return;
        }
        
        const itemsHtml = this.wordlistGenerator.wordsPhrases.map(item => `
            <span class="word-tag">
                ${this.escapeHtml(item)}
                <button class="delete-word" onclick="window.zephyrClient.removeWordPhrase('${this.escapeHtml(item)}')">
                    ×
                </button>
            </span>
        `).join('');
        
        this.wordPhrasesPreview.innerHTML = itemsHtml;
    }
    
    generateRandomSentences() {
        if (this.wordlistGenerator.wordsPhrases.length === 0) {
            this.showNotification('Need words/phrases to generate sentences', 'warning');
            return;
        }

        const sentences = [];
        const linesToGenerate = this.wordlistGenerator.linesToGenerate;
        const wordsPerSentence = this.wordlistGenerator.wordsPerSentence;
        const noRepeating = this.noRepeatingLinesCheckbox?.checked || false;

        // If no repeating lines is enabled, generate all unique combinations first
        if (noRepeating) {
            const uniqueCombinations = this.generateAllUniqueCombinations(wordsPerSentence);
            const maxLines = Math.min(linesToGenerate, uniqueCombinations.length);

            // Shuffle the combinations and take the requested amount
            for (let i = uniqueCombinations.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [uniqueCombinations[i], uniqueCombinations[j]] = [uniqueCombinations[j], uniqueCombinations[i]];
            }

            for (let i = 0; i < maxLines; i++) {
                const combination = uniqueCombinations[i];
                const sentenceItems = combination.map(item => this.applyTypos(item));
                sentences.push(sentenceItems.join(' '));
            }

            if (maxLines < linesToGenerate) {
                this.showNotification(`Generated ${maxLines} unique lines (max possible with current wordlist)`, 'info');
            }
        } else {
            // Original random generation
            for (let i = 0; i < linesToGenerate; i++) {
                const sentenceItems = [];
                const usedInThisSentence = new Set();

                // Generate multiple words/phrases per sentence based on wordsPerSentence setting
                for (let j = 0; j < wordsPerSentence; j++) {
                    let attempts = 0;
                    let selectedItem = null;

                    // Try to find an unused item for this sentence
                    while (attempts < 50) {
                        const randomItem = this.wordlistGenerator.wordsPhrases[Math.floor(Math.random() * this.wordlistGenerator.wordsPhrases.length)];

                        if (!usedInThisSentence.has(randomItem)) {
                            selectedItem = randomItem;
                            usedInThisSentence.add(randomItem);
                            break;
                        }
                        attempts++;
                    }

                    // If we couldn't find an unused item, just use a random one
                    if (!selectedItem) {
                        selectedItem = this.wordlistGenerator.wordsPhrases[Math.floor(Math.random() * this.wordlistGenerator.wordsPhrases.length)];
                    }

                    const finalItem = this.applyTypos(selectedItem);
                    sentenceItems.push(finalItem);
                }

                sentences.push(sentenceItems.join(' '));
            }
        }

        const generatedText = sentences.join('\n');
        if (this.generatedSentence) {
            this.generatedSentence.textContent = generatedText;
        }

        console.log('🎲 Generated sentences:', sentences);
    }

    generateAllUniqueCombinations(wordsPerSentence) {
        const words = this.wordlistGenerator.wordsPhrases;
        const combinations = [];

        // Generate all possible combinations with repetition allowed
        function generateCombinations(currentCombination, remainingSlots) {
            if (remainingSlots === 0) {
                combinations.push([...currentCombination]);
                return;
            }

            for (let i = 0; i < words.length; i++) {
                currentCombination.push(words[i]);
                generateCombinations(currentCombination, remainingSlots - 1);
                currentCombination.pop();
            }
        }

        generateCombinations([], wordsPerSentence);
        return combinations;
    }
    
    copyGeneratedText() {
        if (!this.generatedSentence || !this.generatedSentence.value.trim()) {
            this.showNotification('No text to copy', 'warning');
            return;
        }
        
        navigator.clipboard.writeText(this.generatedSentence.value).then(() => {
            this.showNotification('Text copied to clipboard', 'success');
        }).catch(err => {
            console.error('Failed to copy text:', err);
            this.showNotification('Failed to copy text', 'error');
        });
    }
    
    applyTypos(phrase) {
        // Split phrase into words
        const words = phrase.split(' ');
        const maxTyposPerPhrase = 2;
        let typosApplied = 0;
        
        const processedWords = words.map(word => {
            // Check if we should apply a typo and haven't exceeded max typos per phrase
            if (typosApplied < maxTyposPerPhrase && Math.random() * 100 <= this.wordlistGenerator.typoChance) {
                typosApplied++;
                
                // Determine typo type: Replace (30%), Scrambled (35%), Normal (35%)
                const rand = Math.random();
                if (rand < 0.30) {
                    return this.createReplaceTypo(word);
                } else if (rand < 0.65) {
                    return this.createScrambledTypo(word);
                } else {
                    return this.createNormalTypo(word);
                }
            }
            return word;
        });
        
        return processedWords.join(' ');
    }
    
    createReplaceTypo(word) {
        if (word.length < 2) return word;
        
        const qwertyMap = {
            'q': ['w', 'a'], 'w': ['q', 'e', 's'], 'e': ['w', 'r', 'd'],
            'r': ['e', 't', 'f'], 't': ['r', 'y', 'g'], 'y': ['t', 'u', 'h'],
            'u': ['y', 'i', 'j'], 'i': ['u', 'o', 'k'], 'o': ['i', 'p', 'l'],
            'p': ['o', 'l'], 'a': ['q', 's', 'z'], 's': ['a', 'w', 'd', 'x'],
            'd': ['s', 'e', 'f', 'c'], 'f': ['d', 'r', 'g', 'v'], 'g': ['f', 't', 'h', 'b'],
            'h': ['g', 'y', 'j', 'n'], 'j': ['h', 'u', 'k', 'm'], 'k': ['j', 'i', 'l'],
            'l': ['k', 'o', 'p'], 'z': ['a', 's', 'x'], 'x': ['z', 's', 'd', 'c'],
            'c': ['x', 'd', 'f', 'v'], 'v': ['c', 'f', 'g', 'b'], 'b': ['v', 'g', 'h', 'n'],
            'n': ['b', 'h', 'j', 'm'], 'm': ['n', 'j', 'k']
        };
        
        const chars = word.split('');
        const pos = Math.floor(Math.random() * chars.length);
        const char = chars[pos].toLowerCase();
        
        // Only apply typo to letters that have adjacent keys
        if (qwertyMap[char] && /[a-z]/.test(char)) {
            const nearbyChars = qwertyMap[char];
            const newChar = nearbyChars[Math.floor(Math.random() * nearbyChars.length)];
            // Preserve original case
            chars[pos] = chars[pos] === chars[pos].toUpperCase() ? newChar.toUpperCase() : newChar;
        }
        
        return chars.join('');
    }
    
    createScrambledTypo(word) {
        if (word.length < 4) return word;
        
        const chars = word.split('');
        const middle = chars.slice(1, -1);
        
        // Shuffle middle characters while preserving case
        for (let i = middle.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [middle[i], middle[j]] = [middle[j], middle[i]];
        }
        
        return chars[0] + middle.join('') + chars[chars.length - 1];
    }
    
    createNormalTypo(word) {
        if (word.length < 2) return word;

        const chars = word.split('');
        const pos = Math.floor(Math.random() * (chars.length - 1));

        // Swap two adjacent characters (preserve original case)
        if (pos < chars.length - 1) {
            [chars[pos], chars[pos + 1]] = [chars[pos + 1], chars[pos]];
        }

        return chars.join('');
    }

    // Enhanced wordlist generator methods
    updateMaxUniqueCount() {
        if (!this.noRepeatingLinesCheckbox || !this.maxUniqueCountDisplay) return;

        const wordsCount = this.wordlistGenerator.wordsPhrases.length;
        const wordsPerSentence = parseInt(this.wordsPerSentenceSlider?.value || 5);

        if (wordsCount === 0) {
            this.maxUniqueCountDisplay.textContent = '0';
            return;
        }

        // Calculate maximum unique combinations
        let maxUnique = 1;
        for (let i = 0; i < wordsPerSentence; i++) {
            maxUnique *= wordsCount;
        }

        this.maxUniqueCountDisplay.textContent = maxUnique.toLocaleString();
    }

    // TYPO ADDER METHODS
    loadFromWordlistGenerator() {
        if (this.wordlistGenerator.wordsPhrases.length === 0) {
            this.showNotification('No words/phrases in wordlist generator', 'warning');
            return;
        }

        const wordlistText = this.wordlistGenerator.wordsPhrases.join('\n');
        if (this.typoInputWordlist) {
            this.typoInputWordlist.value = wordlistText;
        }

        this.showNotification('Loaded wordlist from generator', 'success');
    }

    clearTypoInput() {
        if (this.typoInputWordlist) {
            this.typoInputWordlist.value = '';
        }
        if (this.typoOutput) {
            this.typoOutput.value = 'Click "Generate Typos" to create typos from your wordlist';
        }
    }

    generateTypos() {
        if (!this.typoInputWordlist || !this.typoInputWordlist.value.trim()) {
            this.showNotification('Please enter a wordlist first', 'warning');
            return;
        }

        const inputLines = this.typoInputWordlist.value.trim().split('\n').filter(line => line.trim());
        if (inputLines.length === 0) {
            this.showNotification('No valid lines found in input', 'warning');
            return;
        }

        const typoChance = parseInt(this.typoAdderChanceSlider?.value || 30);
        const typosPerWord = parseInt(this.typosPerWordSlider?.value || 1);

        const typoLines = inputLines.map(line => {
            return this.applyTyposToLine(line.trim(), typoChance, typosPerWord);
        });

        if (this.typoOutput) {
            this.typoOutput.value = typoLines.join('\n');
        }

        this.showNotification(`Generated typos for ${typoLines.length} lines`, 'success');
    }

    applyTyposToLine(line, typoChance, maxTyposPerWord) {
        const words = line.split(' ');

        const processedWords = words.map(word => {
            if (word.length < 2) return word;

            let processedWord = word;
            let typosApplied = 0;

            // Apply multiple typos per word if specified
            while (typosApplied < maxTyposPerWord && Math.random() * 100 <= typoChance) {
                const rand = Math.random();
                if (rand < 0.30) {
                    processedWord = this.createReplaceTypo(processedWord);
                } else if (rand < 0.65) {
                    processedWord = this.createScrambledTypo(processedWord);
                } else {
                    processedWord = this.createNormalTypo(processedWord);
                }
                typosApplied++;
            }

            return processedWord;
        });

        return processedWords.join(' ');
    }

    copyTypos() {
        if (!this.typoOutput || !this.typoOutput.value.trim()) {
            this.showNotification('No typos to copy', 'warning');
            return;
        }

        navigator.clipboard.writeText(this.typoOutput.value).then(() => {
            this.showNotification('Typos copied to clipboard', 'success');
        }).catch(err => {
            console.error('Failed to copy typos:', err);
            this.showNotification('Failed to copy typos', 'error');
        });
    }

    addTyposToWordlistGen() {
        if (!this.typoOutput || !this.typoOutput.value.trim()) {
            this.showNotification('No typos to add', 'warning');
            return;
        }

        const typoLines = this.typoOutput.value.trim().split('\n').filter(line => line.trim());

        // Add each typo line to the wordlist generator
        typoLines.forEach(line => {
            if (line.trim() && !this.wordlistGenerator.wordsPhrases.includes(line.trim())) {
                this.wordlistGenerator.wordsPhrases.push(line.trim());
            }
        });

        this.updateWordPhrasesPreview();
        this.updateMaxUniqueCount();
        this.saveWordlistToFile();

        this.showNotification(`Added ${typoLines.length} typos to wordlist generator`, 'success');
    }

    toggleUniqueTyposSettings() {
        if (this.uniqueTyposSettings && this.uniqueTyposModeCheckbox) {
            this.uniqueTyposSettings.style.display = this.uniqueTyposModeCheckbox.checked ? 'flex' : 'none';
        }
    }

    // Enhanced generateTypos method with unique variations support
    generateTypos() {
        if (!this.typoInputWordlist || !this.typoInputWordlist.value.trim()) {
            this.showNotification('Please enter a wordlist first', 'warning');
            return;
        }

        const inputLines = this.typoInputWordlist.value.trim().split('\n').filter(line => line.trim());
        if (inputLines.length === 0) {
            this.showNotification('No valid lines found in input', 'warning');
            return;
        }

        const typoChance = parseInt(this.typoAdderChanceSlider?.value || 30);
        const typosPerWord = parseInt(this.typosPerWordSlider?.value || 1);
        const uniqueMode = this.uniqueTyposModeCheckbox?.checked || false;
        const variations = uniqueMode ? parseInt(this.uniqueVariationsSlider?.value || 5) : 1;

        let allTypoLines = [];

        if (uniqueMode) {
            // Generate multiple unique variations of each line
            for (let v = 0; v < variations; v++) {
                const variationLines = inputLines.map(line => {
                    return this.applyTyposToLine(line.trim(), typoChance, typosPerWord);
                });
                allTypoLines = allTypoLines.concat(variationLines);
            }
        } else {
            // Original single generation
            allTypoLines = inputLines.map(line => {
                return this.applyTyposToLine(line.trim(), typoChance, typosPerWord);
            });
        }

        if (this.typoOutput) {
            this.typoOutput.value = allTypoLines.join('\n');
        }

        const message = uniqueMode ?
            `Generated ${variations} unique variations (${allTypoLines.length} total lines)` :
            `Generated typos for ${allTypoLines.length} lines`;
        this.showNotification(message, 'success');
    }
    
    // AUTOBEEF METHODS
    async toggleAutobeef(suppressPopup = false) {
        if (this.autobeefActive) {
            await this.stopAutobeef(suppressPopup);
        } else {
            await this.startAutobeef(suppressPopup);
        }
    }
    
    async startAutobeef() {
        if (!this.isConnected) {
            this.showNotification('Please connect to Discord first', 'warning');
            return;
        }
        try {
        
            const delay = parseInt(this.autobeefDelay?.value || 5);
            
         await ipcRenderer.invoke('send-to-python', {
                action: 'start_autobeef',
                   delay: delay
            });
            
            console.log('🤖 Autobeef start request sent to backend');
            
        } catch (error) {
            console.error('❌ Failed to start autobeef:', error);
            this.showNotification('Failed to start autobeef', 'error');
        }
    }
    
    async stopAutobeef() {
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'stop_autobeef'
            });
            
            console.log('🛑 Autobeef stop request sent to backend');
            
        } catch (error) {
            console.error('❌ Failed to stop autobeef:', error);
            this.showNotification('Failed to stop autobeef', 'error');
        }
    }
    
    pauseAutobeefForManual() {
        this.autobeefPausedForManual = true;
        console.log('⏸️ Autobeef paused for manual message');
    }
    
    resumeAutobeefAfterManual() {
        this.autobeefPausedForManual = false;
        console.log('▶️ Autobeef resumed after manual message');
    }
    
    updateAutobeefDelay() {
        const delay = parseInt(this.autobeefDelaySlider?.value || 5);
        if (this.autobeefDelayDisplay) {
            this.autobeefDelayDisplay.textContent = `${delay}s`;
        }
        
        // Restart interval with new delay if autobeef is running
        if (this.autobeefActive && this.autobeefInterval) {
            clearInterval(this.autobeefInterval);
            this.autobeefInterval = setInterval(() => {
                if (!this.autobeefPausedForManual) {
                    this.sendAutobeefMessage();
                }
            }, delay * 1000);
        }
    }
    
    async sendAutobeefMessage() {
        if (this.wordlistGenerator.wordsPhrases.length === 0) {
            this.showNotification('Need words/phrases for autobeef', 'warning');
            this.stopAutobeef();
            return;
        }
        
        // Generate a random word/phrase
        const randomItem = this.wordlistGenerator.wordsPhrases[Math.floor(Math.random() * this.wordlistGenerator.wordsPhrases.length)];
        const message = randomItem;
        
        try {
            let finalMessage = message;
            
            // Apply prefix/suffix if enabled
            if (this.prefixSuffixEnabled?.checked) {
                const prefix = this.messagePrefix?.value || '';
                const suffix = this.messageSuffix?.value || '';
                finalMessage = `${prefix}${message}${suffix}`;
            }
            
            await ipcRenderer.invoke('send-to-python', {
                action: 'send_autobeef_message',
                content: finalMessage,
                delay: 0,
                skip_queue: true
            });
            
        } catch (error) {
            console.error('❌ Autobeef message failed:', error);
        }
    }
    
    updateAutobeefStatus() {
        if (this.autobeefStatusDot) {
            this.autobeefStatusDot.className = `status-dot ${this.autobeefActive ? 'online' : 'offline'}`;
        }

        if (this.autobeefStatusText) {
            this.autobeefStatusText.textContent = this.autobeefActive ? 'Running' : 'Stopped';
        }

        if (this.autobeefToggle) {
            this.autobeefToggle.textContent = this.autobeefActive ? 'Stop Autobeef' : 'Start Autobeef';
            this.autobeefToggle.className = this.autobeefActive ? 'btn btn-danger' : 'btn btn-success';
        }
    }

    async updateAutobeefBypassQueue(bypass) {
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'set_autobeef_bypass_queue',
                bypass: bypass
            });

            // Save setting to localStorage
            localStorage.setItem('autobeef_bypass_queue', bypass.toString());

            console.log(`🤖 Autobeef bypass queue: ${bypass ? 'enabled' : 'disabled'}`);
            this.showNotification(
                `Autobeef ${bypass ? 'will bypass queue (direct send)' : 'will use queue system'}`,
                'info'
            );
        } catch (error) {
            console.error('Error updating autobeef bypass queue setting:', error);
            this.showNotification('Failed to update autobeef setting', 'error');
        }
    }

    // ADVANCED ANTI-REPEAT SYSTEM
    normalizeMessage(message) {
        // Advanced normalization to detect typo variations
        let normalized = message.toLowerCase()
            .replace(/\s+/g, ' ')
            .trim()
            .replace(/[.,!?;:]/g, ''); // Remove punctuation

        // Additional normalization for common typo patterns
        normalized = normalized
            .replace(/(\w)\1+/g, '$1') // Remove repeated characters (helllo -> helo)
            .replace(/[0-9]/g, (match) => { // Convert numbers back to letters
                const numToLetter = {'0': 'o', '1': 'i', '3': 'e', '4': 'a', '5': 's', '7': 't', '8': 'b'};
                return numToLetter[match] || match;
            })
            .replace(/[^a-z\s]/g, '') // Remove any remaining special characters
            .replace(/\s+/g, ' ')
            .trim();

        return normalized;
    }

    isMessageTooSimilar(newMessage) {
        const normalized = this.normalizeMessage(newMessage);

        // Check if this exact normalized message was used recently (last 30 messages)
        if (this.recentMessages.includes(normalized)) {
            return true;
        }

        // Check if any similar message exists in history
        for (const [historicalNormalized, data] of this.messageHistory) {
            if (this.calculateSimilarity(normalized, historicalNormalized) > 0.85) {
                // If found similar message, check if it's within the 30-message cooldown
                const messagesSinceLastUse = this.recentMessages.length - this.recentMessages.indexOf(historicalNormalized);
                if (messagesSinceLastUse < this.maxRecentMessages) {
                    return true;
                }
            }
        }

        return false;
    }

    calculateSimilarity(str1, str2) {
        // Simple similarity calculation using Levenshtein distance
        const matrix = [];
        const len1 = str1.length;
        const len2 = str2.length;

        if (len1 === 0) return len2 === 0 ? 1 : 0;
        if (len2 === 0) return 0;

        // Initialize matrix
        for (let i = 0; i <= len1; i++) {
            matrix[i] = [i];
        }
        for (let j = 0; j <= len2; j++) {
            matrix[0][j] = j;
        }

        // Fill matrix
        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[i][j] = Math.min(
                    matrix[i - 1][j] + 1,      // deletion
                    matrix[i][j - 1] + 1,      // insertion
                    matrix[i - 1][j - 1] + cost // substitution
                );
            }
        }

        const maxLen = Math.max(len1, len2);
        const distance = matrix[len1][len2];
        return 1 - (distance / maxLen);
    }

    addMessageToHistory(message) {
        const normalized = this.normalizeMessage(message);

        // Add to recent messages queue
        this.recentMessages.push(normalized);
        if (this.recentMessages.length > this.maxRecentMessages) {
            this.recentMessages.shift();
        }

        // Update message history
        if (this.messageHistory.has(normalized)) {
            const data = this.messageHistory.get(normalized);
            data.lastUsed = Date.now();
            data.useCount++;
        } else {
            this.messageHistory.set(normalized, {
                original: message,
                lastUsed: Date.now(),
                useCount: 1
            });
        }
    }

    async getNextValidMessage() {
        try {
            // Read wordlist directly from wordlist.txt file
            const result = await ipcRenderer.invoke('read-wordlist-file');
            if (!result.success || !result.content) {
                console.warn('⚠️ No wordlist.txt file found for autobeef');
                return null;
            }

            // Split wordlist into lines and filter out empty lines
            const wordlistLines = result.content.split('\n')
                .map(line => line.trim())
                .filter(line => line.length > 0);

            if (wordlistLines.length === 0) {
                console.warn('⚠️ wordlist.txt file is empty');
                return null;
            }

            const noRepeatUntil98 = this.autobeefNoRepeatUntil98?.checked || false;
            const maxAttempts = wordlistLines.length * 3; // Prevent infinite loops
            let attempts = 0;

            while (attempts < maxAttempts) {
                // Pick a random line from wordlist.txt
                const randomLine = wordlistLines[Math.floor(Math.random() * wordlistLines.length)];

                // Apply typos using typo adder logic (with some chance)
                const typoChance = parseInt(this.typoAdderChanceSlider?.value || 30);
                const typosPerWord = parseInt(this.typosPerWordSlider?.value || 1);

                let finalMessage;
                if (this.applyTyposToLine) {
                    finalMessage = this.applyTyposToLine(randomLine, typoChance, typosPerWord);
                } else {
                    // Fallback if typo method not available
                    finalMessage = randomLine;
                    console.warn('⚠️ Typo method not available, using original message');
                }

                // Check if message is too similar to recent messages (using typo-aware detection)
                if (!this.isMessageTooSimilar(finalMessage)) {
                    // Additional check for 98% rule if enabled
                    if (noRepeatUntil98) {
                        const usedPercentage = (this.messageHistory.size / wordlistLines.length) * 100;
                        if (usedPercentage < 98 && this.messageHistory.has(this.normalizeMessage(finalMessage))) {
                            attempts++;
                            continue;
                        }
                    }

                    this.addMessageToHistory(finalMessage);
                    return finalMessage;
                }

                attempts++;
            }

            // If we can't find a unique message, return a random one anyway
            console.warn('⚠️ Could not find unique message, using random fallback');
            const fallbackLine = wordlistLines[Math.floor(Math.random() * wordlistLines.length)];
            const fallbackMessage = this.applyTyposToLine(fallbackLine, 30, 1); // Apply some typos to fallback
            this.addMessageToHistory(fallbackMessage);
            console.log('🔄 Fallback message generated:', fallbackMessage);
            return fallbackMessage;

        } catch (error) {
            console.error('❌ Error reading wordlist for autobeef:', error);
            return null;
        }
    }

    validateEmojiInput(input) {
        try {
            let value = input.value;

            // Only do minimal validation - let users set what they want
            // Just limit length to prevent extremely long inputs
            if (value.length > 10) {
                value = value.substring(0, 10);
                input.value = value;
                this.showNotification('Emoji length limited to 10 characters', 'warning');
            }

            // Check for complex emojis that might need normalization
            if (value && value.length > 1) {
                const hasVariationSelector = value.includes('\uFE0F') || value.includes('\uFE0E');
                const hasZWJ = value.includes('\u200D');

                if (hasVariationSelector || hasZWJ) {
                    console.log(`🔍 Complex emoji detected: "${value}" - will be normalized for Discord`);
                    // Don't show notification for every complex emoji, just log it
                }

                // Special notification for skull and crossbones
                if (value.includes('☠')) {
                    console.log(`🔍 Skull and crossbones emoji detected - will be normalized`);
                }
            }

            // If completely empty, use default (but allow user to clear and type)
            if (value === '') {
                // Don't auto-fill with default - let user type what they want
                return;
            }

        } catch (error) {
            console.error('Error validating emoji input:', error);
            // Don't force a default on error - let user try again
        }
    }

    async updateAutoreact() {
        try {
            const enabled = this.autoreactEnabled?.checked || false;
            // Get the exact value from the input, no default fallback
            let emoji = this.autoreactEmoji?.value?.trim() || '';

            // Debug: Log what we're sending
            console.log(`🔍 Frontend emoji value: "${emoji}" (type: ${typeof emoji}, length: ${emoji?.length})`);

            // If empty, just use empty string (no reactions)
            if (!emoji) {
                console.log(`🔍 No emoji provided - autoreact will be disabled for reactions`);
            }

            // Additional debugging for emoji
            if (emoji) {
                console.log(`🔍 Emoji codepoints:`, [...emoji].map(c => c.codePointAt(0).toString(16)));
                console.log(`🔍 Emoji as JSON:`, JSON.stringify(emoji));
            }

            await ipcRenderer.invoke('send-to-python', {
                action: 'set_autoreact',
                enabled: enabled,
                emoji: emoji
            });

            // Save settings to localStorage
            localStorage.setItem('autoreact_enabled', enabled.toString());
            localStorage.setItem('autoreact_emoji', emoji);

            console.log(`😀 Autoreact: ${enabled ? 'enabled' : 'disabled'} with emoji "${emoji || 'none'}"`);
            this.showNotification(
                `Autoreact ${enabled ? (emoji ? `enabled with ${emoji}` : 'enabled (no emoji - won\'t react)') : 'disabled'}`,
                'info'
            );
        } catch (error) {
            console.error('Error updating autoreact settings:', error);
            this.showNotification('Failed to update autoreact settings', 'error');
        }
    }
    
    updateMessagesSentDisplay() {
        if (this.messagesSent) {
            this.messagesSent.textContent = this.autobeefMessageCount;
        }
    }
    
    clearMessagesSent() {
        this.autobeefMessageCount = 0;
        this.updateMessagesSentDisplay();
        this.showNotification('Messages sent counter cleared', 'info');
    }
    
    // BURST MODE METHODS
    async toggleBurstMode(enabled) {
        this.burstModeEnabled = enabled;
        
        // Check if message delay is at least 0.5 seconds for burst mode
        const currentDelay = parseFloat(this.messageDelay?.value || 0.1);
        if (enabled && currentDelay < 0.5) {
            this.showNotification('Burst mode requires message delay ≥ 0.5 seconds', 'warning');
            this.burstModeEnabledCheckbox.checked = false;
            this.burstModeEnabled = false;
            return;
        }
        
        try {
            if (enabled) {
                await ipcRenderer.invoke('send-to-python', {
                    action: 'start_burst_mode',
                    intervals: this.burstIntervals
                });
                console.log('🚀 Burst mode enabled');
            } else {
                await ipcRenderer.invoke('send-to-python', {
                    action: 'stop_burst_mode'
                });
                console.log('🛑 Burst mode disabled');
            }
            
            this.updateBurstModeUI();
            
        } catch (error) {
            console.error('❌ Failed to toggle burst mode:', error);
            this.showNotification('Failed to toggle burst mode', 'error');
            // Revert checkbox state
            this.burstModeEnabledCheckbox.checked = !enabled;
            this.burstModeEnabled = !enabled;
        }
    }
    
    async updateBurstIntervals() {
        if (!this.burstModeEnabled) return;
        
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'update_burst_intervals',
                intervals: this.burstIntervals
            });
            console.log('⚙️ Burst intervals updated:', this.burstIntervals);
        } catch (error) {
            console.error('❌ Failed to update burst intervals:', error);
        }
    }
    
    updateBurstModeUI() {
        // Update settings panel visibility
        if (this.burstSettings) {
            this.burstSettings.className = this.burstModeEnabled ? 
                'burst-settings enabled' : 'burst-settings';
        }
        
        // Update status indicator
        if (this.burstStatusDot) {
            this.burstStatusDot.className = this.burstModeEnabled ? 
                'status-dot active' : 'status-dot';
        }
        
        if (this.burstStatusText) {
            this.burstStatusText.textContent = this.burstModeEnabled ? 'Active' : 'Disabled';
        }
    }
    
    initializeBurstModeUI() {
        // Set initial slider values and displays
        if (this.doubleBurstInterval) {
            this.doubleBurstInterval.value = this.burstIntervals.double;
            this.doubleBurstDisplay.textContent = `${this.burstIntervals.double}s`;
        }

        if (this.tripleBurstInterval) {
            this.tripleBurstInterval.value = this.burstIntervals.triple;
            this.tripleBurstDisplay.textContent = `${this.burstIntervals.triple}s`;
        }

        if (this.quadBurstInterval) {
            this.quadBurstInterval.value = this.burstIntervals.quad;
            this.quadBurstDisplay.textContent = `${this.burstIntervals.quad}s`;
        }

        this.updateBurstModeUI();
        this.initializeMiniBurstUI();
    }

    // MINI BURST METHODS
    toggleMiniBurst(enabled) {
        this.miniBurstEnabled = enabled;

        if (enabled) {
            console.log('⚡ Mini burst enabled');
            this.startMiniBurstCycle();
        } else {
            console.log('⚡ Mini burst disabled');
            this.stopMiniBurstCycle();
        }

        this.updateMiniBurstUI();
    }

    startMiniBurstCycle() {
        if (!this.miniBurstEnabled) return;

        // Clear any existing timeouts
        this.stopMiniBurstCycle();

        // Calculate random delay between min and max
        const minDelay = this.miniBurstSettings.activationMin * 1000;
        const maxDelay = this.miniBurstSettings.activationMax * 1000;
        const randomDelay = Math.random() * (maxDelay - minDelay) + minDelay;

        console.log(`⚡ Mini burst will activate in ${(randomDelay / 1000).toFixed(1)}s`);

        this.miniBurstTimeout = setTimeout(() => {
            this.activateMiniBurst();
        }, randomDelay);
    }

    stopMiniBurstCycle() {
        if (this.miniBurstTimeout) {
            clearTimeout(this.miniBurstTimeout);
            this.miniBurstTimeout = null;
        }

        if (this.miniBurstDeactivateTimeout) {
            clearTimeout(this.miniBurstDeactivateTimeout);
            this.miniBurstDeactivateTimeout = null;
        }

        if (this.miniBurstActive) {
            this.deactivateMiniBurst();
        }
    }

    activateMiniBurst() {
        if (!this.miniBurstEnabled) return;

        this.miniBurstActive = true;

        // Store original delay
        this.originalDelay = parseFloat(this.messageDelay?.value || 0.1);

        // Set delay to 0.01s
        if (this.messageDelay) {
            this.messageDelay.value = 0.01;
            this.delayDisplay.textContent = '0.01s';
        }

        console.log('⚡ Mini burst activated! Message delay set to 0.01s');
        this.showNotification('Mini burst activated! (0.01s delay)', 'info');

        this.updateMiniBurstUI();

        // Schedule deactivation
        const duration = this.miniBurstSettings.duration * 1000;
        this.miniBurstDeactivateTimeout = setTimeout(() => {
            this.deactivateMiniBurst();
        }, duration);
    }

    deactivateMiniBurst() {
        if (!this.miniBurstActive) return;

        this.miniBurstActive = false;

        // Restore original delay
        if (this.messageDelay && this.originalDelay !== null) {
            this.messageDelay.value = this.originalDelay;
            this.delayDisplay.textContent = `${this.originalDelay}s`;
        }

        console.log(`⚡ Mini burst deactivated! Message delay restored to ${this.originalDelay}s`);
        this.showNotification(`Mini burst deactivated! Delay restored to ${this.originalDelay}s`, 'info');

        this.updateMiniBurstUI();

        // Start next cycle if still enabled
        if (this.miniBurstEnabled) {
            this.startMiniBurstCycle();
        }
    }

    updateMiniBurstUI() {
        // Update settings panel visibility
        if (this.miniBurstSettings) {
            this.miniBurstSettings.style.display = this.miniBurstEnabled ? 'block' : 'none';
        }

        // Update status indicator
        if (this.miniBurstStatusDot) {
            this.miniBurstStatusDot.className = this.miniBurstActive ?
                'status-dot active' : 'status-dot';
        }

        if (this.miniBurstStatusText) {
            if (this.miniBurstActive) {
                this.miniBurstStatusText.textContent = 'Active';
            } else if (this.miniBurstEnabled) {
                this.miniBurstStatusText.textContent = 'Waiting';
            } else {
                this.miniBurstStatusText.textContent = 'Disabled';
            }
        }
    }

    initializeMiniBurstUI() {
        // Set initial slider values and displays
        if (this.miniBurstActivationMin) {
            this.miniBurstActivationMin.value = this.miniBurstSettings.activationMin;
        }

        if (this.miniBurstActivationMax) {
            this.miniBurstActivationMax.value = this.miniBurstSettings.activationMax;
        }

        if (this.miniBurstDuration) {
            this.miniBurstDuration.value = this.miniBurstSettings.duration;
            this.miniBurstDurationDisplay.textContent = `${this.miniBurstSettings.duration}s`;
        }

        this.updateMiniBurstDisplay();
        this.updateMiniBurstUI();
    }

    updateMiniBurstDisplay() {
        if (this.miniBurstActivationDisplay) {
            this.miniBurstActivationDisplay.textContent =
                `${this.miniBurstSettings.activationMin}-${this.miniBurstSettings.activationMax}s`;
        }
    }
    
    // QUEUE METHODS
    async toggleQueuePause(suppressPopup = false) {
        if (!this.isConnected) {
            this.showNotification('Not connected to Discord', 'error');
            return;
        }
        
        try {
            const action = this.queuePaused ? 'resume_queue' : 'pause_queue';
            await ipcRenderer.invoke('send-to-python', { 
                action,
                suppress_popup: suppressPopup 
            });
            
            this.queuePaused = !this.queuePaused;
            
            if (this.pauseQueue) {
                this.pauseQueue.innerHTML = this.queuePaused ? 
                    '<i class="fas fa-play"></i> Resume' : 
                    '<i class="fas fa-pause"></i> Pause';
                this.pauseQueue.className = this.queuePaused ? 'btn btn-success' : 'btn btn-warning';
            }
            
            if (!suppressPopup) {
                this.showNotification(this.queuePaused ? 'Queue paused' : 'Queue resumed', 'info');
            }
            console.log(`📋 Queue ${this.queuePaused ? 'paused' : 'resumed'}`);
        } catch (error) {
            console.error('Error toggling queue pause:', error);
            this.showNotification('Failed to toggle queue pause', 'error');
        }
    }
    
    async clearMessageQueue() {
        if (!this.isConnected) {
            this.showNotification('Not connected to Discord', 'error');
            return;
        }
        
        try {
            await ipcRenderer.invoke('send-to-python', { action: 'clear_queue' });
            
            this.messageQueue = [];
            this.updateQueueDisplay();
            
            this.showNotification('Message queue cleared', 'success');
            console.log('🗑️ Message queue cleared');
        } catch (error) {
            console.error('Error clearing queue:', error);
            this.showNotification('Failed to clear queue', 'error');
        }
    }
    
    clearQueue() {
        if (this.messageQueue.length === 0) {
            this.showNotification('Queue is already empty', 'info');
            return;
        }
        
        const queueSize = this.messageQueue.length;
        this.messageQueue = [];
        this.updateQueueDisplay();
        this.showNotification(`Cleared ${queueSize} messages from queue`, 'success');
        console.log(`🗑️ Cleared ${queueSize} messages from queue`);
    }
    
    updateQueueDisplay() {
        // Update queue counter if it exists
        const queueCounter = document.getElementById('queueCounter');
        if (queueCounter) {
            queueCounter.textContent = this.messageQueue.length;
        }
    }
    
    removeFromQueue(index) {
        this.messageQueue.splice(index, 1);
        this.updateQueueDisplay();
        this.showNotification('Message removed from queue', 'info');
    }
    
    handleQueuePaused() {
        this.queuePaused = true;
        this.updateQueuePauseButton();
    }
    
    handleQueueResumed() {
        this.queuePaused = false;
        this.updateQueuePauseButton();
    }
    
    handleQueueCleared() {
        this.messageQueue = [];
        this.updateQueueDisplay();
    }
    
    // TAB SWITCHING
    switchTab(tabName) {
        this.activeTab = tabName;
        
        // Update tab buttons
        this.featureTabs?.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });
        
        // Update tab content
        this.tabContents?.forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}-content`);
        });
        
        // Show feature panel
        const featurePanel = document.getElementById('featurePanel');
        if (featurePanel) {
            featurePanel.classList.add('active');
        }
        
        console.log(`📑 Switched to ${tabName} tab`);
    }
    
    closeFeaturePanel() {
        const featurePanel = document.getElementById('featurePanel');
        if (featurePanel) {
            featurePanel.classList.remove('active');
        }

        // Remove active state from all tabs
        this.featureTabs?.forEach(tab => {
            tab.classList.remove('active');
        });

        console.log('❌ Feature panel closed');
    }

    // Window control methods
    async minimizeWindow() {
        try {
            await ipcRenderer.invoke('minimize-window');
            console.log('🗕 Window minimized');
        } catch (error) {
            console.error('❌ Failed to minimize window:', error);
        }
    }

    async closeWindow() {
        try {
            await ipcRenderer.invoke('close-window');
            console.log('❌ Window close requested');
        } catch (error) {
            console.error('❌ Failed to close window:', error);
        }
    }
    
    // UTILITY METHODS
    updateSliderValue(slider, display, suffix) {
        if (display) {
            display.textContent = slider.value + suffix;
        }
    }
    
    updateSliderDisplays() {
        if (this.messageDelay && this.delayDisplay) {
            this.updateSliderValue(this.messageDelay, this.delayDisplay, 's');
        }
        if (this.autobeefDelay && this.autobeefDelayDisplay) {
            this.updateSliderValue(this.autobeefDelay, this.autobeefDelayDisplay, 's');
        }
        if (this.wordsPerSentenceSlider && this.wordsPerSentenceDisplay) {
            this.updateSliderValue(this.wordsPerSentenceSlider, this.wordsPerSentenceDisplay, '');
        }
        if (this.typoChanceSlider && this.typoChanceDisplay) {
            this.updateSliderValue(this.typoChanceSlider, this.typoChanceDisplay, '%');
        }
        if (this.linesToGenerateSlider && this.linesToGenerateDisplay) {
            this.updateSliderValue(this.linesToGenerateSlider, this.linesToGenerateDisplay, '');
        }
        if (this.typoAdderChanceSlider && this.typoAdderChanceDisplay) {
            this.updateSliderValue(this.typoAdderChanceSlider, this.typoAdderChanceDisplay, '%');
        }
        if (this.typosPerWordSlider && this.typosPerWordDisplay) {
            this.updateSliderValue(this.typosPerWordSlider, this.typosPerWordDisplay, '');
        }
        if (this.uniqueVariationsSlider && this.uniqueVariationsDisplay) {
            this.updateSliderValue(this.uniqueVariationsSlider, this.uniqueVariationsDisplay, 'x');
        }
    }
    
    startSessionTimer() {
        setInterval(() => {
            this.updateSessionTime();
        }, 1000);
    }
    
    updateSessionTime() {
        let totalTime = this.autobeefSessionTime;
        
        // Add current session time if autobeef is running
        if (this.autobeefStartTime) {
            totalTime += Date.now() - this.autobeefStartTime;
        }
        
        const hours = Math.floor(totalTime / 3600000);
        const minutes = Math.floor((totalTime % 3600000) / 60000);
        const seconds = Math.floor((totalTime % 60000) / 1000);
        
        const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        if (this.sessionTimeDisplay) {
            this.sessionTimeDisplay.textContent = timeString;
        }
    }
    
    autoResizeTextarea() {
        if (!this.messageInput) return;
        
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }
    
    updateWPM() {
        const text = this.messageInput?.value || '';
        const words = text.trim().split(/\s+/).filter(word => word.length > 0).length;
        const timeElapsed = (Date.now() - this.sessionStartTime) / 60000; // minutes
        const wpm = Math.round(words / Math.max(timeElapsed, 0.1));
        
        if (this.wpmCounter) {
            this.wpmCounter.textContent = wpm;
        }
    }
    
    async loadSavedData() {
        // Load saved token
        try {
            const result = await ipcRenderer.invoke('read-token-file');
            if (result.success && result.token) {
                this.tokenInput.value = result.token;
                this.showNotification('Token loaded from file', 'success');
            }
        } catch (error) {
            console.error('Error loading token:', error);
        }
        
        // Load saved channel
        const savedChannel = localStorage.getItem('channel_id');
        if (savedChannel && this.channelInput) {
            this.channelInput.value = savedChannel;
        }
    }
    
    async saveToken() {
        const token = this.tokenInput.value.trim();
        if (!token) {
            this.showNotification('Please enter a token', 'error');
            return;
        }
        
        try {
            const result = await ipcRenderer.invoke('save-token-file', token);
            if (result.success) {
                this.showNotification('Token saved successfully', 'success');
            } else {
                this.showNotification('Failed to save token: ' + result.error, 'error');
            }
        } catch (error) {
            this.showNotification('Error saving token: ' + error.message, 'error');
        }
    }
    
    toggleTokenVisibility() {
        if (!this.tokenInput) return;
        
        const type = this.tokenInput.type === 'password' ? 'text' : 'password';
        this.tokenInput.type = type;
        
        if (this.toggleTokenBtn) {
            this.toggleTokenBtn.innerHTML = type === 'password' ? 
                '<i class="fas fa-eye"></i>' : '<i class="fas fa-eye-slash"></i>';
        }
    }
    
    loadSettings() {
        const savedToken = localStorage.getItem('discord_token');
        if (savedToken && this.tokenInput) {
            this.tokenInput.value = savedToken;
        }

        // Load autobeef bypass queue setting
        const savedBypassQueue = localStorage.getItem('autobeef_bypass_queue');
        if (savedBypassQueue && this.autobeefBypassQueue) {
            this.autobeefBypassQueue.checked = savedBypassQueue === 'true';
            // Also send to backend
            this.updateAutobeefBypassQueue(this.autobeefBypassQueue.checked);
        }

        // Load autoreact settings
        const savedAutoreactEnabled = localStorage.getItem('autoreact_enabled');
        const savedAutoreactEmoji = localStorage.getItem('autoreact_emoji');

        if (this.autoreactEnabled && savedAutoreactEnabled !== null) {
            this.autoreactEnabled.checked = savedAutoreactEnabled === 'true';
        }

        if (this.autoreactEmoji && savedAutoreactEmoji !== null) {
            // Load the exact saved emoji, don't force defaults
            this.autoreactEmoji.value = savedAutoreactEmoji;
        }

        // Send autoreact settings to backend if they exist
        if (savedAutoreactEnabled !== null || savedAutoreactEmoji !== null) {
            this.updateAutoreact();
        }

        // Load wordlist generator settings and words from file
        this.loadWordlistSettings();
    }
    
    saveSettings() {
        localStorage.setItem('commands_enabled', this.commandsEnabled.toString());
    }
    
    saveWordlistSettings() {
        const wordlistData = {
            wordsPerSentence: this.wordlistGenerator.wordsPerSentence,
            typoChance: this.wordlistGenerator.typoChance,
            linesToGenerate: this.wordlistGenerator.linesToGenerate,
            lastSaved: Date.now()
        };
        
        try {
            localStorage.setItem('wordlist_generator_settings', JSON.stringify(wordlistData));
            console.log('💾 Wordlist generator settings saved');
        } catch (error) {
            console.error('❌ Failed to save wordlist settings:', error);
            this.showNotification('Failed to save wordlist settings', 'error');
        }
    }
    
    loadWordlistSettings() {
        try {
            const savedData = localStorage.getItem('wordlist_generator_settings');
            if (savedData) {
                const wordlistData = JSON.parse(savedData);
                
                // Load settings (not words - those come from file)
                if (wordlistData.wordsPerSentence) {
                    this.wordlistGenerator.wordsPerSentence = wordlistData.wordsPerSentence;
                    if (this.wordsPerSentenceSlider) {
                        this.wordsPerSentenceSlider.value = wordlistData.wordsPerSentence;
                    }
                }
                
                if (wordlistData.typoChance !== undefined) {
                    this.wordlistGenerator.typoChance = wordlistData.typoChance;
                    if (this.typoChanceSlider) {
                        this.typoChanceSlider.value = wordlistData.typoChance;
                    }
                }
                
                if (wordlistData.linesToGenerate) {
                    this.wordlistGenerator.linesToGenerate = wordlistData.linesToGenerate;
                    if (this.linesToGenerateSlider) {
                        this.linesToGenerateSlider.value = wordlistData.linesToGenerate;
                    }
                }
                
                // Update displays
                this.updateSliderDisplays();
                this.updateMaxUniqueCount();

                console.log('📂 Loaded wordlist generator settings');
            }
            
            // Auto-load words from wordindex.txt on startup
            this.loadWordIndex();
            
        } catch (error) {
            console.error('❌ Failed to load wordlist settings:', error);
            this.showNotification('Failed to load saved wordlist settings', 'warning');
        }
    }
    
    async saveWordlistToFile() {
        try {
            const wordlistContent = this.wordlistGenerator.words.join('\n');
            const result = await ipcRenderer.invoke('write-wordlist-file', wordlistContent);
            if (result.success) {
                console.log('💾 Wordlist saved to file');
            } else {
                console.error('❌ Failed to save wordlist:', result.error);
                this.showNotification('Failed to save wordlist', 'error');
            }
        } catch (error) {
            console.error('❌ Error saving wordlist:', error);
            this.showNotification('Error saving wordlist', 'error');
        }
    }
    
    async loadWordlistFromFile() {
        try {
            const result = await ipcRenderer.invoke('read-wordlist-file');
            if (result.success && result.content) {
                const words = result.content.split('\n')
                    .map(line => line.trim())
                    .filter(line => line.length > 0);
                
                this.wordlistGenerator.words = words;
                this.updateWordPhrasesPreview();
                this.updateMaxUniqueCount();
                console.log(`📂 Loaded ${words.length} words from wordlist file`);
            }
        } catch (error) {
            console.error('❌ Error loading wordlist:', error);
        }
    }
    
    async saveWordIndexToFile() {
        try {
            const indexContent = {
                words_phrases: this.wordlistGenerator.wordsPhrases
            };
            const content = JSON.stringify(indexContent, null, 2);
            const result = await ipcRenderer.invoke('write-wordindex-file', content);
            if (result.success) {
                console.log('💾 Word index saved to file');
            } else {
                console.error('❌ Failed to save word index:', result.error);
                this.showNotification('Failed to save word index', 'error');
            }
        } catch (error) {
            console.error('❌ Error saving word index:', error);
            this.showNotification('Error saving word index', 'error');
        }
    }
    
    async loadWordIndex() {
        try {
            const result = await ipcRenderer.invoke('read-wordindex-file');
            if (result.success && result.content) {
                try {
                    const indexData = JSON.parse(result.content);
                    
                    // Handle new format
                    if (indexData.words_phrases) {
                        this.wordlistGenerator.wordsPhrases = indexData.words_phrases || [];
                    } else {
                        // Handle old format - merge words and phrases
                        const words = indexData.words || [];
                        const phrases = indexData.phrases || [];
                        this.wordlistGenerator.wordsPhrases = [...words, ...phrases];
                    }
                    
                    this.updateWordPhrasesPreview();
                    this.showNotification(`Loaded ${this.wordlistGenerator.wordsPhrases.length} words/phrases`, 'success');
                    console.log(`📂 Loaded ${this.wordlistGenerator.wordsPhrases.length} words/phrases from index`);
                } catch (parseError) {
                    // Fallback for plain text format
                    const lines = result.content.split('\n')
                        .map(line => line.trim())
                        .filter(line => line.length > 0);
                    this.wordlistGenerator.wordsPhrases = lines;
                    this.updateWordPhrasesPreview();
                    this.showNotification(`Loaded ${lines.length} items (converted from plain text)`, 'warning');
                }
            } else {
                this.showNotification('No word index file found', 'warning');
            }
        } catch (error) {
            console.error('❌ Error loading word index:', error);
            this.showNotification('Error loading word index', 'error');
        }
    }
    
    // TYPING HANDLERS
    handleTypingStart(data) {
        this.typingUsers.add(data.user.username);
        this.updateTypingIndicator();
    }
    
    handleTypingStop(data) {
        this.typingUsers.delete(data.user.username);
        this.updateTypingIndicator();
    }
    
    updateTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (!typingIndicator) return;
        
        if (this.typingUsers.size > 0) {
            const users = Array.from(this.typingUsers);
            const text = users.length === 1 ? 
                `${users[0]} is typing...` : 
                `${users.slice(0, -1).join(', ')} and ${users[users.length - 1]} are typing...`;
            
            typingIndicator.querySelector('.typing-text').textContent = text;
            typingIndicator.style.display = 'block';
        } else {
            typingIndicator.style.display = 'none';
        }
    }
    // NOTIFICATION SYSTEM
    showNotification(message, type = 'info') {
        // Skip rate limit notifications
        if (message.toLowerCase().includes('rate limit')) {
            return;
        }
        
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '6px',
            color: 'white',
            fontWeight: '500',
            zIndex: '10000',
            maxWidth: '300px',
            wordWrap: 'break-word',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        // Set background color based on type
        const colors = {
            success: '#43b581',
            error: '#f04747',
            warning: '#faa61a',
            info: '#7289da'
        };
        notification.style.background = colors[type] || colors.info;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);
        
        // Remove after delay
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    addRippleEffect(element, event) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    showConfirmationModal(title, message, onConfirm) {
        // Create modal overlay
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(4px);
        `;
        
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'confirmation-modal';
        modal.style.cssText = `
            background: #2f3136;
            border-radius: 8px;
            padding: 24px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            border: 1px solid #40444b;
        `;
        
        modal.innerHTML = `
            <div class="modal-header" style="margin-bottom: 16px;">
                <h3 style="color: #ffffff; margin: 0; font-size: 18px;">${title}</h3>
            </div>
            <div class="modal-body" style="margin-bottom: 24px;">
                <p style="color: #b9bbbe; margin: 0; line-height: 1.4;">${message}</p>
            </div>
            <div class="modal-footer" style="display: flex; gap: 12px; justify-content: flex-end;">
                <button class="btn-cancel" style="
                    background: #4f545c;
                    color: #ffffff;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                ">Cancel</button>
                <button class="btn-confirm" style="
                    background: #ed4245;
                    color: #ffffff;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                ">Confirm</button>
            </div>
        `;
        
        overlay.appendChild(modal);
        document.body.appendChild(overlay);
        
        // Add event listeners
        const cancelBtn = modal.querySelector('.btn-cancel');
        const confirmBtn = modal.querySelector('.btn-confirm');
        
        const closeModal = () => {
            document.body.removeChild(overlay);
        };
        
        cancelBtn.addEventListener('click', closeModal);
        confirmBtn.addEventListener('click', () => {
            onConfirm();
            closeModal();
        });
        
        // Close on overlay click
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                closeModal();
            }
        });
        
        // Close on escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
    }
    
    // NEW FEATURE METHODS
    
    // Burst Feature - sends 30 messages simultaneously using threading
    toggleBurstFeature() {
        this.burstFeatureActive = !this.burstFeatureActive;
        console.log(`💥 Burst feature ${this.burstFeatureActive ? 'activated' : 'deactivated'}`);
        this.updateMessageInputGlow();
        
        if (this.burstFeatureActive) {
            this.showNotification('Burst mode activated - next message will send 30 times!', 'info');
        } else {
            this.showNotification('Burst mode deactivated', 'info');
        }
    }
    
    // Priority Message Feature - pauses queue/autobeef, waits 2s, sends once
    togglePriorityMessage() {
        this.priorityMessageActive = !this.priorityMessageActive;
        console.log(`🔥 Priority message ${this.priorityMessageActive ? 'activated' : 'deactivated'}`);
        
        if (this.priorityMessageActive) {
            // Store current pause states
            this.priorityMessageWasPaused.queue = this.queuePaused;
            this.priorityMessageWasPaused.autobeef = !this.autobeefActive;
            
            // Pause queue if not already paused
            if (!this.queuePaused) {
                this.toggleQueuePause(true); // Suppress popup for priority message feature
            }
            
            // Pause autobeef if not already paused
            if (this.autobeefActive) {
                this.toggleAutobeef(true); // Suppress popup for priority message feature
            }
            
            this.showNotification('Priority message activated - queue and autobeef paused', 'success');
        } else {
            // Unpause queue if it wasn't paused before
            if (!this.priorityMessageWasPaused.queue && this.queuePaused) {
                this.toggleQueuePause(true); // Suppress popup for priority message feature
            }
            
            // Unpause autobeef if it wasn't paused before
            if (!this.priorityMessageWasPaused.autobeef && !this.autobeefActive) {
                this.toggleAutobeef(true); // Suppress popup for priority message feature
            }
            
            this.showNotification('Priority message deactivated - queue and autobeef restored', 'info');
        }
        
        this.updateMessageInputGlow();
    }
    
    // Send burst messages (30 simultaneous or fast sequential)
    async sendBurstMessages(message) {
        console.log(`💥 Sending 30 burst messages using ${this.burstType} mode`);

        if (this.burstType === 'tasks') {
            // Tasks mode: Send as fast as possible with 0.01s delay
            await this.sendBurstMessagesTasks(message);
        } else {
            // Threading mode: Send simultaneously (original behavior)
            await this.sendBurstMessagesThreading(message);
        }

        // Reset delay to 0.1s immediately after manual burst
        this.resetDelayAfterBurst();
    }

    // Threading mode: Send all messages simultaneously
    async sendBurstMessagesThreading(message) {
        console.log('💥 Using threading mode - sending 30 messages simultaneously');

        // Create 30 promises to send messages simultaneously
        const promises = [];
        for (let i = 0; i < 30; i++) {
            promises.push(
                ipcRenderer.invoke('send-to-python', {
                    action: 'burst_message',
                    content: message,
                    burst_id: `burst_${Date.now()}_${i}`
                })
            );
        }

        // Wait for all messages to complete
        const results = await Promise.allSettled(promises);

        // Count successful messages for logging
        const successCount = results.filter(result => result.status === 'fulfilled').length;
        console.log(`💥 Threading burst completed: ${successCount}/30 messages sent`);

        // Only show success notification if at least some messages were sent
        if (successCount > 0) {
            this.showNotification(`Threading burst sent ${successCount}/30 messages`, 'success');
        }
    }

    // Tasks mode: Send messages as fast as possible with 0.01s delay
    async sendBurstMessagesTasks(message) {
        console.log('💥 Using tasks mode - sending 30 messages with 0.01s delay');

        let successCount = 0;

        for (let i = 0; i < 30; i++) {
            try {
                await ipcRenderer.invoke('send-to-python', {
                    action: 'burst_message',
                    content: message,
                    burst_id: `burst_${Date.now()}_${i}`
                });
                successCount++;

                // Wait 0.01s before next message (except for the last one)
                if (i < 29) {
                    await new Promise(resolve => setTimeout(resolve, 10));
                }
            } catch (error) {
                console.error(`❌ Failed to send burst message ${i + 1}:`, error);
            }
        }

        console.log(`💥 Tasks burst completed: ${successCount}/30 messages sent`);

        if (successCount > 0) {
            this.showNotification(`Tasks burst sent ${successCount}/30 messages`, 'success');
        }
    }
    
    // Send priority message (single message after 2s delay)
    async sendPriorityMessage(message) {
        console.log('🔥 Sending priority message');
        
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'priority_message',
                content: message,
                priority_id: `priority_${Date.now()}`
            });
            this.showNotification('Priority message sent!', 'success');
        } catch (error) {
            console.error('❌ Failed to send priority message:', error);
            this.showNotification('Failed to send priority message', 'error');
        }
    }
    
    // Update message input glow based on active features
    updateMessageInputGlow() {
        if (!this.messageInput) return;
        
        const container = this.messageInput.closest('.message-input-container');
        if (!container) return;
        
        // Remove all glow classes
        container.classList.remove('burst-mode-active', 'priority-mode-active');
        
        // Add appropriate glow class
        if (this.burstFeatureActive) {
            container.classList.add('burst-mode-active');
        } else if (this.priorityMessageActive) {
            container.classList.add('priority-mode-active');
        }
    }
    
    // Clear message input
    clearMessageInput() {
        if (this.messageInput) {
            this.messageInput.value = '';
            this.autoResizeTextarea();
        }
    }
    
    // Display received message in chat
    displayMessage(message, user) {
        if (!this.messagesContainer) {
            console.error('❌ Messages container not found');
            return;
        }
        
        try {
            // Create message element
            const messageElement = document.createElement('div');
            messageElement.className = 'message';
            messageElement.dataset.messageId = message.id;
            
            // Check if this is our own message
            const isOwnMessage = this.currentUser && user.id === this.currentUser.id;
            if (isOwnMessage) {
                messageElement.classList.add('own-message');
            }
            
            // Format timestamp
            const timestamp = new Date(message.timestamp);
            const timeString = timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            
            // Create avatar URL
            const avatarUrl = user.avatar 
                ? `https://cdn.discordapp.com/avatars/${user.id}/${user.avatar}.png`
                : `https://cdn.discordapp.com/embed/avatars/${parseInt(user.discriminator) % 5}.png`;
            
            // Build message HTML
            messageElement.innerHTML = `
                <div class="message-avatar">
                    <img src="${avatarUrl}" alt="${user.username}" onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-author">${user.display_name || user.username}</span>
                        <span class="message-timestamp">${timeString}</span>
                    </div>
                    <div class="message-text">${this.escapeHtml(message.content)}</div>
                </div>
            `;
            
            // Add message to container
            this.messagesContainer.appendChild(messageElement);
            
            // Auto-scroll to bottom
            this.scrollToBottom();
            
            console.log('✅ Message displayed:', message.content.substring(0, 50));
            
        } catch (error) {
            console.error('❌ Error displaying message:', error);
        }
    }
    
    // Handle connection success
    handleConnectionSuccess(data) {
        this.isConnected = true;
        this.currentUser = data.user;
        this.currentChannel = data.channel_name;
        
        // Update UI
        this.updateConnectionStatus(true);
        if (this.channelName) {
            this.channelName.textContent = data.channel_name || 'Unknown Channel';
        }
        
        // Update connect button
        if (this.connectBtn) {
            this.connectBtn.textContent = 'Disconnect';
            this.connectBtn.disabled = false;
            this.connectBtn.classList.remove('btn-success');
            this.connectBtn.classList.add('btn-danger');
        }
        
        console.log('✅ Connection successful:', data.user.username);
    }
    
    // Handle connection error
    handleConnectionError(data) {
        this.isConnected = false;
        this.currentUser = null;
        this.currentChannel = null;
        
        // Update UI
        this.updateConnectionStatus(false);
        console.error(`Connection failed: ${data.error}`);
        
        // Reset connect button
        if (this.connectBtn) {
            this.connectBtn.textContent = 'Connect';
            this.connectBtn.disabled = false;
            this.connectBtn.classList.remove('btn-danger');
            this.connectBtn.classList.add('btn-success');
        }
        
        console.error('❌ Connection failed:', data.error);
    }
    
    // Handle message sent confirmation
    handleMessageSent(data) {
        console.log('✅ Message sent confirmation:', data.content.substring(0, 50));
        // Could add visual feedback here if needed
    }
    
    // Handle disconnection
    handleDisconnected() {
        this.isConnected = false;
        this.currentUser = null;
        this.currentChannel = null;
        
        // Update UI
        this.updateConnectionStatus(false);
        if (this.channelName) {
            this.channelName.textContent = '';
        }
        
        // Reset connect button
        if (this.connectBtn) {
            this.connectBtn.textContent = 'Connect';
            this.connectBtn.disabled = false;
            this.connectBtn.classList.remove('btn-danger');
            this.connectBtn.classList.add('btn-success');
        }
        
        console.log('🔌 Disconnected from Discord');
    }
    
    // Update connection status indicator
    updateConnectionStatus(connected) {
        if (!this.connectionStatus) return;
        
        const statusIcon = this.connectionStatus.querySelector('i');
        const statusText = this.connectionStatus.querySelector('span');
        
        if (connected) {
            statusIcon.className = 'fas fa-circle';
            statusIcon.style.color = '#23a55a';
            statusText.textContent = 'Connected';
            this.connectionStatus.style.color = '#23a55a';
        } else {
            statusIcon.className = 'fas fa-circle';
            statusIcon.style.color = '#f23f43';
            statusText.textContent = 'Disconnected';
            this.connectionStatus.style.color = '#f23f43';
        }
    }
    
    // Scroll messages container to bottom
    scrollToBottom() {
        if (this.messagesContainer) {
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }
    }
    
    // Connect to Discord channel
    async connectToChannel() {
        const token = this.tokenInput?.value?.trim();
        const channelId = this.channelInput?.value?.trim();
        
        if (!token) {
            console.error('❌ Discord token is required');
            return;
        }
        
        if (!channelId) {
            console.error('❌ Channel ID is required');
            return;
        }
        
        try {
            console.log('🔌 Connecting to Discord...');
            
            // Update button state
            if (this.connectBtn) {
                this.connectBtn.textContent = 'Connecting...';
                this.connectBtn.disabled = true;
            }
            
            // Send connect request to Python backend
            await ipcRenderer.invoke('send-to-python', {
                action: 'connect',
                token: token,
                channel_id: channelId
            });
            
        } catch (error) {
            console.error('❌ Connection failed:', error);
            
            // Reset button state
            if (this.connectBtn) {
                this.connectBtn.textContent = 'Connect';
                this.connectBtn.disabled = false;
            }
        }
    }
    
    // Disconnect from Discord channel
    async disconnectFromChannel() {
        try {
            console.log('🔌 Disconnecting from Discord...');
            
            // Update button state
            if (this.connectBtn) {
                this.connectBtn.textContent = 'Disconnecting...';
                this.connectBtn.disabled = true;
            }
            
            // Send disconnect request to Python backend
            await ipcRenderer.invoke('send-to-python', {
                action: 'disconnect'
            });
            
        } catch (error) {
            console.error('❌ Disconnect failed:', error);
            
            // Reset button state
            if (this.connectBtn) {
                this.connectBtn.textContent = 'Disconnect';
                this.connectBtn.disabled = false;
            }
        }
    }
    
    // Update queue display
    updateQueueDisplay(count) {
        if (this.queueCountInput) {
            this.queueCountInput.textContent = count || 0;
        }
        if (this.queueCount) {
            this.queueCount.textContent = count || 0;
        }
    }
    
    // Escape HTML to prevent XSS
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Validate emoji input and clean invalid characters
    validateEmojiInput(inputElement) {
        if (!inputElement) return;
        
        const value = inputElement.value;
        
        // If empty, that's valid (will use default emoji)
        if (!value.trim()) {
            inputElement.style.borderColor = '';
            return;
        }
        
        // Check if it's a valid emoji format
        if (this.isValidEmoji(value)) {
            inputElement.style.borderColor = '#23a55a'; // Green for valid
        } else {
            // Clear invalid input and show warning
            inputElement.value = '';
            inputElement.style.borderColor = '#f23f43'; // Red for invalid
            inputElement.placeholder = 'Invalid emoji - cleared';
            
            // Reset placeholder after 3 seconds
            setTimeout(() => {
                inputElement.placeholder = 'Enter emoji';
                inputElement.style.borderColor = '';
            }, 3000);
            
            this.showNotification('Invalid emoji format - input cleared', 'error');
        }
    }
    
    // Check if emoji is in valid format
    isValidEmoji(emoji) {
        const trimmed = emoji.trim();
        
        // Check for Unicode emoji (comprehensive regex for all emoji ranges)
        const unicodeEmojiRegex = /^(?:[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F018}-\u{1F270}]|[\u{238C}-\u{2454}]|[\u{20D0}-\u{20FF}]|[\u{FE0F}]|[\u{200D}]|[\u{1F3FB}-\u{1F3FF}])+$/u;
        
        // Check for Discord custom emoji format <:name:id> or <a:name:id>
        const customEmojiRegex = /^<a?:[a-zA-Z0-9_]+:\d+>$/;
        
        // Check for emoji shortcode format :name:
        const shortcodeRegex = /^:[a-zA-Z0-9_+-]+:$/;
        
        // Check if it's a single character that might be an emoji
        const isSingleChar = trimmed.length === 1;
        
        return unicodeEmojiRegex.test(trimmed) || 
               customEmojiRegex.test(trimmed) || 
               shortcodeRegex.test(trimmed) ||
               isSingleChar;
    }
    

    
    // Normalize emoji to ensure proper encoding
    normalizeEmoji(emoji) {
        if (!emoji) return '👍';
        
        // Handle different emoji formats
        const trimmed = emoji.trim();
        
        // If it's already a custom Discord emoji or shortcode, return as-is
        if (trimmed.startsWith('<') && trimmed.endsWith('>')) {
            return trimmed; // Custom Discord emoji
        }
        
        if (trimmed.startsWith(':') && trimmed.endsWith(':') && trimmed.length > 2) {
            return trimmed; // Shortcode
        }
        
        // For Unicode emojis, ensure proper normalization
        try {
            // Normalize Unicode to ensure consistent representation
            return trimmed.normalize('NFC');
        } catch (error) {
            console.warn('Failed to normalize emoji, using default:', error);
            return '👍';
        }
    }
    
    // Save autoreact settings to localStorage
    saveAutoreactSettings() {
        if (!this.autoreactEnabled || !this.autoreactEmoji) return;
        
        const settings = {
            enabled: this.autoreactEnabled.checked,
            emoji: this.normalizeEmoji(this.autoreactEmoji.value.trim()) || ''
        };
        
        localStorage.setItem('zephyr_autoreact_settings', JSON.stringify(settings));
    }
    
    // Load autoreact settings from localStorage
    loadAutoreactSettings() {
        try {
            const saved = localStorage.getItem('zephyr_autoreact_settings');
            if (saved) {
                const settings = JSON.parse(saved);
                
                if (this.autoreactEnabled) {
                    this.autoreactEnabled.checked = settings.enabled || false;
                }
                
                if (this.autoreactEmoji) {
                    this.autoreactEmoji.value = settings.emoji || '';
                }
                
                // Update backend with loaded settings
                this.updateAutoreact();
            }
        } catch (error) {
            console.error('❌ Failed to load autoreact settings:', error);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.zephyrClient = new ZephyrDiscordClient();
});
