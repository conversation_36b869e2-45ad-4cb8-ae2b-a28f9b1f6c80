const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class ZephyrClient {
    constructor() {
        this.token = '';
        this.channelId = '';
        this.connected = false;
        this.messageQueue = [];
        this.queuePaused = false;
        this.rateLimitInfo = {
            remaining: null,
            reset: null,
            retryAfter: null
        };
        this.currentUser = null;
        this.selectedMessage = null;
        
        // WPM tracking
        this.typingHistory = [];
        this.wpmInterval = null;
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadSavedToken();
        this.startWpmTracking();
        this.updateQueueDisplay(); // Initialize queue display
    }

    initializeElements() {
        // Input elements
        this.tokenInput = document.getElementById('tokenInput');
        this.channelInput = document.getElementById('channelInput');
        this.messageInput = document.getElementById('messageInput');
        
        // Button elements
        this.toggleTokenBtn = document.getElementById('toggleToken');
        this.saveTokenBtn = document.getElementById('saveToken');
        this.connectChannelBtn = document.getElementById('connectChannel');
        this.sendButton = document.getElementById('sendButton');
        this.toggleQueueBtn = document.getElementById('toggleQueue');
        
        // Display elements
        this.connectionStatus = document.getElementById('connectionStatus');
        this.queueCounter = document.getElementById('queueCounter');
        this.queueCount = document.getElementById('queueCount');
        this.wpmCount = document.getElementById('wpmCount');
        this.channelHeader = document.getElementById('channelHeader');
        this.messagesContainer = document.getElementById('messagesContainer');
        this.typingIndicator = document.getElementById('typingIndicator');
        
        // New queue controls
        this.pauseQueueBtn = document.getElementById('pauseQueue');
        this.clearQueueBtn = document.getElementById('clearQueue');
        
        // Modal and context menu
        this.contextMenu = document.getElementById('contextMenu');
        this.queuePanel = document.getElementById('queuePanel');
        this.queueContent = document.getElementById('queueContent');
    }

    setupEventListeners() {
        // Token management
        this.toggleTokenBtn.addEventListener(
            'click', () => this.toggleTokenVisibility()
        );
        this.saveTokenBtn.addEventListener('click', () => this.saveToken());
        
        // Channel connection
        this.connectChannelBtn.addEventListener(
            'click', () => this.connectToChannel()
        );
        
        // Message sending
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // WPM tracking
        this.messageInput.addEventListener('input', () => this.trackTyping());
        
        // Queue controls
        if (this.pauseQueueBtn) {
            this.pauseQueueBtn.addEventListener('click', () => this.toggleQueuePause());
        }
        if (this.clearQueueBtn) {
            this.clearQueueBtn.addEventListener('click', () => this.clearQueue());
        }
        
        // WPM tracking
        this.messageInput.addEventListener('input', () => this.trackTyping());
        
        // Queue controls
        if (this.pauseQueueBtn) {
            this.pauseQueueBtn.addEventListener('click', () => this.toggleQueuePause());
        }
        if (this.clearQueueBtn) {
            this.clearQueueBtn.addEventListener('click', () => this.clearQueue());
        }
        
        // Queue management
        if (this.toggleQueueBtn) {
            this.toggleQueueBtn.addEventListener('click', () => this.toggleQueuePanel());
        }
        
        // Context menu
        document.addEventListener('click', () => this.hideContextMenu());
        document.getElementById('copyMention').addEventListener('click', () => this.copyMention());
        document.getElementById('copyId').addEventListener('click', () => this.copyUserId());
        
        // Python backend messages
        ipcRenderer.on('python-message', (event, data) => {
            this.handlePythonMessage(data);
        });
        
        // Close modal on click outside (only if modal exists)
        this.rateLimitModal = document.getElementById('rateLimitModal');
        if (this.rateLimitModal) {
            this.rateLimitModal.addEventListener('click', (e) => {
                if (e.target === this.rateLimitModal) {
                    this.hideRateLimitModal();
                }
            });
        }
        
        // Initialize other modal elements
        this.rateLimitCountdown = document.getElementById('rateLimitCountdown');
        this.rateLimitProgress = document.getElementById('rateLimitProgress');
        this.rateLimitRemaining = document.getElementById('rateLimitRemaining');
        this.rateLimitReset = document.getElementById('rateLimitReset');
    }

    async loadSavedToken() {
        try {
            const result = await ipcRenderer.invoke('read-token-file');
            if (result.success && result.token) {
                this.tokenInput.value = result.token;
                this.token = result.token;
                this.updateConnectionStatus('Token loaded', 'success');
                this.showNotification('Token loaded from file', 'success');
            }
        } catch (error) {
            console.error('Error loading token:', error);
        }
    }
    
    startWpmTracking() {
        // Initialize WPM display
        if (this.wpmCount) {
            this.wpmCount.textContent = '0';
        }
        
        // Update WPM every second
        this.wpmInterval = setInterval(() => {
            this.updateWpm();
        }, 1000);
    }
    
    trackTyping() {
        const now = Date.now();
        this.typingHistory.push(now);
        
        // Keep only last 15 seconds of typing data
        this.typingHistory = this.typingHistory.filter(time => now - time <= 15000);
    }
    
    updateWpm() {
        const now = Date.now();
        const recentTyping = this.typingHistory.filter(time => now - time <= 15000);
        
        // Calculate WPM based on keystrokes in last 15 seconds
        // Assuming average word length of 5 characters
        const wpm = Math.round((recentTyping.length / 5) * (60 / 15));
        
        if (this.wpmCount) {
            this.wpmCount.textContent = wpm;
        }
    }
    
    toggleQueuePause() {
        this.queuePaused = !this.queuePaused;
        
        if (this.queuePaused) {
            this.pauseQueueBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
            this.pauseQueueBtn.classList.remove('btn-warning');
            this.pauseQueueBtn.classList.add('btn-success');
        } else {
            this.pauseQueueBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
            this.pauseQueueBtn.classList.remove('btn-success');
            this.pauseQueueBtn.classList.add('btn-warning');
        }
        
        this.updateQueueDisplay();
    }
    
    clearQueue() {
        this.messageQueue = [];
        this.updateQueueDisplay();
        this.showNotification('Queue cleared', 'success');
    }
    
    updateQueueDisplay() {
        const queueSize = this.messageQueue.length;
        
        if (this.queueCount) {
            this.queueCount.textContent = queueSize;
        }
        
        if (this.queueCounter) {
            this.queueCounter.textContent = queueSize;
        }
        
        // Update queue panel if it exists
        if (this.queueContent) {
            this.queueContent.innerHTML = '';
            
            this.messageQueue.forEach((msg, index) => {
                const queueItem = document.createElement('div');
                queueItem.className = `queue-item ${msg.status || 'pending'}`;
                queueItem.innerHTML = `
                    <span class="queue-number">${index + 1}</span>
                    <span class="queue-message">${msg.content.substring(0, 50)}${msg.content.length > 50 ? '...' : ''}</span>
                    <span class="queue-status">${msg.status || 'pending'}</span>
                `;
                this.queueContent.appendChild(queueItem);
            });
        }
    }
    
    trackTyping() {
        const now = Date.now();
        this.typingHistory.push(now);
        
        // Keep only last 15 seconds of typing data
        this.typingHistory = this.typingHistory.filter(time => now - time <= 15000);
    }
    
    updateWpm() {
        const now = Date.now();
        const recentTyping = this.typingHistory.filter(time => now - time <= 15000);
        
        // Calculate WPM based on keystrokes in last 15 seconds
        // Assuming average word length of 5 characters
        const wpm = Math.round((recentTyping.length / 5) * (60 / 15));
        
        if (this.wpmCount) {
            this.wpmCount.textContent = wpm;
        }
    }
    
    toggleQueuePause() {
        this.queuePaused = !this.queuePaused;
        
        if (this.queuePaused) {
            this.pauseQueueBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
            this.pauseQueueBtn.classList.remove('btn-warning');
            this.pauseQueueBtn.classList.add('btn-success');
        } else {
            this.pauseQueueBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
            this.pauseQueueBtn.classList.remove('btn-success');
            this.pauseQueueBtn.classList.add('btn-warning');
        }
        
        this.updateQueueDisplay();
    }
    
    clearQueue() {
        this.messageQueue = [];
        this.updateQueueDisplay();
        this.showNotification('Queue cleared', 'success');
    }
    
    updateQueueDisplay() {
        const queueSize = this.messageQueue.length;
        
        if (this.queueCount) {
            this.queueCount.textContent = queueSize;
        }
        
        if (this.queueCounter) {
            this.queueCounter.textContent = queueSize;
        }
        
        // Update queue panel if it exists
        if (this.queueContent) {
            this.queueContent.innerHTML = '';
            
            this.messageQueue.forEach((msg, index) => {
                const queueItem = document.createElement('div');
                queueItem.className = `queue-item ${msg.status || 'pending'}`;
                queueItem.innerHTML = `
                    <span class="queue-number">${index + 1}</span>
                    <span class="queue-message">${msg.content.substring(0, 50)}${msg.content.length > 50 ? '...' : ''}</span>
                    <span class="queue-status">${msg.status || 'pending'}</span>
                `;
                this.queueContent.appendChild(queueItem);
            });
        }
    }

    async saveToken() {
        const token = this.tokenInput.value.trim();
        if (!token) {
            this.showNotification('Please enter a user token', 'error');
            return;
        }

        try {
            const result = await ipcRenderer.invoke('save-token-file', token);
            if (result.success) {
                this.token = token;
                this.showNotification('Token saved successfully', 'success');
            } else {
                this.showNotification('Failed to save token: ' + result.error, 'error');
            }
        } catch (error) {
            this.showNotification('Error saving token: ' + error.message, 'error');
        }
    }

    toggleTokenVisibility() {
        const isPassword = this.tokenInput.type === 'password';
        this.tokenInput.type = isPassword ? 'text' : 'password';
        this.toggleTokenBtn.innerHTML = isPassword ? '<i class="fas fa-eye-slash"></i>' : '<i class="fas fa-eye"></i>';
    }

    async connectToChannel() {
        const channelId = this.channelInput.value.trim();
        const token = this.tokenInput.value.trim();
        
        if (!token) {
            this.showNotification('Please enter a user token', 'error');
            return;
        }
        
        if (!channelId) {
            this.showNotification('Please enter a channel ID', 'error');
            return;
        }

        this.channelId = channelId;
        this.token = token;
        
        this.updateConnectionStatus('Connecting...', 'connecting');
        this.connectChannelBtn.disabled = true;
        
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'connect',
                token: token,
                channel_id: channelId
            });
        } catch (error) {
            this.updateConnectionStatus('Connection failed', 'disconnected');
            this.connectChannelBtn.disabled = false;
            this.showNotification('Connection error: ' + error.message, 'error');
        }
    }

    async sendMessage() {
        const content = this.messageInput.value.trim();
        if (!content) return;
        
        if (!this.connected) {
            this.showNotification('Not connected to Discord. Please connect first.', 'error');
            return;
        }

        const messageId = Date.now().toString();
        const queueItem = {
            id: messageId,
            content: content,
            timestamp: new Date(),
            status: 'pending',
            retries: 0,
            maxRetries: 3
        };

        // Add to queue but don't update display yet
        this.messageQueue.push(queueItem);
        this.messageInput.value = '';
        
        // Only update display if paused (so user can see queued messages)
        if (this.queuePaused) {
            this.updateQueueDisplay();
        }

        // Don't send if paused
        if (this.queuePaused) {
            return;
        }

        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'send_message',
                content: content,
                message_id: messageId
            });
        } catch (error) {
            this.updateQueueItemStatus(messageId, 'failed');
            this.showNotification('Failed to send message: ' + error.message, 'error');
        }
    }

    addToQueue(item) {
        this.messageQueue.push(item);
        this.renderQueueItem(item);
        this.displayQueuedMessage(item);
    }

    updateQueueItemStatus(messageId, status, error = null) {
        const item = this.messageQueue.find(q => q.id === messageId);
        if (item) {
            item.status = status;
            if (error) item.error = error;
            this.renderQueueItem(item);
            this.updateQueuedMessage(messageId, status);
        }
    }

    removeFromQueue(messageId) {
        this.messageQueue = this.messageQueue.filter(q => q.id !== messageId);
        this.updateQueueDisplay();
        this.removeQueuedMessage(messageId);
    }

    displayQueuedMessage(item) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message message-queue-item';
        messageElement.dataset.messageId = item.id;
        
        messageElement.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-user-circle" style="font-size: 40px; color: #7289da;"></i>
            </div>
            <div class="message-content">
                <div class="message-header">
                    <span class="message-author">You</span>
                    <span class="message-timestamp">${this.formatTimestamp(item.timestamp)}</span>
                    <span class="queue-status">Sending...</span>
                </div>
                <div class="message-text">${this.escapeHtml(item.content)}</div>
            </div>
        `;
        
        this.messagesContainer.appendChild(messageElement);
        this.scrollToBottom();
    }

    updateQueuedMessage(messageId, status) {
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (messageElement) {
            const statusElement = messageElement.querySelector('.queue-status');
            const messageDiv = messageElement;
            
            switch (status) {
                case 'sent':
                    messageDiv.className = 'message';
                    if (statusElement) statusElement.remove();
                    break;
                case 'failed':
                    messageDiv.className = 'message message-failed';
                    if (statusElement) statusElement.textContent = 'Failed';
                    break;
                case 'retrying':
                    messageDiv.className = 'message message-sending';
                    if (statusElement) statusElement.textContent = 'Retrying...';
                    break;
            }
        }
    }

    removeQueuedMessage(messageId) {
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (messageElement) {
            messageElement.remove();
        }
    }

    renderQueue() {
        if (this.messageQueue.length === 0) {
            this.queueContent.innerHTML = `
                <div class="queue-empty">
                    <i class="fas fa-inbox"></i>
                    <p>No messages in queue</p>
                </div>
            `;
            return;
        }

        this.queueContent.innerHTML = this.messageQueue.map(item => `
            <div class="queue-item ${item.status}">
                <div class="queue-item-header">
                    <span class="queue-item-status">${item.status.toUpperCase()}</span>
                    <span>${this.formatTimestamp(item.timestamp)}</span>
                </div>
                <div class="queue-item-text">${this.escapeHtml(item.content)}</div>
                ${item.error ? `<div style="color: #f04747; font-size: 0.8rem; margin-top: 5px;">${item.error}</div>` : ''}
            </div>
        `).join('');
    }

    renderQueueItem(item) {
        this.renderQueue();
    }

    updateQueueCounter() {
        const count = this.messageQueue.length;
        this.queueCounter.innerHTML = `<i class="fas fa-clock"></i><span>Queue: ${count}</span>`;
        
        if (count > 0) {
            this.queuePanel.style.display = 'block';
        }
    }

    toggleQueuePanel() {
        const isVisible = this.queueContent.style.display !== 'none';
        this.queueContent.style.display = isVisible ? 'none' : 'block';
        this.toggleQueueBtn.innerHTML = isVisible ? 
            '<i class="fas fa-chevron-up"></i>' : 
            '<i class="fas fa-chevron-down"></i>';
    }

    handlePythonMessage(data) {
        console.log('Python message:', data);
        switch (data.type) {
            case 'connection_success':
                this.handleConnectionSuccess(data);
                break;
            case 'connection_error':
                this.handleConnectionError(data);
                break;
            case 'message_received':
                this.handleMessageReceived(data);
                break;
            case 'message_sent':
                this.handleMessageSent(data);
                break;
            case 'message_failed':
                this.handleMessageFailed(data);
                break;
            case 'message_updated':
                this.handleMessageUpdated(data);
                break;
            case 'message_deleted':
                this.handleMessageDeleted(data);
                break;
            case 'reaction_added':
                this.handleReactionAdded(data);
                break;
            case 'reaction_removed':
                this.handleReactionRemoved(data);
                break;
            case 'thread_created':
                this.handleThreadCreated(data);
                break;
            case 'thread_updated':
                this.handleThreadUpdated(data);
                break;
            case 'rate_limit':
                this.handleRateLimit(data);
                break;
            case 'typing_start':
                this.handleTypingStart(data);
                break;
            case 'typing_stop':
                this.handleTypingStop(data);
                break;
            case 'rate_limit_info':
                this.updateRateLimitInfo(data);
                break;
        }
    }

    handleConnectionSuccess(data) {
        this.connected = true;
        this.currentUser = data.user;
        this.updateConnectionStatus('Connected', 'connected');
        this.connectChannelBtn.disabled = false;
        this.messageInput.disabled = false;
        this.sendButton.disabled = false;
        this.messageInput.placeholder = 'Type a message...';
        
        this.channelHeader.innerHTML = `
            <i class="fas fa-hashtag"></i>
            <span>${data.channel_name || 'Channel'}</span>
        `;
        
        this.clearWelcomeMessage();
        this.showNotification('Connected successfully!', 'success');
        
        // Add a test message to verify display works
        setTimeout(() => {
            this.displayMessage({
                id: 'test-message-' + Date.now(),
                content: 'Test message to verify display functionality',
                author: {
                    id: 'test-user',
                    username: 'TestUser',
                    display_name: 'Test User',
                    avatar: 'https://cdn.discordapp.com/embed/avatars/0.png',
                    color: '#7289da',
                    bot: false
                },
                timestamp: new Date().toISOString(),
                message_type: 'normal',
                attachments: [],
                embeds: [],
                poll: null,
                thread: null,
                reactions: [],
                edited_timestamp: null
            });
        }, 1000);
    }

    handleConnectionError(data) {
        this.connected = false;
        this.updateConnectionStatus('Connection failed', 'disconnected');
        this.connectChannelBtn.disabled = false;
        this.messageInput.disabled = false;
        this.sendButton.disabled = false;
        this.messageInput.placeholder = 'Connect to Discord to send messages...';
        this.showNotification('Connection failed: ' + data.error, 'error');
    }

    handleMessageReceived(data) {
        console.log('=== FRONTEND MESSAGE RECEIVED ===');
        console.log('Full message data received in frontend:', JSON.stringify(data, null, 2));
        console.log('Message ID:', data.id);
        console.log('Message content:', data.content);
        console.log('Message author:', data.author);
        console.log('Content type:', typeof data.content);
        console.log('Content length:', data.content ? data.content.length : 'N/A');
        console.log('=== END FRONTEND MESSAGE RECEIVED ===');
        this.displayMessage(data);
    }

    handleMessageSent(data) {
        console.log('Message sent:', data);
        this.updateQueueItemStatus(data.message_id, 'sent');
        this.removeFromQueue(data.message_id);
    }

    handleMessageFailed(data) {
        const item = this.messageQueue.find(q => q.id === data.message_id);
        if (item) {
            item.retries++;
            if (item.retries < item.maxRetries) {
                this.updateQueueItemStatus(data.message_id, 'retrying');
                // Retry after delay
                setTimeout(() => {
                    this.retryMessage(item);
                }, data.retry_after || 5000);
            } else {
                this.updateQueueItemStatus(data.message_id, 'failed', data.error);
            }
        }
    }

    async retryMessage(item) {
        try {
            await ipcRenderer.invoke('send-to-python', {
                action: 'send_message',
                content: item.content,
                message_id: item.id
            });
        } catch (error) {
            this.updateQueueItemStatus(item.id, 'failed', error.message);
        }
    }

    handleRateLimit(data) {
        // Rate limit handling removed - no popup or UI updates
        console.log('Rate limited for', data.retry_after, 'ms');
    }

    handleTypingStart(data) {
        this.typingIndicator.textContent = `${data.user} is typing...`;
    }

    handleTypingStop(data) {
        this.typingIndicator.textContent = '';
    }

    displayMessage(data) {
        console.log('=== PROCESSING MESSAGE ===');
        console.log('Raw message data:', JSON.stringify(data, null, 2));
        
        // Validate required data
        if (!data || !data.author) {
            console.error('Invalid message data - missing author:', data);
            return;
        }
        
        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = 'message';
        
        // Set data attributes safely
        try {
            messageElement.dataset.userId = String(data.author.id || 'unknown');
            messageElement.dataset.messageId = String(data.id || Date.now());
        } catch (e) {
            console.error('Error setting data attributes:', e);
        }
        
        // Add context menu
        messageElement.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showContextMenu(e, data.author);
        });
        
        // Process author information safely
        const author = this.processAuthorData(data.author);
        console.log('Processed author:', author);
        
        // Process message content
        const content = this.processMessageContent(data);
        console.log('Processed content:', content);
        
        // Process timestamp
        const timestamp = this.processTimestamp(data.timestamp);
        console.log('Processed timestamp:', timestamp);
        
        // Build message HTML using a more robust approach
        const messageHTML = this.buildMessageHTML(author, content, timestamp, data);
        
        messageElement.innerHTML = messageHTML;
        
        console.log('Final message HTML:', messageHTML);
        console.log('Appending to container...');
        
        // Append and scroll
        this.messagesContainer.appendChild(messageElement);
        this.scrollToBottom();
        
        console.log('Message successfully added. Total messages:', this.messagesContainer.children.length);
        console.log('=== END MESSAGE PROCESSING ===\n');
    }
    
    processAuthorData(authorData) {
        if (!authorData) {
            return {
                id: 'unknown',
                username: 'Unknown User',
                displayName: 'Unknown User',
                avatar: 'https://cdn.discordapp.com/embed/avatars/0.png',
                color: '#ffffff',
                isBot: false
            };
        }
        
        return {
            id: String(authorData.id || 'unknown'),
            username: String(authorData.username || 'Unknown'),
            displayName: String(authorData.display_name || authorData.username || 'Unknown User'),
            avatar: String(authorData.avatar || 'https://cdn.discordapp.com/embed/avatars/0.png'),
            color: String(authorData.color || '#ffffff'),
            isBot: Boolean(authorData.bot)
        };
    }
    
    processMessageContent(data) {
        const result = {
            text: '',
            hasText: false,
            attachments: [],
            embeds: [],
            poll: null,
            thread: null,
            reactions: [],
            messageType: 'normal'
        };
        
        // Process text content with more robust checking
        console.log('Processing content for message:', data.id, 'Content value:', data.content, 'Type:', typeof data.content);
        
        if (data.content !== undefined && data.content !== null) {
            const textContent = String(data.content).trim();
            console.log('Processed text content:', textContent, 'Length:', textContent.length);
            if (textContent.length > 0) {
                result.text = textContent;
                result.hasText = true;
                console.log('✓ Found text content for message:', data.id, ':', textContent);
            } else {
                console.log('⚠ Text content is empty or whitespace only for message:', data.id);
            }
        } else {
            console.log('⚠ No content property found in message data for message:', data.id);
        }
        
        // Process attachments
        if (Array.isArray(data.attachments) && data.attachments.length > 0) {
            result.attachments = data.attachments;
            result.messageType = this.determineAttachmentType(data.attachments);
            console.log('Found attachments:', data.attachments.length);
        }
        
        // Process embeds
        if (Array.isArray(data.embeds) && data.embeds.length > 0) {
            result.embeds = data.embeds;
            if (result.messageType === 'normal') result.messageType = 'embed';
            console.log('Found embeds:', data.embeds.length);
        }
        
        // Process poll
        if (data.poll && typeof data.poll === 'object') {
            result.poll = data.poll;
            result.messageType = 'poll';
            console.log('Found poll');
        }
        
        // Process thread
        if (data.thread && typeof data.thread === 'object') {
            result.thread = data.thread;
            result.messageType = 'thread';
            console.log('Found thread');
        }
        
        // Process reactions
        if (Array.isArray(data.reactions) && data.reactions.length > 0) {
            result.reactions = data.reactions;
            console.log('Found reactions:', data.reactions.length);
        }
        
        return result;
    }
    
    determineAttachmentType(attachments) {
        if (!Array.isArray(attachments) || attachments.length === 0) return 'normal';
        
        const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        const hasImages = attachments.some(att => 
            att.content_type && imageTypes.includes(att.content_type.toLowerCase())
        );
        
        return hasImages ? 'image' : 'file';
    }
    
    processTimestamp(timestamp) {
        try {
            if (!timestamp) return new Date();
            
            // Handle different timestamp formats
            if (typeof timestamp === 'string') {
                return new Date(timestamp);
            } else if (typeof timestamp === 'number') {
                // Handle both milliseconds and seconds
                return new Date(timestamp > 1000000000000 ? timestamp : timestamp * 1000);
            } else if (timestamp instanceof Date) {
                return timestamp;
            }
            
            return new Date();
        } catch (e) {
            console.error('Error processing timestamp:', e);
            return new Date();
        }
    }
    
    buildMessageHTML(author, content, timestamp, rawData) {
        // Build avatar HTML
        const avatarHTML = `
            <img class="message-avatar" 
                 src="${this.escapeHtml(author.avatar)}" 
                 alt="${this.escapeHtml(author.username)}" 
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
            <div class="message-avatar fallback" 
                 style="display: none; align-items: center; justify-content: center; background: #7289da; color: white; font-weight: bold; width: 40px; height: 40px; border-radius: 50%; font-size: 16px;">
                ${this.escapeHtml(author.username.charAt(0).toUpperCase())}
            </div>
        `;
        
        // Build header HTML
        const botIndicator = author.isBot ? '<span class="bot-indicator" style="background: #5865f2; color: white; padding: 2px 4px; border-radius: 3px; font-size: 10px; margin-left: 5px;">BOT</span>' : '';
        const editedIndicator = rawData.edited_timestamp ? '<span class="edited-indicator" style="color: #72767d; font-size: 12px; margin-left: 4px;">(edited)</span>' : '';
        const typeIndicator = this.buildTypeIndicator(content.messageType);
        
        const headerHTML = `
            <div class="message-header">
                <span class="message-author" style="color: ${this.escapeHtml(author.color)}; font-weight: 600;">
                    ${this.escapeHtml(author.displayName)}
                </span>
                ${botIndicator}
                <span class="message-timestamp" style="color: #72767d; font-size: 12px; margin-left: 8px;">
                    ${this.formatTimestamp(timestamp)}
                </span>
                ${editedIndicator}
                ${typeIndicator}
            </div>
        `;
        
        // Build content HTML
        const contentHTML = this.buildContentHTML(content);
        
        // Combine everything
        return `
            ${avatarHTML}
            <div class="message-content">
                ${headerHTML}
                ${contentHTML}
            </div>
        `;
    }
    
    buildTypeIndicator(messageType) {
        const indicators = {
            thread: '<span class="type-indicator thread" style="background: rgba(88, 101, 242, 0.2); color: #5865f2; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;"><i class="fas fa-comments"></i> Thread</span>',
            poll: '<span class="type-indicator poll" style="background: rgba(67, 181, 129, 0.2); color: #43b581; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;"><i class="fas fa-poll"></i> Poll</span>',
            image: '<span class="type-indicator image" style="background: rgba(255, 165, 0, 0.2); color: #faa61a; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;"><i class="fas fa-image"></i> Image</span>',
            file: '<span class="type-indicator file" style="background: rgba(114, 137, 218, 0.2); color: #7289da; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;"><i class="fas fa-file"></i> File</span>',
            embed: '<span class="type-indicator embed" style="background: rgba(114, 137, 218, 0.2); color: #7289da; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;"><i class="fas fa-link"></i> Embed</span>'
        };
        
        return indicators[messageType] || '';
    }
    
    buildContentHTML(content) {
        let html = '';
        
        // Add text content section only if there's actual text
        if (content.hasText) {
            html += `<div class="message-text" style="color: #dcddde; line-height: 1.4; word-wrap: break-word; margin: 4px 0;">
                ${this.formatMessageContent(content.text)}
            </div>`;
        } else if (!content.attachments.length && !content.embeds.length && !content.poll && !content.thread) {
            // Only show placeholder if there's truly no content at all
            html += `<div class="message-text" style="color: #72767d; font-style: italic; margin: 4px 0;">
                <em>This message has no content</em>
            </div>`;
        }
        
        // Add attachments
        if (content.attachments.length > 0) {
            html += this.renderAttachments(content.attachments);
        }
        
        // Add embeds
        if (content.embeds.length > 0) {
            html += this.renderEmbeds(content.embeds);
        }
        
        // Add poll
        if (content.poll) {
            html += this.renderPoll(content.poll);
        }
        
        // Add thread info
        if (content.thread) {
            html += this.renderThreadInfo(content.thread);
        }
        
        // Add reactions
        if (content.reactions.length > 0) {
            html += this.renderReactions(content.reactions);
        }
        
        return html;
    }
    
    renderAttachments(attachments) {
        let html = '<div class="message-attachments">';
        
        for (const attachment of attachments) {
            const isImage = attachment.content_type && attachment.content_type.startsWith('image/');
            
            if (isImage) {
                html += `
                    <div class="attachment-image">
                        <img src="${attachment.url}" alt="${attachment.filename}" 
                             style="max-width: 400px; max-height: 300px; border-radius: 4px; cursor: pointer;"
                             onclick="window.open('${attachment.url}', '_blank')">
                        <div class="attachment-info">
                            <span class="attachment-name">${attachment.filename}</span>
                            <span class="attachment-size">${this.formatFileSize(attachment.size)}</span>
                        </div>
                    </div>
                `;
            } else {
                html += `
                    <div class="attachment-file">
                        <i class="fas fa-file"></i>
                        <div class="attachment-info">
                            <a href="${attachment.url}" target="_blank" class="attachment-name">${attachment.filename}</a>
                            <span class="attachment-size">${this.formatFileSize(attachment.size)}</span>
                        </div>
                    </div>
                `;
            }
        }
        
        html += '</div>';
        return html;
    }
    
    renderEmbeds(embeds) {
        let html = '<div class="message-embeds">';
        
        for (const embed of embeds) {
            const borderColor = embed.color ? '#' + embed.color.toString(16).padStart(6, '0') : '#7289da';
            html += `
                <div class="embed" style="border-left: 4px solid ${borderColor}; padding: 8px; margin: 4px 0; background: rgba(46, 48, 54, 0.3);">
                    ${embed.author ? `<div class="embed-author" style="font-size: 12px; font-weight: bold;">${embed.author.name}</div>` : ''}
                    ${embed.title ? `<div class="embed-title" style="font-weight: bold; margin: 4px 0;">${embed.title}</div>` : ''}
                    ${embed.description ? `<div class="embed-description" style="margin: 4px 0;">${embed.description}</div>` : ''}
                    ${embed.image ? `<img class="embed-image" src="${embed.image.url}" alt="Embed image" style="max-width: 400px; border-radius: 4px;">` : ''}
                    ${embed.thumbnail ? `<img class="embed-thumbnail" src="${embed.thumbnail.url}" alt="Embed thumbnail" style="max-width: 80px; float: right;">` : ''}
                    ${embed.fields && embed.fields.length > 0 ? this.renderEmbedFields(embed.fields) : ''}
                </div>
            `;
        }
        
        html += '</div>';
        return html;
    }
    
    renderEmbedFields(fields) {
        let html = '<div class="embed-fields" style="display: grid; gap: 8px;">';
        
        for (const field of fields) {
            html += `
                <div class="embed-field ${field.inline ? 'inline' : ''}" style="${field.inline ? 'display: inline-block; margin-right: 16px;' : ''}">
                    <div class="embed-field-name" style="font-weight: bold; font-size: 12px;">${field.name}</div>
                    <div class="embed-field-value" style="font-size: 14px;">${field.value}</div>
                </div>
            `;
        }
        
        html += '</div>';
        return html;
    }
    
    renderPoll(poll) {
        let html = `
            <div class="message-poll" style="background: rgba(46, 48, 54, 0.3); padding: 12px; border-radius: 4px; margin: 8px 0;">
                <div class="poll-question" style="font-weight: bold; margin-bottom: 8px;"><i class="fas fa-poll"></i> ${poll.question}</div>
                <div class="poll-answers">
        `;
        
        for (const answer of poll.answers) {
            html += `
                <div class="poll-answer" style="display: flex; justify-content: space-between; padding: 6px; margin: 4px 0; background: rgba(64, 68, 75, 0.6); border-radius: 3px;">
                    <span class="poll-answer-text">${answer.text}</span>
                    <span class="poll-answer-votes" style="color: #b9bbbe;">${answer.votes} votes</span>
                </div>
            `;
        }
        
        html += `
                </div>
                ${poll.expires_at ? `<div class="poll-expires" style="font-size: 12px; color: #72767d; margin-top: 8px;">Expires: ${new Date(poll.expires_at).toLocaleString()}</div>` : ''}
            </div>
        `;
        
        return html;
    }
    
    renderThreadInfo(thread) {
        return `
            <div class="message-thread-info" style="background: rgba(88, 101, 242, 0.1); padding: 8px; border-radius: 4px; margin: 4px 0;">
                <i class="fas fa-comments" style="color: #5865f2;"></i>
                <span class="thread-name" style="font-weight: bold; margin: 0 8px;">${thread.name}</span>
                <span class="thread-stats" style="color: #b9bbbe; font-size: 12px;">${thread.message_count} messages, ${thread.member_count} members</span>
            </div>
        `;
    }
    
    renderReactions(reactions) {
        let html = '<div class="message-reactions" style="margin-top: 4px;">';
        
        for (const reaction of reactions) {
            const emoji = reaction.emoji.id ? 
                `<img src="https://cdn.discordapp.com/emojis/${reaction.emoji.id}.${reaction.emoji.animated ? 'gif' : 'png'}" alt="${reaction.emoji.name}" class="custom-emoji" style="width: 16px; height: 16px;">` :
                reaction.emoji.name;
            
            html += `
                <div class="reaction ${reaction.me ? 'me' : ''}" style="display: inline-flex; align-items: center; background: rgba(64, 68, 75, 0.6); border-radius: 3px; padding: 2px 6px; margin: 2px; font-size: 12px; ${reaction.me ? 'background: rgba(88, 101, 242, 0.3);' : ''}">
                    ${emoji}
                    <span class="reaction-count" style="margin-left: 4px;">${reaction.count}</span>
                </div>
            `;
        }
        
        html += '</div>';
        return html;
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatMessageContent(content) {
        // Basic formatting for mentions, links, etc.
        return this.escapeHtml(content)
            .replace(/<@!?(\d+)>/g, '<span class="mention">@user</span>')
            .replace(/<#(\d+)>/g, '<span class="channel-mention">#channel</span>')
            .replace(/https?:\/\/[^\s]+/g, '<a href="$&" target="_blank">$&</a>');
    }

    showContextMenu(event, author) {
        this.selectedMessage = author;
        this.contextMenu.style.display = 'block';
        this.contextMenu.style.left = event.pageX + 'px';
        this.contextMenu.style.top = event.pageY + 'px';
    }

    hideContextMenu() {
        this.contextMenu.style.display = 'none';
        this.selectedMessage = null;
    }

    copyMention() {
        if (this.selectedMessage) {
            const mention = `<@${this.selectedMessage.id}>`;
            navigator.clipboard.writeText(mention);
            this.showNotification('Mention copied to clipboard', 'success');
        }
        this.hideContextMenu();
    }

    copyUserId() {
        if (this.selectedMessage) {
            navigator.clipboard.writeText(this.selectedMessage.id);
            this.showNotification('User ID copied to clipboard', 'success');
        }
        this.hideContextMenu();
    }

    showRateLimitModal(retryAfter) {
        this.rateLimitModal.style.display = 'flex';
        let timeLeft = Math.ceil(retryAfter / 1000);
        
        const updateCountdown = () => {
            this.rateLimitCountdown.textContent = timeLeft;
            const progress = ((retryAfter / 1000 - timeLeft) / (retryAfter / 1000)) * 100;
            this.rateLimitProgress.style.width = progress + '%';
            
            if (timeLeft <= 0) {
                this.hideRateLimitModal();
                return;
            }
            
            timeLeft--;
            setTimeout(updateCountdown, 1000);
        };
        
        updateCountdown();
    }

    hideRateLimitModal() {
        this.rateLimitModal.style.display = 'none';
    }

    updateRateLimitInfo(data) {
        this.rateLimitInfo = data;
        this.rateLimitRemaining.textContent = data.remaining || 'Unknown';
        this.rateLimitReset.textContent = data.reset ? 
            new Date(data.reset * 1000).toLocaleTimeString() : 'Unknown';
    }

    updateConnectionStatus(text, status) {
        this.connectionStatus.className = `connection-status ${status}`;
        this.connectionStatus.innerHTML = `<i class="fas fa-circle"></i><span>${text}</span>`;
    }

    clearWelcomeMessage() {
        const welcomeMessage = this.messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    formatTimestamp(date) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showNotification(message, type = 'info') {
        // Simple notification system - you can enhance this
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // Create a simple toast notification
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: ${type === 'success' ? '#43b581' : type === 'error' ? '#f04747' : '#7289da'};
            color: white;
            border-radius: 6px;
            z-index: 3000;
            animation: slideIn 0.3s ease-out;
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.animation = 'slideOut 0.3s ease-out';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
    
    handleMessageUpdated(data) {
        console.log('Message updated:', data);
        const messageElement = this.messagesContainer.querySelector(`[data-message-id="${data.id}"]`);
        if (messageElement) {
            const contentElement = messageElement.querySelector('.message-text');
            if (contentElement) {
                contentElement.innerHTML = this.formatMessageContent(data.content);
            }
            
            // Add edited indicator if not already present
            const header = messageElement.querySelector('.message-header');
            if (header && !header.querySelector('.edited-indicator')) {
                const editedSpan = document.createElement('span');
                editedSpan.className = 'edited-indicator';
                editedSpan.textContent = '(edited)';
                editedSpan.style.cssText = 'color: #72767d; font-size: 12px; margin-left: 4px;';
                header.appendChild(editedSpan);
            }
        }
    }
    
    handleMessageDeleted(data) {
        console.log('Message deleted:', data);
        const messageElement = this.messagesContainer.querySelector(`[data-message-id="${data.id}"]`);
        if (messageElement) {
            messageElement.style.opacity = '0.5';
            const contentElement = messageElement.querySelector('.message-text');
            if (contentElement) {
                contentElement.innerHTML = '<em style="color: #72767d;">This message was deleted</em>';
            }
        }
    }
    
    handleReactionAdded(data) {
        console.log('Reaction added:', data);
        // Find the message and update reactions
        const messageElement = this.messagesContainer.querySelector(`[data-message-id="${data.message_id}"]`);
        if (messageElement) {
            // This would require more complex logic to update individual reactions
            // For now, just log it
            this.showNotification('Reaction added to message', 'info');
        }
    }
    
    handleReactionRemoved(data) {
        console.log('Reaction removed:', data);
        // Similar to reaction added
        this.showNotification('Reaction removed from message', 'info');
    }
    
    handleThreadCreated(data) {
        console.log('Thread created:', data);
        this.showNotification(`New thread created: ${data.thread.name}`, 'info');
    }
    
    handleThreadUpdated(data) {
        console.log('Thread updated:', data);
        this.showNotification(`Thread updated: ${data.thread.name}`, 'info');
    }
}

// Add CSS for toast animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize the client when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.zephyrClient = new ZephyrClient();
});