const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const fs = require('fs').promises;
const path = require('path');

class ZephyrDiscordClient {
    constructor() {
        // Core properties
        this.token = '';
        this.channelId = '';
        this.connected = false;
        this.currentUser = null;
        this.channelInfo = null;
        
        // Message handling - NEW: Proper ordered queue system
        this.messageQueue = [];
        this.queuePaused = false;
        this.isProcessingQueue = false;
        this.messages = [];
        this.sentMessageIds = new Set(); // Track sent messages to avoid duplicates
        
        // Features
        this.spacingEnabled = false;
        this.messageDelay = 0.1;
        this.prefixSuffixEnabled = false;
        this.messagePrefix = '';
        this.messageSuffix = '';
        
        // NEW: Enhanced Autobeef system
        this.autobeefEnabled = false;
        this.autobeefDelay = 5;
        this.autobeefInterval = null;
        this.autobeefStats = {
            messagesSent: 0,
            sessionStartTime: null
        };
        this.usedMessages = new Set(); // Track used messages to prevent repeats
        
        // NEW: Advanced Wordlist system
        this.wordsPhrases = []; // Main wordlist
        this.typoPercent = 0;
        this.genderOption = 'male';
        this.blockRules = []; // New block rules system
        this.sentenceHistory = []; // Track sentence usage to prevent word repetition
        this.wordlistPath = path.join(process.cwd(), 'wordlist.txt');
        
        // UI state
        this.activeTab = 'manual';
        this.panelOpen = false;
        
        // WPM tracking
        this.typingHistory = [];
        this.wpmInterval = null;
        
        // Keybinds
        this.keybinds = {
            pauseQueue: null,
            spacing: null,
            autobeef: 'escape'
        };
        
        this.initializeElements();
        this.setupEventListeners();
        this.startWpmTracking();
        this.loadSavedSettings();
        this.loadWordlistFromFile();
    }
    
    initializeElements() {
        // Connection elements
        this.tokenInput = document.getElementById('tokenInput');
        this.channelInput = document.getElementById('channelInput');
        this.toggleTokenBtn = document.getElementById('toggleToken');
        this.saveTokenBtn = document.getElementById('saveToken');
        this.connectBtn = document.getElementById('connectChannel');
        this.connectionStatus = document.getElementById('connectionStatus');
        
        // Chat elements
        this.messagesContainer = document.getElementById('messagesContainer');
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.channelName = document.getElementById('channelName');
        this.typingIndicator = document.getElementById('typingIndicator');
        
        // Feature elements
        this.featureTabs = document.querySelectorAll('.feature-tab');
        this.featurePanel = document.getElementById('featurePanel');
        this.tabContents = document.querySelectorAll('.tab-content');
        this.closePanelBtns = document.querySelectorAll('.close-panel');
        
        // Manual controls
        this.spacingCheckbox = document.getElementById('spacingEnabled');
        this.delaySlider = document.getElementById('messageDelay');
        this.delayDisplay = document.getElementById('delayDisplay');
        this.prefixSuffixCheckbox = document.getElementById('prefixSuffixEnabled');
        this.prefixInput = document.getElementById('messagePrefix');
        this.suffixInput = document.getElementById('messageSuffix');
        this.wpmCounter = document.getElementById('wpmCount');
        
        // Autobeef elements
        this.autobeefToggle = document.getElementById('autobeefToggle');
        this.autobeefStatusDot = document.getElementById('autobeefStatusDot');
        this.autobeefStatusText = document.getElementById('autobeefStatusText');
        this.autobeefDelaySlider = document.getElementById('autobeefDelay');
        this.autobeefDelayDisplay = document.getElementById('autobeefDelayDisplay');
        this.messagesSentDisplay = document.getElementById('messagesSent');
        this.sessionTimeDisplay = document.getElementById('sessionTime');
        this.nextMessageTimeDisplay = document.getElementById('nextMessageTime');
        
        // Queue elements
        this.queueCountDisplay = document.getElementById('queueCount');
        this.pauseQueueBtn = document.getElementById('pauseQueue');
        this.clearQueueBtn = document.getElementById('clearQueue');
        this.queueList = document.getElementById('queueList');
        
        // NEW: Enhanced Wordlist elements
        this.newWordPhraseInput = document.getElementById('newWordPhrase');
        this.addWordPhraseBtn = document.getElementById('addWordPhrase');
        this.typoSlider = document.getElementById('typoPercent');
        this.typoDisplay = document.getElementById('typoDisplay');
        this.wordsList = document.getElementById('wordsList');
        this.wordCount = document.getElementById('wordCount');
        this.genderRadios = document.querySelectorAll('input[name="genderOption"]');
        this.blockRuleInput = document.getElementById('blockRuleInput');
        this.addBlockRuleBtn = document.getElementById('addBlockRule');
        this.blockRulesList = document.getElementById('blockRulesList');
        this.loadFromFileBtn = document.getElementById('loadFromFile');
        this.saveToFileBtn = document.getElementById('saveToFile');
        this.clearAllWordsBtn = document.getElementById('clearAllWords');
        this.generatePreviewBtn = document.getElementById('generatePreview');
        this.previewSentence = document.getElementById('previewSentence');
        this.previewStats = document.getElementById('previewStats');
        
        // Header controls
        this.settingsBtn = document.getElementById('settingsBtn');
        this.minimizeBtn = document.getElementById('minimizeBtn');
        this.closeBtn = document.getElementById('closeBtn');
        
        // Keybind elements
        this.keybindInputs = {
            pauseQueue: document.getElementById('pauseKeybind'),
            spacing: document.getElementById('spacingKeybind'),
            autobeef: document.getElementById('autobeefKeybind')
        };
        this.keybindSetBtns = {
            pauseQueue: document.getElementById('setPauseKeybind'),
            spacing: document.getElementById('setSpacingKeybind'),
            autobeef: document.getElementById('setAutobeefKeybind')
        };
    }
    
    setupEventListeners() {
        // Connection events
        this.toggleTokenBtn?.addEventListener('click', () => this.toggleTokenVisibility());
        this.saveTokenBtn?.addEventListener('click', () => this.saveToken());
        this.connectBtn?.addEventListener('click', () => this.connectToChannel());
        
        // Message input events
        this.messageInput?.addEventListener('keydown', (e) => this.handleMessageInput(e));
        this.messageInput?.addEventListener('input', () => this.handleTyping());
        this.sendButton?.addEventListener('click', () => this.sendMessage());
        
        // Feature tab events
        this.featureTabs.forEach(tab => {
            tab.addEventListener('click', () => this.switchTab(tab.dataset.tab));
        });
        
        this.closePanelBtns.forEach(btn => {
            btn.addEventListener('click', () => this.closePanel());
        });
        
        // Manual control events - FIXED
        this.spacingCheckbox?.addEventListener('change', (e) => {
            this.spacingEnabled = e.target.checked;
            this.saveSettings();
        });
        
        this.delaySlider?.addEventListener('input', (e) => {
            this.messageDelay = parseFloat(e.target.value);
            if (this.delayDisplay) this.delayDisplay.textContent = `${this.messageDelay}s`;
            this.saveSettings();
        });
        
        this.prefixSuffixCheckbox?.addEventListener('change', (e) => {
            this.prefixSuffixEnabled = e.target.checked;
            this.saveSettings();
        });
        
        this.prefixInput?.addEventListener('input', (e) => {
            this.messagePrefix = e.target.value;
            this.saveSettings();
        });
        
        this.suffixInput?.addEventListener('input', (e) => {
            this.messageSuffix = e.target.value;
            this.saveSettings();
        });
        
        // Autobeef events
        this.autobeefToggle?.addEventListener('click', () => this.toggleAutobeef());
        this.autobeefDelaySlider?.addEventListener('input', (e) => {
            this.autobeefDelay = parseInt(e.target.value);
            if (this.autobeefDelayDisplay) this.autobeefDelayDisplay.textContent = `${this.autobeefDelay}s`;
            this.saveSettings();
        });
        
        // Queue events
        this.pauseQueueBtn?.addEventListener('click', () => this.toggleQueuePause());
        this.clearQueueBtn?.addEventListener('click', () => this.clearQueue());
        
        // NEW: Enhanced Wordlist events
        this.addWordPhraseBtn?.addEventListener('click', () => this.addWordPhrase());
        this.newWordPhraseInput?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                this.addWordPhrase();
            }
        });
        
        this.typoSlider?.addEventListener('input', (e) => {
            this.typoPercent = parseInt(e.target.value);
            if (this.typoDisplay) this.typoDisplay.textContent = `${this.typoPercent}%`;
            this.saveSettings();
        });
        
        this.genderRadios?.forEach(radio => {
            radio.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.genderOption = e.target.value;
                    this.saveSettings();
                }
            });
        });
        
        this.addBlockRuleBtn?.addEventListener('click', () => this.addBlockRule());
        this.blockRuleInput?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.addBlockRule();
            }
        });
        
        this.loadFromFileBtn?.addEventListener('click', () => this.loadWordlistFromFile());
        this.saveToFileBtn?.addEventListener('click', () => this.saveWordlistToFile());
        this.clearAllWordsBtn?.addEventListener('click', () => this.clearAllWords());
        this.generatePreviewBtn?.addEventListener('click', () => this.generatePreview());
        
        // Header controls
        this.minimizeBtn?.addEventListener('click', () => {
            ipcRenderer.invoke('minimize-window');
        });
        
        this.closeBtn?.addEventListener('click', () => {
            ipcRenderer.invoke('close-window');
        });
        
        // Keybind events
        Object.keys(this.keybindSetBtns).forEach(key => {
            this.keybindSetBtns[key]?.addEventListener('click', () => this.setKeybind(key));
        });
        
        // Global keyboard events
        document.addEventListener('keydown', (e) => this.handleGlobalKeydown(e));
        
        // IPC events
        ipcRenderer.on('message-received', (event, message) => {
            this.handleIncomingMessage(message);
        });
        
        ipcRenderer.on('connection-status', (event, status) => {
            this.updateConnectionStatus(status);
        });
        
        ipcRenderer.on('rate-limit', (event, info) => {
            this.handleRateLimit(info);
        });
    }
    
    toggleTokenVisibility() {
        const isPassword = this.tokenInput.type === 'password';
        this.tokenInput.type = isPassword ? 'text' : 'password';
        this.toggleTokenBtn.innerHTML = `<i class="fas fa-eye${isPassword ? '-slash' : ''}"></i>`;
    }
    
    saveToken() {
        const token = this.tokenInput.value.trim();
        if (!token) {
            this.showNotification('Please enter a token', 'error');
            return;
        }
        
        this.token = token;
        localStorage.setItem('discord_token', token);
        this.showNotification('Token saved successfully', 'success');
    }
    
    async connectToChannel() {
        const channelId = this.channelInput.value.trim();
        if (!channelId) {
            this.showNotification('Please enter a channel ID', 'error');
            return;
        }
        
        if (!this.token) {
            this.showNotification('Please save a token first', 'error');
            return;
        }
        
        try {
            this.connectBtn.disabled = true;
            this.connectBtn.textContent = 'Connecting...';
            
            const result = await ipcRenderer.invoke('connect-to-channel', {
                token: this.token,
                channelId: channelId
            });
            
            if (result.success) {
                this.connected = true;
                this.channelId = channelId;
                this.currentUser = result.user;
                this.channelInfo = result.channel;
                
                this.channelName.textContent = result.channel.name || 'Unknown Channel';
                this.updateConnectionStatus(true);
                this.clearWelcomeMessage();
                this.showNotification('Connected successfully!', 'success');
                
                localStorage.setItem('channel_id', channelId);
            } else {
                this.showNotification(result.error || 'Failed to connect', 'error');
            }
        } catch (error) {
            this.showNotification('Connection failed: ' + error.message, 'error');
        } finally {
            this.connectBtn.disabled = false;
            this.connectBtn.textContent = 'Connect';
        }
    }
    
    updateConnectionStatus(connected) {
        this.connected = connected;
        const statusElement = this.connectionStatus;
        const statusIcon = statusElement.querySelector('i');
        const statusText = statusElement.querySelector('span');
        
        if (connected) {
            statusElement.classList.remove('disconnected');
            statusElement.classList.add('connected');
            statusIcon.className = 'fas fa-circle';
            statusText.textContent = 'Connected';
        } else {
            statusElement.classList.remove('connected');
            statusElement.classList.add('disconnected');
            statusIcon.className = 'fas fa-circle';
            statusText.textContent = 'Disconnected';
        }
    }
    
    clearWelcomeMessage() {
        const welcomeMessage = this.messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
    }
    
    handleMessageInput(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.sendMessage();
        }
        
        // NEW: Stop autobeef when typing in queue (unless paused)
        if (this.autobeefEnabled && this.messageInput.value.trim() && !this.queuePaused) {
            this.pauseAutobeefForQueue();
        }
    }
    
    handleTyping() {
        const text = this.messageInput.value;
        this.updateWpmCounter(text);
        
        // Auto-resize textarea with proper word wrapping
        this.messageInput.style.height = 'auto';
        const newHeight = Math.min(this.messageInput.scrollHeight, 144);
        this.messageInput.style.height = newHeight + 'px';
    }
    
    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || !this.connected) return;
        
        let finalMessage = message;
        
        // Apply prefix/suffix if enabled - FIXED
        if (this.prefixSuffixEnabled) {
            if (this.messagePrefix && this.messagePrefix.trim()) {
                finalMessage = this.messagePrefix.trim() + ' ' + finalMessage;
            }
            if (this.messageSuffix && this.messageSuffix.trim()) {
                finalMessage = finalMessage + ' ' + this.messageSuffix.trim();
            }
        }
        
        // Add to queue if spacing is enabled - FIXED
        if (this.spacingEnabled && this.messageDelay > 0) {
            this.addToQueue(finalMessage);
        } else {
            await this.sendDirectMessage(finalMessage);
        }
        
        this.messageInput.value = '';
        this.messageInput.style.height = 'auto';
    }
    
    async sendDirectMessage(message, fromQueue = false) {
        try {
            const result = await ipcRenderer.invoke('send-message', {
                channelId: this.channelId,
                content: message
            });
            
            if (result.success) {
                // FIXED: Only display message once and track it
                const messageId = result.messageId || Date.now().toString();
                if (!this.sentMessageIds.has(messageId)) {
                    this.sentMessageIds.add(messageId);
                    this.displayMessage({
                        id: messageId,
                        author: this.currentUser,
                        content: message,
                        timestamp: new Date().toISOString(),
                        fromSelf: true
                    });
                }
                return true;
            } else {
                this.showNotification('Failed to send message: ' + result.error, 'error');
                return false;
            }
        } catch (error) {
            this.showNotification('Error sending message: ' + error.message, 'error');
            return false;
        }
    }
    
    addToQueue(message) {
        const queueItem = {
            id: Date.now().toString() + Math.random(),
            message: message,
            status: 'pending',
            timestamp: Date.now(),
            order: this.messageQueue.length + 1 // NEW: Proper ordering
        };
        
        this.messageQueue.push(queueItem);
        this.updateQueueDisplay();
        
        if (!this.queuePaused && !this.isProcessingQueue) {
            this.processQueue();
        }
    }
    
    async processQueue() {
        if (this.messageQueue.length === 0 || this.queuePaused) return;
        
        const item = this.messageQueue.find(item => item.status === 'pending');
        if (!item) return;
        
        item.status = 'sending';
        this.updateQueueDisplay();
        
        try {
            const result = await ipcRenderer.invoke('send-message', {
                channelId: this.channelId,
                content: item.message
            });
            
            if (result.success) {
                item.status = 'sent';
                this.displayMessage({
                    id: item.id,
                    author: this.currentUser,
                    content: item.message,
                    timestamp: new Date().toISOString(),
                    fromSelf: true
                });
                
                // Remove sent item after delay
                setTimeout(() => {
                    const index = this.messageQueue.findIndex(qi => qi.id === item.id);
                    if (index !== -1) {
                        this.messageQueue.splice(index, 1);
                        this.updateQueueDisplay();
                    }
                }, 2000);
            } else {
                item.status = 'failed';
                this.showNotification('Failed to send queued message', 'error');
            }
        } catch (error) {
            item.status = 'failed';
            this.showNotification('Error sending queued message', 'error');
        }
        
        this.updateQueueDisplay();
        
        // Continue processing queue with delay
        if (this.spacingEnabled && !this.queuePaused) {
            setTimeout(() => this.processQueue(), this.messageDelay * 1000);
        }
    }
    
    toggleQueuePause() {
        this.queuePaused = !this.queuePaused;
        this.pauseQueueBtn.innerHTML = this.queuePaused 
            ? '<i class="fas fa-play"></i> Resume' 
            : '<i class="fas fa-pause"></i> Pause';
            
        if (!this.queuePaused) {
            this.processQueue();
        }
    }
    
    clearQueue() {
        this.messageQueue = [];
        this.updateQueueDisplay();
    }
    
    updateQueueDisplay() {
        if (this.queueCountDisplay) {
            this.queueCountDisplay.textContent = this.messageQueue.length;
        }
        
        if (this.queueList) {
            this.queueList.innerHTML = '';
            
            this.messageQueue.forEach((item, index) => {
                const queueItem = document.createElement('div');
                queueItem.className = 'queue-item';
                queueItem.innerHTML = `
                    <div class="queue-number">${index + 1}</div>
                    <div class="queue-message">${item.message}</div>
                    <div class="queue-status ${item.status}">${item.status}</div>
                `;
                this.queueList.appendChild(queueItem);
            });
        }
    }
    
    displayMessage(messageData) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message';
        messageDiv.innerHTML = `
            <div class="message-avatar">
                ${messageData.author.username ? messageData.author.username.charAt(0).toUpperCase() : 'U'}
            </div>
            <div class="message-content">
                <div class="message-header">
                    <span class="message-author">${messageData.author.username || 'Unknown'}</span>
                    <span class="message-timestamp">${new Date(messageData.timestamp).toLocaleTimeString()}</span>
                </div>
                <div class="message-text">${messageData.content}</div>
            </div>
        `;
        
        this.messagesContainer.appendChild(messageDiv);
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        
        this.messages.push(messageData);
    }
    
    handleIncomingMessage(messageData) {
        if (messageData.author.id !== this.currentUser?.id) {
            this.displayMessage(messageData);
        }
    }
    
    switchTab(tabName) {
        // Update tab buttons
        this.featureTabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });
        
        // Update tab content
        this.tabContents.forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}-content`);
        });
        
        this.activeTab = tabName;
        this.panelOpen = true;
        this.featurePanel.classList.add('active');
    }
    
    closePanel() {
        this.panelOpen = false;
        this.featurePanel.classList.remove('active');
        this.featureTabs.forEach(tab => tab.classList.remove('active'));
    }
    
    toggleAutobeef() {
        this.autobeefEnabled = !this.autobeefEnabled;
        
        if (this.autobeefEnabled) {
            this.startAutobeef();
        } else {
            this.stopAutobeef();
        }
    }
    
    startAutobeef() {
        if (!this.connected || this.wordlist.length === 0) {
            this.showNotification('Connect to a channel and add words to the wordlist first', 'error');
            this.autobeefEnabled = false;
            return;
        }
        
        this.autobeefStats.sessionStartTime = Date.now();
        this.autobeefStats.messagesSent = 0;
        
        this.autobeefToggle.textContent = 'Stop Autobeef';
        this.autobeefToggle.className = 'btn btn-danger';
        this.autobeefStatusDot.classList.add('active');
        this.autobeefStatusText.textContent = 'Running';
        
        this.scheduleNextAutobeefMessage();
    }
    
    stopAutobeef() {
        if (this.autobeefInterval) {
            clearTimeout(this.autobeefInterval);
            this.autobeefInterval = null;
        }
        
        this.autobeefToggle.textContent = 'Start Autobeef';
        this.autobeefToggle.className = 'btn btn-success';
        this.autobeefStatusDot.classList.remove('active');
        this.autobeefStatusText.textContent = 'Stopped';
        
        if (this.nextMessageTimeDisplay) {
            this.nextMessageTimeDisplay.textContent = '--';
        }
    }
    
    scheduleNextAutobeefMessage() {
        if (!this.autobeefEnabled) return;
        
        const delay = this.autobeefDelay * 1000;
        const nextTime = Date.now() + delay;
        
        this.autobeefInterval = setTimeout(() => {
            this.sendAutobeefMessage();
            this.scheduleNextAutobeefMessage();
        }, delay);
        
        this.updateAutobeefTimer(nextTime);
    }
    
    async sendAutobeefMessage() {
        if (this.wordlist.length === 0) return;
        
        const randomWord = this.wordlist[Math.floor(Math.random() * this.wordlist.length)];
        let message = randomWord;
        
        // Apply typos if enabled
        if (this.typoPercent > 0 && Math.random() * 100 < this.typoPercent) {
            message = this.addTypo(message);
        }
        
        try {
            const result = await ipcRenderer.invoke('send-message', {
                channelId: this.channelId,
                content: message
            });
            
            if (result.success) {
                this.autobeefStats.messagesSent++;
                this.displayMessage({
                    id: Date.now().toString(),
                    author: this.currentUser,
                    content: message,
                    timestamp: new Date().toISOString(),
                    fromSelf: true
                });
                this.updateAutobeefStats();
            }
        } catch (error) {
            console.error('Autobeef message failed:', error);
        }
    }
    
    updateAutobeefTimer(nextTime) {
        const updateTimer = () => {
            if (!this.autobeefEnabled || !this.nextMessageTimeDisplay) return;
            
            const remaining = Math.max(0, Math.ceil((nextTime - Date.now()) / 1000));
            this.nextMessageTimeDisplay.textContent = remaining > 0 ? `${remaining}s` : 'Sending...';
            
            if (remaining > 0) {
                setTimeout(updateTimer, 1000);
            }
        };
        updateTimer();
    }
    
    updateAutobeefStats() {
        if (this.messagesSentDisplay) {
            this.messagesSentDisplay.textContent = this.autobeefStats.messagesSent;
        }
        
        if (this.sessionTimeDisplay && this.autobeefStats.sessionStartTime) {
            const elapsed = Date.now() - this.autobeefStats.sessionStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            this.sessionTimeDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }
    
    addWord() {
        const word = this.newWordInput.value.trim();
        if (!word) return;
        
        if (!this.wordlist.includes(word)) {
            this.wordlist.push(word);
            this.updateWordlistDisplay();
            this.saveSettings();
        }
        
        this.newWordInput.value = '';
    }
    
    updateWordlistDisplay() {
        if (this.wordlistDisplay) {
            this.wordlistDisplay.value = this.wordlist.join('\n');
        }
    }
    
    loadWordlist() {
        // This would typically open a file dialog
        this.showNotification('Load wordlist functionality would open a file dialog', 'info');
    }
    
    saveWordlist() {
        // This would typically save to a file
        this.showNotification('Save wordlist functionality would save to a file', 'info');
    }
    
    clearWordlist() {
        this.wordlist = [];
        this.updateWordlistDisplay();
        this.saveSettings();
    }
    
    addTypo(text) {
        if (text.length < 2) return text;
        
        const typoTypes = ['swap', 'duplicate', 'skip'];
        const typoType = typoTypes[Math.floor(Math.random() * typoTypes.length)];
        const pos = Math.floor(Math.random() * (text.length - 1));
        
        switch (typoType) {
            case 'swap':
                if (pos < text.length - 1) {
                    return text.substring(0, pos) + text[pos + 1] + text[pos] + text.substring(pos + 2);
                }
                break;
            case 'duplicate':
                return text.substring(0, pos + 1) + text[pos] + text.substring(pos + 1);
            case 'skip':
                return text.substring(0, pos) + text.substring(pos + 1);
        }
        return text;
    }
    
    startWpmTracking() {
        this.wpmInterval = setInterval(() => {
            this.updateWpmCounter();
        }, 1000);
    }
    
    updateWpmCounter(currentText) {
        if (currentText !== undefined) {
            this.typingHistory.push({
                timestamp: Date.now(),
                length: currentText.length
            });
        }
        
        // Keep only last 60 seconds of typing history
        const cutoff = Date.now() - 60000;
        this.typingHistory = this.typingHistory.filter(entry => entry.timestamp > cutoff);
        
        if (this.typingHistory.length > 1) {
            const timeSpan = this.typingHistory[this.typingHistory.length - 1].timestamp - this.typingHistory[0].timestamp;
            const charCount = this.typingHistory[this.typingHistory.length - 1].length - this.typingHistory[0].length;
            const wpm = Math.round((charCount / 5) / (timeSpan / 60000));
            
            if (this.wpmCounter) {
                this.wpmCounter.textContent = Math.max(0, wpm);
            }
        }
    }
    
    setKeybind(type) {
        const input = this.keybindInputs[type];
        if (!input) return;
        
        input.value = 'Press a key...';
        input.focus();
        
        const handleKeydown = (e) => {
            e.preventDefault();
            const key = e.key.toLowerCase();
            this.keybinds[type] = key;
            input.value = key;
            input.removeEventListener('keydown', handleKeydown);
            this.saveSettings();
        };
        
        input.addEventListener('keydown', handleKeydown, { once: true });
    }
    
    handleGlobalKeydown(e) {
        const key = e.key.toLowerCase();
        
        if (key === this.keybinds.pauseQueue) {
            e.preventDefault();
            this.toggleQueuePause();
        } else if (key === this.keybinds.spacing) {
            e.preventDefault();
            this.spacingEnabled = !this.spacingEnabled;
            if (this.spacingCheckbox) this.spacingCheckbox.checked = this.spacingEnabled;
            this.saveSettings();
        } else if (key === this.keybinds.autobeef) {
            e.preventDefault();
            this.toggleAutobeef();
        }
    }
    
    handleRateLimit(info) {
        this.showNotification(`Rate limited! Wait ${info.retryAfter}ms`, 'warning');
    }
    
    showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ed4245' : type === 'success' ? '#57f287' : type === 'warning' ? '#fee75c' : '#5865f2'};
            color: ${type === 'warning' ? '#000' : '#fff'};
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 500;
            z-index: 10000;
            animation: slideIn 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
    
    saveSettings() {
        const settings = {
            spacingEnabled: this.spacingEnabled,
            messageDelay: this.messageDelay,
            prefixSuffixEnabled: this.prefixSuffixEnabled,
            messagePrefix: this.messagePrefix,
            messageSuffix: this.messageSuffix,
            autobeefDelay: this.autobeefDelay,
            typoPercent: this.typoPercent,
            genderOption: this.genderOption,
            wordlist: this.wordlist,
            keybinds: this.keybinds
        };
        
        localStorage.setItem('zephyr_settings', JSON.stringify(settings));
    }
    
    loadSavedSettings() {
        const savedSettings = localStorage.getItem('zephyr_settings');
        if (savedSettings) {
            try {
                const settings = JSON.parse(savedSettings);
                
                this.spacingEnabled = settings.spacingEnabled || false;
                this.messageDelay = settings.messageDelay || 0.1;
                this.prefixSuffixEnabled = settings.prefixSuffixEnabled || false;
                this.messagePrefix = settings.messagePrefix || '';
                this.messageSuffix = settings.messageSuffix || '';
                this.autobeefDelay = settings.autobeefDelay || 5;
                this.typoPercent = settings.typoPercent || 0;
                this.genderOption = settings.genderOption || 'male';
                this.wordlist = settings.wordlist || [];
                this.keybinds = { ...this.keybinds, ...settings.keybinds };
                
                this.applySettingsToUI();
            } catch (error) {
                console.error('Failed to load settings:', error);
            }
        }
        
        // Load saved token and channel
        const savedToken = localStorage.getItem('discord_token');
        const savedChannel = localStorage.getItem('channel_id');
        
        if (savedToken && this.tokenInput) {
            this.tokenInput.value = savedToken;
            this.token = savedToken;
        }
        
        if (savedChannel && this.channelInput) {
            this.channelInput.value = savedChannel;
        }
    }
    
    applySettingsToUI() {
        if (this.spacingCheckbox) this.spacingCheckbox.checked = this.spacingEnabled;
        if (this.delaySlider) this.delaySlider.value = this.messageDelay;
        if (this.delayDisplay) this.delayDisplay.textContent = `${this.messageDelay}s`;
        if (this.prefixSuffixCheckbox) this.prefixSuffixCheckbox.checked = this.prefixSuffixEnabled;
        if (this.prefixInput) this.prefixInput.value = this.messagePrefix;
        if (this.suffixInput) this.suffixInput.value = this.messageSuffix;
        if (this.autobeefDelaySlider) this.autobeefDelaySlider.value = this.autobeefDelay;
        if (this.autobeefDelayDisplay) this.autobeefDelayDisplay.textContent = `${this.autobeefDelay}s`;
        if (this.typoSlider) this.typoSlider.value = this.typoPercent;
        if (this.typoDisplay) this.typoDisplay.textContent = `${this.typoPercent}%`;
        
        // Apply gender option
        this.genderRadios.forEach(radio => {
            radio.checked = radio.value === this.genderOption;
        });
        
        // Apply keybinds
        Object.keys(this.keybindInputs).forEach(key => {
            if (this.keybindInputs[key] && this.keybinds[key]) {
                this.keybindInputs[key].value = this.keybinds[key];
            }
        });
        
        this.updateWordlistDisplay();
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.zephyrClient = new ZephyrDiscordClient();
});

// Add notification animation CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);