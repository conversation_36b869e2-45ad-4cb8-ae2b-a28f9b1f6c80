/* <PERSON><PERSON><PERSON>r Discord Client - <PERSON> Gray & Purple Theme */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    background: linear-gradient(135deg, #2c2f36 0%, #1e2124 100%);
    color: #e3e5e8;
    overflow: hidden;
    height: 100vh;
    font-size: 12px;
    font-weight: 400;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Global input styling to prevent overflow */
input, textarea {
    box-sizing: border-box !important;
    max-width: 100% !important;
}

input[type="text"], 
input[type="password"], 
textarea {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #0f0f0f;
    overflow: hidden;
}

/* Title Bar */
.title-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;
    background: #2f3136;
    border-bottom: 1px solid #40444b;
    padding: 0 16px;
    -webkit-app-region: drag;
    user-select: none;
}

.title-bar-left {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #8b5cf6;
    font-weight: 600;
    font-size: 13px;
}

.title-bar-left i {
    font-size: 14px;
}

.title-bar-right {
    display: flex;
    gap: 4px;
    -webkit-app-region: no-drag;
}

.title-bar-btn {
    width: 32px;
    height: 24px;
    background: transparent;
    border: none;
    color: #e0e0e0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.title-bar-btn:hover {
    background: #40444b;
}

.title-bar-btn.close:hover {
    background: #dc2626;
    color: white;
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 280px;
    min-width: 280px;
    background: #2f3136;
    border-right: 1px solid #40444b;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

/* Connection Panel */
.connection-panel {
    padding: 16px;
    border-bottom: 1px solid #40444b;
}

.panel-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    color: #8b5cf6;
    font-weight: 600;
    font-size: 14px;
}

.panel-body {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Feature Tabs */
.feature-tabs {
    display: flex;
    flex-direction: column;
    padding: 8px;
}

.feature-tab {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s;
    color: #a0a0a0;
    font-weight: 500;
    margin-bottom: 2px;
}

.feature-tab:hover {
    background: #40444b;
    color: #e0e0e0;
}

.feature-tab.active {
    background: #8b5cf6;
    color: white;
}

.feature-tab i {
    width: 16px;
    text-align: center;
}

/* Tab Contents */
.tab-contents {
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    background: #0a0a0a;
    border-left: 1px solid #1a1a1a;
    overflow-y: auto;
    z-index: 10;
}

.tab-content {
    display: none;
    padding: 16px;
    height: 100%;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
}

.tab-content .panel {
    background: #111111;
    border-radius: 8px;
    border: 1px solid #1a1a1a;
}

.tab-content .panel-header {
    padding: 10px;
    border-bottom: 1px solid #40444b;
}

.tab-content .panel-body {
    padding: 12px;
}

.discord-app {
    display: flex;
    height: 100vh;
    background: linear-gradient(135deg, #2c2f36 0%, #1e2124 100%);
    overflow: hidden;
    border-radius: 12px;
    margin: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

/* Left Sidebar */
.left-sidebar {
    width: 280px;
    min-width: 280px;
    max-width: 280px;
    background: linear-gradient(180deg, #36393f 0%, #2f3136 100%);
    display: flex;
    flex-direction: column;
    border-right: 2px solid #8b5cf6;
    overflow-y: auto;
    flex-shrink: 0;
    border-radius: 12px 0 0 12px;
}

.server-header {
    height: 60px;
    padding: 0 20px;
    border-bottom: 2px solid rgba(139, 92, 246, 0.3);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.server-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.server-header:hover::before {
    left: 100%;
}

.server-header:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
}

.server-name {
    display: flex;
    align-items: center;
    font-weight: 700;
    color: #ffffff;
    font-size: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    z-index: 1;
}



.connection-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #ff6b6b;
    font-weight: 600;
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    z-index: 1;
}

.connection-status.connected {
    color: #51cf66;
}

.connection-status i {
    font-size: 10px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* Sidebar Sections */
.sidebar-section {
    margin: 16px 12px;
    flex-shrink: 0;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    margin-bottom: 12px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    color: #8b5cf6;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(139, 92, 246, 0.1);
    border-radius: 8px;
    border-left: 3px solid #8b5cf6;
}

.section-header:hover {
    color: #a855f7;
    background: rgba(139, 92, 246, 0.2);
    transform: translateX(4px);
}

/* Configuration Form */
.config-form {
    padding: 0 12px;
}

.input-group {
    margin-bottom: 16px;
}

.input-group label {
    display: block;
    font-size: 11px;
    font-weight: 600;
    color: #e3e5e8;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.input-group input {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    background: linear-gradient(135deg, #40444b 0%, #36393f 100%);
    border: 2px solid transparent;
    border-radius: 8px;
    padding: 12px 16px;
    color: #ffffff;
    font-size: 12px;
    transition: all 0.3s ease;
    background-clip: padding-box;
}

.input-group input:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
    transform: translateY(-1px);
}

.input-group input::placeholder {
    color: #72767d;
    font-style: italic;
}

/* Token Input Container */
.token-input-container {
    display: flex;
    gap: 8px;
    align-items: center;
}

.token-input-container input {
    flex: 1;
}

.token-input-container .btn {
    flex-shrink: 0;
}

.btn-icon {
    padding: 8px;
    min-width: auto;
    width: auto;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

/* Checkbox styling */
.checkbox-setting {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 12px 0;
    padding: 10px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.checkbox-setting:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(139, 92, 246, 0.3);
}

.checkbox-setting input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #8b5cf6;
    cursor: pointer;
    border-radius: 3px;
}

.checkbox-setting label {
    cursor: pointer;
    font-size: 11px;
    color: #e3e5e8;
    font-weight: 600;
    flex: 1;
}

.checkbox-setting:hover label {
    color: #ffffff;
}

.setting-note {
    color: #b9bbbe;
    font-size: 10px;
    margin-top: 6px;
    line-height: 1.3;
    font-style: italic;
}

.emoji-setting {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin: 12px 0;
    padding: 12px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.emoji-setting label {
    color: #ffffff;
    font-weight: 600;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.emoji-setting input[type="text"] {
    width: 60px;
    height: 36px;
    background: linear-gradient(135deg, #40444b 0%, #36393f 100%);
    border: 1px solid transparent;
    border-radius: 6px;
    color: #ffffff;
    text-align: center;
    font-size: 16px;
    padding: 6px;
    transition: all 0.3s ease;
    font-weight: 600;
}

.emoji-setting input[type="text"]:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
    transform: scale(1.02);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 12px;
    color: #cccccc;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #7c3aed;
    cursor: pointer;
}

.checkbox-label:hover {
    color: #ffffff;
}

.token-input-wrapper {
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.toggle-visibility {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #888888;
    cursor: pointer;
    padding: 6px;
    font-size: 14px;
}

.toggle-visibility:hover {
    color: #cccccc;
}

/* Buttons */
.btn {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: #ffffff;
    border: 1px solid transparent;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 10px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 6px;
    width: 100%;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    background: linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(139, 92, 246, 0.4);
}

.btn-purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.btn-purple:hover {
    background: linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #ffffff;
}

.btn-success:hover {
    background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: #ffffff;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-secondary {
    background: #40444b;
}

.btn-secondary:hover {
    background: #4f545c;
}

.btn-small {
    padding: 4px 8px;
    font-size: 10px;
}

/* Feature Tabs */
.feature-tabs {
    padding: 0 12px;
}

.feature-tab {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    margin: 6px 0;
    border-radius: 12px;
    color: #b9bbbe;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 12px;
    font-weight: 600;
    background: linear-gradient(135deg, #40444b 0%, #36393f 100%);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.feature-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
    transition: left 0.5s ease;
}

.feature-tab:hover::before {
    left: 100%;
}

.feature-tab:hover {
    background: linear-gradient(135deg, #4c5058 0%, #40444b 100%);
    color: #e3e5e8;
    border-color: rgba(139, 92, 246, 0.3);
    transform: translateX(4px);
}

.feature-tab.active {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: #ffffff;
    border-color: #8b5cf6;
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
    transform: translateX(8px);
}

.feature-tab i {
    font-size: 14px;
    z-index: 1;
}

/* Chat Area */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: linear-gradient(180deg, #40444b 0%, #36393f 100%);
    min-width: 0;
    overflow: hidden;
    border-radius: 0 12px 12px 0;
}

/* Chat Header */
.chat-header {
    height: 60px;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 2px solid rgba(139, 92, 246, 0.3);
    background: linear-gradient(135deg, #2f3136 0%, #36393f 100%);
    flex-shrink: 0;
    backdrop-filter: blur(10px);
}

.channel-info {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #ffffff;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.channel-info i {
    color: #8b5cf6;
    font-size: 18px;
    filter: drop-shadow(0 2px 4px rgba(139, 92, 246, 0.4));
}

.header-controls {
    display: flex;
    gap: 12px;
}

.header-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    color: #b9bbbe;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    backdrop-filter: blur(10px);
}

.header-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #e3e5e8;
    transform: translateY(-1px);
}

.header-btn.close-btn:hover {
    background: #ff6b6b;
    color: #ffffff;
    border-color: #ff6b6b;
}

/* Messages Container */
.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    min-height: 0;
    background: linear-gradient(180deg, rgba(64, 68, 75, 0.3) 0%, rgba(54, 57, 63, 0.3) 100%);
}

.message-item {
    display: flex;
    padding: 12px 16px;
    margin-bottom: 6px;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    min-height: fit-content;
}

.message-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.message-item.own-message {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.15) 0%, rgba(124, 58, 237, 0.1) 100%);
    border-color: rgba(139, 92, 246, 0.3);
}

.message-item.own-message:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.25) 0%, rgba(124, 58, 237, 0.2) 100%);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.2);
}

.message-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
}

.message-avatar {
    flex-shrink: 0;
    margin-right: 16px;
    margin-top: 2px;
}

.message-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid rgba(139, 92, 246, 0.3);
    transition: all 0.3s ease;
    object-fit: cover;
}

.message-avatar img:hover {
    border-color: #8b5cf6;
    transform: scale(1.05);
}

.avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 16px;
    border: 2px solid rgba(139, 92, 246, 0.3);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-header {
    display: flex;
    align-items: baseline;
    margin-bottom: 6px;
    gap: 12px;
}

.message-author {
    font-weight: 700;
    color: #ffffff;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.message-author:hover {
    transform: translateY(-1px);
}

.message-author.own-message {
    color: #8b5cf6 !important;
    text-shadow: 0 0 8px rgba(139, 92, 246, 0.5);
}

.message-timestamp {
    font-size: 11px;
    color: #72767d;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.05);
    padding: 2px 6px;
    border-radius: 6px;
}

.message-text {
    color: #e3e5e8;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
    white-space: pre-wrap;
}

/* Message Pings */
.user-mention {
    background: #5865f2;
    color: #ffffff;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.user-mention:hover {
    background: #4752c4;
}

.user-mention.mention-me {
    background: #faa61a;
    color: #000000;
    animation: mentionPulse 2s ease-in-out;
}

@keyframes mentionPulse {
    0%, 100% { background: #faa61a; }
    50% { background: #ffb84d; }
}

.role-mention {
    background: #7289da;
    color: #ffffff;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

.channel-mention {
    background: #5865f2;
    color: #ffffff;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

/* Message that pings current user */
.message-pings-me {
    background: linear-gradient(135deg, rgba(250, 166, 26, 0.1) 0%, rgba(255, 184, 77, 0.05) 100%);
    border-left: 3px solid #faa61a;
}

.message-ping-me {
    background: rgba(250, 166, 26, 0.1);
    border-radius: 6px;
    padding: 8px;
}

/* Message Replies */
.message-reply {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    padding: 4px 0 4px 12px;
    position: relative;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.message-reply.reply-to-me {
    background: rgba(250, 166, 26, 0.1);
    border-left: 3px solid #faa61a;
    padding-left: 9px;
}

.reply-bar {
    position: absolute;
    left: 0;
    top: 4px;
    bottom: 4px;
    width: 3px;
    background: #4f545c;
    border-radius: 2px;
}

.message-reply.reply-to-me .reply-bar {
    background: #faa61a;
}

.reply-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-left: 8px;
    flex: 1;
}

.reply-author {
    font-size: 12px;
    font-weight: 600;
    color: #8b5cf6;
    cursor: pointer;
}

.reply-author:hover {
    text-decoration: underline;
}

.message-reply.reply-to-me .reply-author {
    color: #faa61a;
}

.reply-text {
    font-size: 12px;
    color: #b9bbbe;
    opacity: 0.8;
    line-height: 1.3;
    cursor: pointer;
}

.reply-text:hover {
    opacity: 1;
}

/* Message Reactions */
.message-reactions {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
    margin-bottom: 4px;
}

.message-reaction {
    display: flex;
    align-items: center;
    gap: 3px;
    background: rgba(79, 84, 92, 0.2);
    border: 1px solid rgba(79, 84, 92, 0.4);
    border-radius: 8px;
    padding: 3px 6px;
    cursor: pointer;
    transition: all 0.15s ease;
    font-size: 12px;
    min-height: 22px;
    user-select: none;
}

.message-reaction:hover {
    background: rgba(79, 84, 92, 0.4);
    border-color: rgba(79, 84, 92, 0.6);
    transform: scale(1.05);
}

.message-reaction.me-reacted {
    background: rgba(88, 101, 242, 0.15);
    border-color: rgba(88, 101, 242, 0.5);
    color: #5865f2;
}

.message-reaction.me-reacted:hover {
    background: rgba(88, 101, 242, 0.25);
    border-color: rgba(88, 101, 242, 0.7);
}

.reaction-emoji {
    font-size: 16px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.reaction-count {
    font-size: 11px;
    font-weight: 600;
    color: #b9bbbe;
    min-width: 8px;
    text-align: center;
}

.message-reaction.me-reacted .reaction-count {
    color: #5865f2;
    font-weight: 700;
}

/* Message Input Area */
.message-input-area {
    padding: 10px;
    background: #36393f;
    flex-shrink: 0;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 0;
    color: #666666;
    font-size: 12px;
}

.typing-dots {
    display: flex;
    gap: 2px;
}

.typing-dots span {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #666666;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 80%, 100% { opacity: 0.3; }
    40% { opacity: 1; }
}

.message-input-container {
    background: linear-gradient(135deg, #40444b 0%, #36393f 100%);
    border-radius: 16px;
    padding: 0;
    margin: 20px;
    border: 2px solid rgba(139, 92, 246, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.message-input-container:focus-within {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.1);
    transform: translateY(-2px);
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    padding: 16px 20px;
}

#messageInput {
    flex: 1;
    background: transparent;
    border: none;
    color: #ffffff;
    font-size: 14px;
    line-height: 1.5;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    overflow-y: auto;
    font-weight: 500;
}

#messageInput::placeholder {
    color: #72767d;
    font-style: italic;
}

#messageInput:focus {
    outline: none;
}

.input-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: 12px;
}

.queue-info {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #a3a6aa;
}

.queue-count {
    background-color: #7c3aed;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
    min-width: 18px;
    text-align: center;
}

.send-btn {
    background: none;
    border: none;
    color: #a3a6aa;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.1s ease;
}

.send-btn:hover {
    color: #dcddde;
}

.send-btn:disabled {
    background: #333333;
    color: #666666;
    cursor: not-allowed;
}

/* Message Options */
.message-options {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 10px 12px;
    background: #2f3136;
    border-radius: 0 0 8px 8px;
    font-size: 12px;
}

.option-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.option-toggle {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    color: #cccccc;
    font-weight: 500;
}

.option-toggle input[type="checkbox"] {
    margin: 0;
    transform: scale(1.1);
    accent-color: #7c3aed;
}

.delay-display {
    color: #7c3aed;
    font-weight: 600;
    font-size: 12px;
}

.delay-slider {
    width: 60px;
    height: 6px;
    background: #72767d;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.delay-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: #b9bbbe;
    cursor: pointer;
    transition: all 0.3s ease;
}

.delay-slider::-webkit-slider-thumb:hover {
    background: #dcddde;
    transform: scale(1.1);
}

.delay-slider::-moz-range-thumb {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: #b9bbbe;
    cursor: pointer;
    border: none;
}

.wpm-counter {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666666;
    margin-left: auto;
    font-size: 12px;
    font-weight: 500;
}

.wpm-counter i {
    color: #7c3aed;
    font-size: 14px;
}

/* Feature Panel */
.feature-panel {
    width: 336px;
    min-width: 336px;
    max-width: 336px;
    background: linear-gradient(180deg, #36393f 0%, #2f3136 100%);
    border-left: 2px solid #8b5cf6;
    display: none;
    flex-direction: column;
    overflow-y: auto;
    flex-shrink: 0;
    border-radius: 0 12px 12px 0;
}

.feature-panel.active {
    display: flex;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.tab-content {
    display: none;
    flex: 1;
    flex-direction: column;
    min-height: 0;
}

.tab-content.active {
    display: flex;
}

.panel-header {
    height: 48px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 2px solid rgba(139, 92, 246, 0.3);
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    flex-shrink: 0;
}

.panel-header h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #ffffff;
    font-size: 14px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.panel-header h3 i {
    color: #ffffff;
    font-size: 16px;
    filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.3));
}

.close-panel {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    backdrop-filter: blur(10px);
}

.close-panel:hover {
    background: #ff6b6b;
    color: #ffffff;
    border-color: #ff6b6b;
    transform: translateY(-1px);
}



/* Settings Groups */
.settings-group {
    margin-bottom: 14px;
    padding: 14px;
    background: linear-gradient(135deg, rgba(64, 68, 75, 0.8) 0%, rgba(54, 57, 63, 0.8) 100%);
    border-radius: 10px;
    border: 1px solid rgba(139, 92, 246, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.settings-group:hover {
    border-color: rgba(139, 92, 246, 0.4);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.settings-group h4 {
    color: #ffffff;
    margin: 0 0 12px 0;
    font-size: 12px;
    font-weight: 700;
    border-bottom: 1px solid rgba(139, 92, 246, 0.3);
    padding-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Input Rows */
.input-row {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.input-row input[type="text"] {
    flex: 1;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    padding: 8px 10px;
    background: linear-gradient(135deg, #40444b 0%, #36393f 100%);
    border: 1px solid transparent;
    border-radius: 6px;
    color: #ffffff;
    font-size: 10px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.input-row input[type="text"]:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
    transform: translateY(-1px);
}

/* Import/Export Section */
.wordlist-import-export-section {
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 6px;
    border: 1px solid #40444b;
}

.wordlist-import-export-section h4 {
    margin: 0 0 10px 0;
    color: #dcddde;
    font-size: 13px;
    font-weight: 600;
}

.import-export-buttons {
    display: flex;
    gap: 8px;
}

.import-export-buttons .btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 12px;
    padding: 8px 12px;
    cursor: pointer;
    text-decoration: none;
}

.import-export-buttons .btn i {
    font-size: 11px;
}

.slider-row {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.slider-row label {
    min-width: 150px;
    color: #dcddde;
    font-weight: 500;
}

.slider-row input[type="range"] {
    flex: 1;
}

.checkbox-row {
    margin-bottom: 10px;
}

.checkbox-row label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #dcddde;
    cursor: pointer;
}

.button-row {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 12px;
}

.stat-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px;
    background: #40444b;
    border-radius: 4px;
    border: 1px solid #72767d;
    text-align: center;
}

.stat-label {
    font-size: 11px;
    color: #888888;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 20px;
    font-weight: bold;
    color: #7c3aed;
}

/* Words List */
.words-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #40444b;
    border-radius: 4px;
    padding: 8px;
    background: #36393f;
}

.word-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #40444b;
}

.word-item:last-child {
    border-bottom: none;
}

.word-text {
    color: #ffffff;
    font-size: 13px;
    flex: 1;
}

.empty-state {
    color: #666666;
    text-align: center;
    padding: 16px;
    font-style: italic;
    font-size: 13px;
}

/* Generated Display */
.generated-display {
    padding: 12px;
    background: #36393f;
    border: 1px solid #40444b;
    border-radius: 4px;
    min-height: 50px;
    font-family: 'Consolas', 'Monaco', monospace;
    color: #ffffff;
    font-size: 13px;
    line-height: 1.4;
    word-wrap: break-word;
    margin-bottom: 8px;
}

/* Queue List */
.queue-list {
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid #40444b;
    border-radius: 4px;
    padding: 8px;
    background: #36393f;
}

.queue-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    margin-bottom: 6px;
    background: #40444b;
    border-radius: 4px;
    border: 1px solid #72767d;
}

.queue-message {
    flex: 1;
    color: #ffffff;
    font-size: 13px;
    margin-right: 8px;
}

.queue-time {
    color: #666666;
    font-size: 11px;
    margin-right: 8px;
}

.queue-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    padding: 8px;
    background: #40444b;
    border-radius: 4px;
    border: 1px solid #72767d;
}

.queue-info span {
    color: #ffffff;
    font-weight: 500;
    font-size: 13px;
}

.queue-info #queueCount {
    background: #7c3aed;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: bold;
}

/* Status Display */
.status-display {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: #40444b;
    border-radius: 4px;
    border: 1px solid #72767d;
}

.status-indicator,
.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-indicator.online,
.status-dot.online {
    background: #57f287;
    box-shadow: 0 0 6px rgba(87, 242, 135, 0.4);
}

.status-indicator.offline,
.status-dot.offline {
    background: #ed4245;
}

/* Keybind Styles */
.keybind-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.keybind-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px;
    background: #40444b;
    border-radius: 4px;
    border: 1px solid #72767d;
}

.keybind-item span {
    min-width: 100px;
    font-weight: 500;
    color: #ffffff;
    font-size: 13px;
}

.keybind-input {
    flex: 1;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    padding: 6px 8px;
    background: #36393f;
    border: 1px solid #40444b;
    border-radius: 4px;
    color: #ffffff;
    text-align: center;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 11px;
}

.keybind-input:focus {
    outline: none;
    border-color: #7c3aed;
    box-shadow: 0 0 0 1px rgba(124, 58, 237, 0.2);
}

/* Button Styles */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background: #7289da;
    color: white;
}

.btn-primary:hover {
    background: #677bc4;
}

.btn-success {
    background: #43b581;
    color: white;
}

.btn-success:hover {
    background: #3ca374;
}

.btn-warning {
    background: #faa61a;
    color: white;
}

.btn-warning:hover {
    background: #e8940f;
}

.btn-danger {
    background: #f04747;
    color: white;
}

.btn-danger:hover {
    background: #d73c3c;
}

.btn-secondary {
    background: #4f545c;
    color: white;
}

.btn-secondary:hover {
    background: #5d6269;
}

.btn-small {
    padding: 4px 8px;
    font-size: 12px;
}

/* Remove unwanted icons from header */
.header-controls .header-btn:not(#minimizeBtn):not(#closeBtn) {
    display: none;
}

/* Hide rate limit notifications */
.rate-limit-notification,
.notification.rate-limit {
    display: none !important;
}

/* Wordlist Generator Styles */
.wordlist-generator {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.wordlist-input-section,
.words-preview-section,
.generator-settings,
.generated-sentence-section {
    background: #40444b;
    border-radius: 4px;
    border: 1px solid #72767d;
    padding: 8px;
}

.wordlist-input-section h4,
.words-preview-section h4,
.generator-settings h4,
.generated-sentence-section h4 {
    color: #ffffff;
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    border-bottom: 1px solid #72767d;
    padding-bottom: 4px;
}

.words-preview-container {
    max-height: 120px;
    overflow-y: auto;
    border: 1px solid #40444b;
    border-radius: 4px;
    background: #36393f;
}

.words-preview {
    padding: 6px;
    min-height: 60px;
}

.words-preview .no-words {
    color: #666666;
    text-align: center;
    font-style: italic;
    margin: 30px 0;
    font-size: 13px;
}

.word-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: #7c3aed;
    color: white;
    padding: 3px 6px;
    border-radius: 10px;
    margin: 2px;
    font-size: 11px;
    font-weight: 500;
}

.word-tag .delete-word {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 9px;
    transition: background 0.2s ease;
}

.word-tag .delete-word:hover {
    background: rgba(255, 255, 255, 0.2);
}

.settings-row {
    display: flex;
    gap: 16px;
}

.range-setting {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.range-setting label {
    color: #ffffff;
    font-weight: 500;
    font-size: 13px;
}

.range-setting input[type="range"] {
    width: 100%;
    height: 6px;
    background: #72767d;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.range-setting input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #b9bbbe;
    cursor: pointer;
    transition: all 0.3s ease;
}

.range-setting input[type="range"]::-webkit-slider-thumb:hover {
    background: #dcddde;
    transform: scale(1.1);
}

.range-setting input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #b9bbbe;
    cursor: pointer;
    border: none;
}

.generated-sentence-container {
    margin-bottom: 8px;
}

.generated-sentence {
    width: 100%;
    padding: 8px;
    background: #36393f;
    border: 1px solid #40444b;
    border-radius: 4px;
    min-height: 80px;
    max-height: 200px;
    color: #ffffff;
    font-size: 12px;
    line-height: 1.4;
    word-wrap: break-word;
    font-family: 'Consolas', 'Monaco', monospace;
    resize: vertical;
    outline: none;
}

.generated-sentence:focus {
    border-color: #7c3aed;
    box-shadow: 0 0 0 1px rgba(124, 58, 237, 0.2);
}

.generator-buttons {
    display: flex;
    gap: 6px;
}

.generator-buttons .btn {
    flex: 1;
    padding: 6px 8px;
    font-size: 11px;
}

/* Autobeef Controls Repositioning */
.autobeef-status-section {
    margin-bottom: 16px;
}

.autobeef-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: #40444b;
    border-radius: 4px;
    border: 1px solid #72767d;
}

.autobeef-status span {
    color: #ffffff;
    font-weight: 500;
    font-size: 13px;
}

.autobeef-controls {
    margin-top: 16px;
    text-align: center;
}

.autobeef-controls .btn {
    width: 100%;
    padding: 10px;
    font-size: 14px;
    font-weight: 600;
}

/* Stats Grid Updates - Already defined above, removing duplicate */

.stat-card .btn-small {
    margin-top: 6px;
    padding: 3px 6px;
    font-size: 9px;
}

/* Purple button style */
.btn-purple {
    background: #7c3aed;
    color: white;
}

.btn-purple:hover {
    background: #6d28d9;
}

/* Panel body scrolling fix */
.panel-body {
    flex: 1;
    padding: 8px;
    overflow-y: auto;
    min-height: 0;
    max-height: calc(100vh - 48px);
}

/* Ensure proper containment */
.discord-app {
    display: flex;
    height: 100vh;
    background: #0f0f0f;
    overflow: hidden;
    max-width: 100vw;
}

/* Fix button row spacing */
.button-row {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 8px;
}

/* Queue controls styling */
.queue-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.queue-buttons {
    display: flex;
    gap: 8px;
}

/* Remove any AI-looking gradients or fancy effects */
* {
    box-shadow: none !important;
    text-shadow: none !important;
}

/* Keep only essential shadows for depth */
.feature-panel {
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.3) !important;
}

.notification {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4) !important;
}

/* Simplify status dots */
.status-dot.online {
    box-shadow: 0 0 4px rgba(87, 242, 135, 0.3) !important;
}

/* Clean up any remaining fancy styling */
.message {
    background: none;
    border: none;
}

.message:hover {
    background: #40444b;
}

/* Ensure text is readable and clean */
body, input, textarea, button {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Fix any overflow issues */
.left-sidebar,
.chat-area,
.feature-panel {
    overflow-x: hidden;
}

/* Clean button styling */
.btn {
    border: none;
    outline: none;
    font-family: inherit;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.3) !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .left-sidebar {
        width: 220px;
        min-width: 220px;
        max-width: 220px;
    }
    
    .feature-panel {
        width: 260px;
        min-width: 260px;
        max-width: 260px;
    }
}

@media (max-width: 900px) {
    .left-sidebar {
        width: 180px;
        min-width: 180px;
        max-width: 180px;
    }
    
    .feature-panel {
        width: 240px;
        min-width: 240px;
        max-width: 240px;
    }
}

@media (max-width: 768px) {
    .feature-panel {
        display: none;
    }
}

/* Clean up any remaining visual clutter */
.feature-tab {
    justify-content: center;
}

.feature-tab i {
    margin-right: 6px;
}

/* Ensure consistent spacing */
.settings-group:last-child {
    margin-bottom: 0;
}

/* Clean notification styling */
.notification {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    border-radius: 4px;
}

/* Ensure proper text contrast */
.word-tag {
    font-weight: 600;
}

/* Clean up any remaining gradients */
input[type="range"] {
    background: #72767d !important;
}

/* Modern Scrollbar Styling */
.discord-app * {
    scrollbar-width: thin;
    scrollbar-color: #8b5cf6 rgba(64, 68, 75, 0.3);
}

.discord-app *::-webkit-scrollbar {
    width: 8px;
}

.discord-app *::-webkit-scrollbar-track {
    background: rgba(64, 68, 75, 0.3);
    border-radius: 8px;
}

.discord-app *::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.discord-app *::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
}

/* Queue Display */
.queue-item {
    background-color: #2f3136;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 4px;
    border-left: 3px solid #72767d;
}

.queue-item.queued {
    border-left-color: #faa61a;
}

.queue-item.sending {
    border-left-color: #5865f2;
    animation: pulse 1.5s infinite;
}

.queue-item.failed {
    border-left-color: #ed4245;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.queue-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    font-size: 11px;
    color: #a3a6aa;
    text-transform: uppercase;
    font-weight: 600;
}

.queue-position {
    color: #b91d97;
}

.queue-item-content {
    color: #dcddde;
    font-size: 14px;
    word-break: break-word;
}

.queue-retries {
    font-size: 11px;
    color: #ed4245;
    margin-top: 4px;
}

.queue-empty {
    text-align: center;
    padding: 32px;
    color: #72767d;
}

.queue-empty i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* Mentions and Links */
.mention {
    background-color: rgba(88, 101, 242, 0.3);
    color: #dee0fc;
    padding: 0 2px;
    border-radius: 3px;
    font-weight: 500;
}

.channel-mention {
    background-color: rgba(88, 101, 242, 0.15);
    color: #00aff4;
    padding: 0 2px;
    border-radius: 3px;
    font-weight: 500;
}

.message-text a {
    color: #00aff4;
    text-decoration: none;
}

.message-text a:hover {
    text-decoration: underline;
}

/* Enhanced Messages Scrollbar */
.messages-container::-webkit-scrollbar {
    width: 12px;
}

.messages-container::-webkit-scrollbar-track {
    background: rgba(64, 68, 75, 0.2);
    border-radius: 12px;
    margin: 8px;
}

.messages-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.5);
}

/* Full width preview column for combined words/phrases */
.preview-column.full-width {
    width: 100%;
}

.words-preview-section {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.preview-column {
    flex: 1;
}

.preview-column.full-width {
    flex: none;
    width: 100%;
}

/* Burst Mode Styles */
.burst-mode-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.burst-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: #ffffff;
    font-weight: 500;
}

.burst-toggle input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #7c3aed;
}

.burst-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.burst-status .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ed4245;
    transition: background-color 0.3s ease;
}

.burst-status .status-dot.active {
    background: #57f287;
}

/* Message Display Styles */
.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: #0f0f0f;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.message:hover {
    background: rgba(255, 255, 255, 0.02);
}

.message.own-message {
    background: rgba(139, 92, 246, 0.1);
    border-left: 3px solid #8b5cf6;
}

.message.own-message:hover {
    background: rgba(139, 92, 246, 0.15);
}

.message-avatar {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    background: #2a2a2a;
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-header {
    display: flex;
    align-items: baseline;
    gap: 8px;
    margin-bottom: 4px;
}

.message-author {
    font-weight: 600;
    color: #ffffff;
    font-size: 14px;
}

.message.own-message .message-author {
    color: #8b5cf6;
}

.message-timestamp {
    font-size: 11px;
    color: #72767d;
    font-weight: 400;
}

.message-text {
    color: #e0e0e0;
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: pre-wrap;
}



.burst-settings {
    opacity: 0.5;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.burst-settings.enabled {
    opacity: 1;
    pointer-events: all;
}

.burst-type-setting {
    margin-bottom: 16px;
    padding: 12px;
    background: #40444b;
    border-radius: 4px;
    border: 1px solid #72767d;
}

.burst-type-setting label {
    display: block;
    color: #ffffff;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 8px;
}

.burst-type-setting input[type="range"] {
    width: 100%;
    height: 6px;
    background: #72767d;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    margin-bottom: 6px;
}

.burst-type-setting input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background: #b9bbbe;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.burst-type-setting input[type="range"]::-webkit-slider-thumb:hover {
    background: #dcddde;
    transform: scale(1.1);
}

.burst-type-setting small {
    display: block;
    color: #72767d;
    font-size: 11px;
    font-style: italic;
}

.burst-info {
    margin-top: 16px;
    padding: 12px;
    background: rgba(255, 165, 0, 0.1);
    border-radius: 4px;
    border-left: 3px solid #ffa500;
}

.burst-info p {
    margin: 4px 0;
    font-size: 11px;
    color: #ffa500;
    display: flex;
    align-items: center;
    gap: 6px;
}

.burst-info i {
    font-size: 10px;
}

/* Burst Mode Styles */
.burst-mode-active {
    border: 2px solid #8b5cf6 !important;
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.4) !important;
    animation: burstGlow 1.0s ease-in-out infinite alternate;
}

@keyframes burstGlow {
    from {
        box-shadow: 0 0 10px rgba(139, 92, 246, 0.4);
    }
    to {
        box-shadow: 0 0 20px rgba(139, 92, 246, 0.8);
    }
}

/* Priority Message Styles */
.priority-mode-active {
    border: 2px solid #22c55e !important;
    box-shadow: 0 0 10px rgba(34, 197, 94, 0.4) !important;
    animation: priorityGlow 1.5s ease-in-out infinite alternate;
}

@keyframes priorityGlow {
    from {
        box-shadow: 0 0 10px rgba(34, 197, 94, 0.4);
    }
    to {
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.7);
    }
}

/* Spam Mode Styles */
.spam-mode-active {
    border: 2px solid #8b5cf6 !important;
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.4) !important;
    animation: spamGlow 1.2s ease-in-out infinite alternate;
}

@keyframes spamGlow {
    from {
        box-shadow: 0 0 10px rgba(139, 92, 246, 0.4);
    }
    to {
        box-shadow: 0 0 20px rgba(139, 92, 246, 0.7);
    }
}

/* Spam Panel Styles */
.spam-info-section {
    margin-bottom: 20px;
}

.info-box {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: rgba(139, 92, 246, 0.1);
    border-radius: 8px;
    border-left: 3px solid #8b5cf6;
}

.info-box i {
    color: #8b5cf6;
    font-size: 18px;
    margin-top: 2px;
    flex-shrink: 0;
}

.info-box h4 {
    color: #8b5cf6;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-box p {
    color: #b9bbbe;
    margin-bottom: 8px;
    font-size: 13px;
}

.info-box ul {
    margin-left: 16px;
    color: #b9bbbe;
}

.info-box li {
    margin-bottom: 4px;
    font-size: 12px;
}

.spam-input-section {
    margin-bottom: 20px;
}

.spam-input-container {
    background: #40444b;
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #72767d;
}

.spam-input {
    width: 100%;
    background: #36393f;
    border: 1px solid #40444b;
    border-radius: 6px;
    padding: 12px;
    color: #e0e0e0;
    font-family: inherit;
    font-size: 14px;
    resize: vertical;
    min-height: 80px;
    margin-bottom: 12px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.spam-input:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

.spam-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.spam-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.spam-count-label {
    color: #72767d;
    font-size: 13px;
}

.spam-count {
    color: #8b5cf6;
    font-weight: bold;
    font-size: 14px;
}

.spam-status-section {
    margin-bottom: 20px;
}

.status-display {
    background: #40444b;
    border-radius: 6px;
    padding: 12px;
    border: 1px solid #72767d;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-label {
    color: #72767d;
    font-size: 13px;
}

.status-value {
    color: #e0e0e0;
    font-size: 13px;
    font-weight: 500;
}

.spam-keybind-section {
    background: #40444b;
    border-radius: 6px;
    padding: 16px;
    border: 1px solid #72767d;
}

.spam-keybind-section h4 {
    color: #8b5cf6;
    margin-bottom: 8px;
}

.spam-keybind-section p {
    color: #b9bbbe;
    font-size: 13px;
    margin-bottom: 12px;
    line-height: 1.4;
}

.keybind-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #0f0f0f;
    border-radius: 4px;
    border: 1px solid #3a3a3a;
}

.keybind-label {
    color: #72767d;
    font-size: 13px;
}

.keybind-value {
    color: #8b5cf6;
    font-size: 13px;
    font-weight: 500;
    text-transform: uppercase;
}

/* Enhanced keybind item styles for new keybinds */
.keybind-item:has(#priorityKeybind) span:first-child {
    color: #ffd700;
}

.keybind-item:has(#spamKeybind) span:first-child {
    color: #8b5cf6;
}

/* Button enhancements for spam */
.btn-danger {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    border: 1px solid #dc2626;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* Status indicators */
#spamModeStatus.active {
    color: #8b5cf6;
    font-weight: bold;
}

#lastSpamStatus.recent {
    color: #10b981;
}

#priorityModeStatus.active {
    color: #ffd700;
    font-weight: bold;
}

/* Typo Adder Styles */
.typo-adder {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.typo-input-section,
.typo-settings,
.typo-output-section {
    background: #40444b;
    border-radius: 4px;
    border: 1px solid #72767d;
    padding: 8px;
}

.typo-input-section h4,
.typo-settings h4,
.typo-output-section h4 {
    color: #ffffff;
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    border-bottom: 1px solid #72767d;
    padding-bottom: 4px;
}

.typo-input-section textarea,
.typo-output {
    width: 100%;
    padding: 8px;
    background: #36393f;
    border: 1px solid #40444b;
    border-radius: 4px;
    min-height: 120px;
    max-height: 200px;
    color: #ffffff;
    font-size: 12px;
    line-height: 1.4;
    word-wrap: break-word;
    font-family: 'Consolas', 'Monaco', monospace;
    resize: vertical;
    outline: none;
}

.typo-input-section textarea:focus,
.typo-output:focus {
    border-color: #7c3aed;
    box-shadow: 0 0 0 1px rgba(124, 58, 237, 0.2);
}

.typo-buttons {
    display: flex;
    gap: 6px;
}

.typo-buttons .btn {
    flex: 1;
    padding: 6px 8px;
    font-size: 11px;
}
