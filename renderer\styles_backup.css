/* Zephyr Discord Client - Black & Purple Theme */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Whitney', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    background: #2f3136;
    color: #dcddde;
    overflow: hidden;
    height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.header h1 {
    font-size: 2.5rem;
    background: linear-gradient(45deg, #8b5cf6, #06b6d4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.header p {
    color: #a0a0a0;
    font-size: 1.1rem;
}

/* Connection Section */
.connection-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.connection-section h2 {
    color: #8b5cf6;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #e0e0e0;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.form-group input[type="password"] {
    font-family: monospace;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    margin: 5px;
}

.btn-primary {
    background: linear-gradient(45deg, #8b5cf6, #06b6d4);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(139, 92, 246, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
}

.btn-success {
    background: linear-gradient(45deg, #10b981, #059669);
    color: white;
}

.btn-warning {
    background: linear-gradient(45deg, #f59e0b, #d97706);
    color: white;
}

.btn-danger {
    background: linear-gradient(45deg, #ef4444, #dc2626);
    color: white;
}

.btn-small {
    padding: 8px 16px;
    font-size: 12px;
}

/* Status Indicators */
.status {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    margin: 5px;
}

.status.connected {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status.disconnected {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.status.warning {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

/* Tab System */
.tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 5px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-button {
    flex: 1;
    padding: 12px 20px;
    background: transparent;
    border: none;
    color: #a0a0a0;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.tab-button.active {
    background: linear-gradient(45deg, #8b5cf6, #06b6d4);
    color: white;
}

.tab-button:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

/* Tab Panels */
.tab-panels {
    flex: 1;
}

.tab-panel {
    display: none;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 400px;
}

.tab-panel.active {
    display: block;
}

.tab-panel h3 {
    color: #8b5cf6;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

/* Message Input */
.message-section {
    margin-bottom: 20px;
}

.message-input-container {
    position: relative;
    margin-bottom: 15px;
}

#messageInput {
    width: 100%;
    min-height: 60px;
    max-height: 200px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #ffffff;
    font-size: 14px;
    resize: none;
    transition: all 0.3s ease;
    font-family: inherit;
}

#messageInput:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

#messageInput::placeholder {
    color: #a0a0a0;
}

/* Queue Display */
.queue-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
}

.queue-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.queue-stat {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #e0e0e0;
}

.queue-stat i {
    color: #8b5cf6;
}

.queue-controls {
    display: flex;
    gap: 10px;
}

/* Queue Panel */
.queue-panel {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    margin-top: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.queue-header {
    padding: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.queue-content {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
}

.queue-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-bottom: 8px;
    border-left: 3px solid transparent;
}

.queue-item.pending {
    border-left-color: #f59e0b;
}

.queue-item.sent {
    border-left-color: #10b981;
}

.queue-item.failed {
    border-left-color: #ef4444;
}

.queue-number {
    background: #8b5cf6;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.queue-message {
    flex: 1;
    color: #e0e0e0;
    font-size: 14px;
}

.queue-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    text-transform: uppercase;
    font-weight: 500;
}

.queue-status.pending {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.queue-status.sent {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.queue-status.failed {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

/* Settings Sections */
.settings-section {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-section h4 {
    color: #06b6d4;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-label {
    color: #e0e0e0;
    font-weight: 500;
}

.setting-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Sliders */
.slider {
    width: 150px;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.2);
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #8b5cf6;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #8b5cf6;
    cursor: pointer;
    border: none;
}

/* Checkboxes */
.checkbox {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.checkbox input {
    opacity: 0;
    width: 0;
    height: 0;
}

.checkbox-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: 0.3s;
    border-radius: 24px;
}

.checkbox-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

.checkbox input:checked + .checkbox-slider {
    background-color: #8b5cf6;
}

.checkbox input:checked + .checkbox-slider:before {
    transform: translateX(26px);
}

/* Keybind Input */
.keybind-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 8px 12px;
    color: #ffffff;
    font-size: 12px;
    min-width: 80px;
    text-align: center;
    font-family: monospace;
}

/* Wordlist Manager */
.wordlist-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.wordlist-input-section,
.wordlist-preview-section {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.wordlist-input-section h4,
.wordlist-preview-section h4 {
    color: #06b6d4;
    margin-bottom: 15px;
}

.word-input-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.word-input-container input {
    flex: 1;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: #ffffff;
}

.words-list {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    min-height: 200px;
    max-height: 300px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 14px;
    color: #e0e0e0;
    resize: none;
}

.block-rules-container {
    margin-top: 20px;
}

.block-rule-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 8px;
}

.block-rule-text {
    color: #e0e0e0;
    font-size: 14px;
}

.remove-block-rule {
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
}

.remove-block-rule:hover {
    background: #dc2626;
}

/* Gender Options */
.gender-options {
    display: flex;
    gap: 15px;
    margin: 15px 0;
}

.gender-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.gender-option input[type="radio"] {
    accent-color: #8b5cf6;
}

.gender-option label {
    color: #e0e0e0;
    cursor: pointer;
}

/* Autobeef Status */
.autobeef-status {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.running {
    background: #10b981;
    animation: pulse 2s infinite;
}

.status-indicator.stopped {
    background: #6b7280;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    font-weight: 500;
    color: #e0e0e0;
}

.autobeef-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    padding: 15px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #8b5cf6;
}

.stat-label {
    font-size: 0.9rem;
    color: #a0a0a0;
    margin-top: 5px;
}

/* Preview Section */
.preview-container {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    min-height: 100px;
}

.preview-text {
    color: #e0e0e0;
    font-size: 14px;
    line-height: 1.5;
    font-style: italic;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(139, 92, 246, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(139, 92, 246, 0.7);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .tabs {
        flex-direction: column;
    }
    
    .tab-button {
        margin-bottom: 5px;
    }
    
    .wordlist-section {
        grid-template-columns: 1fr;
    }
    
    .autobeef-stats {
        grid-template-columns: 1fr 1fr;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.tab-panel.active {
    animation: fadeIn 0.3s ease-in-out;
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.toast.success {
    background: linear-gradient(45deg, #10b981, #059669);
}

.toast.error {
    background: linear-gradient(45deg, #ef4444, #dc2626);
}

.toast.warning {
    background: linear-gradient(45deg, #f59e0b, #d97706);
}

.toast.info {
    background: linear-gradient(45deg, #06b6d4, #0891b2);
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

/* Context Menu */
.context-menu {
    position: fixed;
    background: rgba(30, 30, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 8px 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.context-menu-item {
    padding: 10px 20px;
    cursor: pointer;
    color: #e0e0e0;
    font-size: 14px;
    transition: background-color 0.2s;
}

.context-menu-item:hover {
    background: rgba(139, 92, 246, 0.2);
    color: #ffffff;
}

/* Rate Limit Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border-radius: 15px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.modal-header {
    text-align: center;
    margin-bottom: 20px;
}

.modal-header h3 {
    color: #f59e0b;
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.modal-body {
    text-align: center;
    color: #e0e0e0;
    line-height: 1.6;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin: 20px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #f59e0b, #d97706);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-10 { margin-top: 10px; }
.mt-15 { margin-top: 15px; }
.mt-20 { margin-top: 20px; }

.mb-10 { margin-bottom: 10px; }
.mb-15 { margin-bottom: 15px; }
.mb-20 { margin-bottom: 20px; }

.hidden { display: none !important; }
.visible { display: block !important; }

.flex { display: flex; }
.flex-center { display: flex; justify-content: center; align-items: center; }
.flex-between { display: flex; justify-content: space-between; align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }

/* Loading Spinner */
.spinner {
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 3px solid #8b5cf6;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}