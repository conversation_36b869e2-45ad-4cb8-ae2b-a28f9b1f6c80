#!/usr/bin/env python3
"""
Test script to verify Discord intents are working correctly
"""

def test_intents():
    """Test that the intents value includes MESSAGE_CONTENT"""
    
    # Current intents values
    GUILDS = 1
    GUILD_MESSAGES = 512
    MESSAGE_CONTENT = 32768
    
    # Calculate expected intents
    expected_intents = GUILDS + GUILD_MESSAGES + MESSAGE_CONTENT
    print(f"Expected intents value: {expected_intents}")
    
    # Check individual flags
    print(f"GUILDS: {GUILDS}")
    print(f"GUILD_MESSAGES: {GUILD_MESSAGES}")
    print(f"MESSAGE_CONTENT: {MESSAGE_CONTENT}")
    
    # Verify the calculation
    print(
        f"Sum: {GUILDS} + {GUILD_MESSAGES} + {MESSAGE_CONTENT} = "
        f"{expected_intents}"
    )
    
    # Check if MESSAGE_CONTENT is included in our intents
    current_intents = 33281
    has_message_content = (
        (current_intents & MESSAGE_CONTENT) == MESSAGE_CONTENT
    )
    has_guild_messages = (current_intents & GUILD_MESSAGES) == GUILD_MESSAGES
    has_guilds = (current_intents & GUILDS) == GUILDS
    
    print(f"\nCurrent intents: {current_intents}")
    print(f"Has GUILDS: {has_guilds}")
    print(f"Has GUILD_MESSAGES: {has_guild_messages}")
    print(f"Has MESSAGE_CONTENT: {has_message_content}")
    
    if has_message_content and has_guild_messages and has_guilds:
        print("\n✅ All required intents are properly configured!")
        return True
    else:
        print("\n❌ Missing required intents!")
        return False

if __name__ == "__main__":
    test_intents()