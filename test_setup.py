#!/usr/bin/env python3
"""
Test script to verify that all dependencies are properly installed
"""

import sys
import importlib

def test_import(module_name):
    """Test if a module can be imported"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {module_name} - OK")
        return True
    except ImportError as e:
        print(f"❌ {module_name} - FAILED: {e}")
        return False

def main():
    print("🔍 Testing Zephyr v6 Setup...")
    print("=" * 40)
    
    # Test Python version
    python_version = sys.version_info
    print(
        f"Python version: {python_version.major}."
        f"{python_version.minor}.{python_version.micro}"
    )
    
    if python_version < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        return False
    else:
        print("✅ Python version - OK")
    
    print("\n📦 Testing Python dependencies...")
    
    # Test required modules
    modules_to_test = [
        'asyncio',
        'json',
        'sys',
        'time',
        'threading',
        'queue',
        'datetime',
        'aiohttp',
        'websockets',
        'logging'
    ]
    
    all_good = True
    for module in modules_to_test:
        if not test_import(module):
            all_good = False
    
    print("\n" + "=" * 40)
    
    if all_good:
        print("🎉 All dependencies are properly installed!")
        print("You can now run: npm start")
    else:
        print("❌ Some dependencies are missing. Please run:")
        print("pip install -r requirements.txt")
    
    return all_good

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")